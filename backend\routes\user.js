const express = require('express');
const AuthUtils = require('../utils/auth');
const ValidationUtils = require('../utils/validation');
const User = require('../models/User');
const PredictionRecord = require('../models/PredictionRecord');
const router = express.Router();

// 获取当前用户信息
router.get('/profile', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await AuthUtils.getUserById(userId);
    const userStats = await User.getUserStats(userId);

    res.json({
      id: user.id,
      nickname: user.get('nickname'),
      avatar: userStats ? userStats.get('avatar') : 'assets/images/default_avatar.png',
      memberLevel: userStats ? userStats.get('memberLevel') : '普通会员',
      predictionCount: userStats ? userStats.get('predictionCount') : 0,
      favoriteCount: userStats ? userStats.get('favoriteCount') : 0,
      consultationCount: userStats ? userStats.get('consultationCount') : 0,
      createdAt: user.get('createdAt'),
      lastActiveAt: user.get('lastActiveAt')
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取用户信息失败'
      }
    });
  }
});

// 更新用户信息
router.put('/profile', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { nickname, avatar } = req.body;
    
    // 验证数据
    if (nickname && (nickname.length < 2 || nickname.length > 20)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '昵称长度必须在2-20个字符之间'
        }
      });
    }

    const user = await AuthUtils.getUserById(userId);
    
    // 更新用户信息
    if (nickname) {
      // 检查昵称是否已被使用
      const existingUser = await User.findByNickname(nickname);
      if (existingUser && existingUser.id !== userId) {
        return res.status(400).json({
          error: {
            code: 400,
            message: '昵称已存在，请选择其他昵称'
          }
        });
      }
      user.set('nickname', nickname);
    }
    
    await user.save();

    // 更新用户统计信息
    if (avatar) {
      const userStats = await User.getUserStats(userId);
      if (userStats) {
        userStats.set('avatar', avatar);
        await userStats.save();
      }
    }

    // 返回更新后的用户信息
    const updatedUserStats = await User.getUserStats(userId);
    res.json({
      id: user.id,
      nickname: user.get('nickname'),
      avatar: updatedUserStats ? updatedUserStats.get('avatar') : 'assets/images/default_avatar.png',
      memberLevel: updatedUserStats ? updatedUserStats.get('memberLevel') : '普通会员',
      predictionCount: updatedUserStats ? updatedUserStats.get('predictionCount') : 0,
      favoriteCount: updatedUserStats ? updatedUserStats.get('favoriteCount') : 0,
      consultationCount: updatedUserStats ? updatedUserStats.get('consultationCount') : 0,
      createdAt: user.get('createdAt'),
      lastActiveAt: user.get('lastActiveAt')
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '更新用户信息失败'
      }
    });
  }
});

// 获取预测历史记录
router.get('/history', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 验证分页参数
    const { error, value } = ValidationUtils.validatePagination(req.query);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }

    const { page, limit, type } = value;
    
    // 获取用户的预测记录
    const result = await PredictionRecord.getUserRecords(userId, page, limit, type);
    
    res.json(result);
  } catch (error) {
    console.error('获取历史记录失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取历史记录失败'
      }
    });
  }
});

// 收藏/取消收藏预测记录
router.post('/history/:recordId/favorite', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const recordId = req.params.recordId;

    if (!recordId) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '记录ID不能为空'
        }
      });
    }

    const result = await PredictionRecord.toggleFavorite(recordId, userId);
    
    // 更新用户收藏数统计
    const increment = result.isFavorite ? 1 : -1;
    await User.updateUserStats(userId, 'favoriteCount', increment);
    
    res.json(result);
  } catch (error) {
    console.error('收藏操作失败:', error);
    if (error.message === '记录不存在') {
      return res.status(404).json({
        error: {
          code: 404,
          message: '记录不存在'
        }
      });
    }
    res.status(500).json({
      error: {
        code: 500,
        message: '收藏操作失败'
      }
    });
  }
});

// 删除预测记录
router.delete('/history/:recordId', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const recordId = req.params.recordId;

    if (!recordId) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '记录ID不能为空'
        }
      });
    }

    // 查找记录
    const query = new AV.Query('PredictionRecord');
    query.equalTo('objectId', recordId);
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    
    const record = await query.first();
    if (!record) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '记录不存在'
        }
      });
    }

    // 删除记录
    await record.destroy();
    
    // 更新用户统计
    await User.updateUserStats(userId, 'predictionCount', -1);
    if (record.get('isFavorite')) {
      await User.updateUserStats(userId, 'favoriteCount', -1);
    }

    res.json({
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除记录失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '删除记录失败'
      }
    });
  }
});

// 获取用户的收藏记录
router.get('/favorites', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 验证分页参数
    const { error, value } = ValidationUtils.validatePagination(req.query);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }

    const { page, limit } = value;
    
    // 获取收藏的记录
    const query = new AV.Query('PredictionRecord');
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    query.equalTo('isFavorite', true);
    query.descending('createdAt');
    query.skip((page - 1) * limit);
    query.limit(limit);
    
    const records = await query.find();
    const count = await query.count();
    
    res.json({
      records: records.map(record => ({
        id: record.id,
        type: record.get('type'),
        title: record.get('title'),
        description: record.get('description'),
        result: record.get('result'),
        isFavorite: record.get('isFavorite'),
        createdAt: record.get('createdAt')
      })),
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(count / limit),
        totalRecords: count
      }
    });
  } catch (error) {
    console.error('获取收藏记录失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取收藏记录失败'
      }
    });
  }
});

// 获取用户统计信息
router.get('/stats', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userStats = await User.getUserStats(userId);
    
    if (!userStats) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '用户统计信息不存在'
        }
      });
    }

    res.json({
      predictionCount: userStats.get('predictionCount'),
      favoriteCount: userStats.get('favoriteCount'),
      consultationCount: userStats.get('consultationCount'),
      memberLevel: userStats.get('memberLevel'),
      avatar: userStats.get('avatar')
    });
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取用户统计失败'
      }
    });
  }
});

module.exports = router; 