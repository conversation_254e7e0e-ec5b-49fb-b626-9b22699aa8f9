const AV = require('leanengine');

// 扩展User类
const User = AV.Object.extend('_User');

// 用户相关的静态方法
User.findByNickname = async (nickname) => {
  const query = new AV.Query(User);
  query.equalTo('nickname', nickname);
  return await query.first();
};

// 创建用户统计信息
User.createUserStats = async (user) => {
  const UserStats = AV.Object.extend('UserStats');
  const stats = new UserStats();
  stats.set('user', user);
  stats.set('predictionCount', 0);
  stats.set('favoriteCount', 0);
  stats.set('consultationCount', 0);
  stats.set('memberLevel', '普通会员');
  stats.set('avatar', 'assets/images/default_avatar.png');
  return await stats.save();
};

// 更新用户统计信息
User.updateUserStats = async (userId, field, increment = 1) => {
  const query = new AV.Query('UserStats');
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  const stats = await query.first();
  
  if (stats) {
    const currentValue = stats.get(field) || 0;
    stats.set(field, currentValue + increment);
    await stats.save();
  }
};

// 获取用户统计信息
User.getUserStats = async (userId) => {
  const query = new AV.Query('UserStats');
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  return await query.first();
};

module.exports = User; 