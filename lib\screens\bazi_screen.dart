import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/prediction_provider.dart';
import '../services/api_service.dart';
import 'ai_chat_screen.dart';

class BaziScreen extends StatefulWidget {
  const BaziScreen({super.key});

  @override
  State<BaziScreen> createState() => _BaziScreenState();
}

class _BaziScreenState extends State<BaziScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isMale = true;
  DateTime _selectedDate = DateTime.now().subtract(Duration(days: 25 * 365)); // 默认25岁
  String _selectedTime = '上午';
  int _selectedHour = 8;
  bool _showBaziResult = false;
  bool _isLoading = false;
  Map<String, dynamic>? _baziResult;
  
  // 日期选择相关
  int _selectedYear = DateTime.now().year - 25; // 默认25岁
  int _selectedMonth = DateTime.now().month;
  int _selectedDay = DateTime.now().day;
  bool _showDatePicker = false; // 控制日期选择器显示

  // 简化的天干地支数据
  final List<String> _tianGan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
  final List<String> _diZhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  // 五行颜色映射
  final Map<String, Color> _wuxingColors = {
    '金': Color(0xFFFFBF24),
    '木': Color(0xFF22C55E),
    '水': Color(0xFF3B82F6),
    '火': Color(0xFFEF4444),
    '土': Color(0xFFA3A3A3),
  };

  // 新增的十二地支时辰数组（用于时辰选择）
  final List<Map<String, dynamic>> _shiChen = [
    {'name': '子时', 'time': '23:00-01:00', 'hour': 0},
    {'name': '丑时', 'time': '01:00-03:00', 'hour': 2},
    {'name': '寅时', 'time': '03:00-05:00', 'hour': 4},
    {'name': '卯时', 'time': '05:00-07:00', 'hour': 6},
    {'name': '辰时', 'time': '07:00-09:00', 'hour': 8},
    {'name': '巳时', 'time': '09:00-11:00', 'hour': 10},
    {'name': '午时', 'time': '11:00-13:00', 'hour': 12},
    {'name': '未时', 'time': '13:00-15:00', 'hour': 14},
    {'name': '申时', 'time': '15:00-17:00', 'hour': 16},
    {'name': '酉时', 'time': '17:00-19:00', 'hour': 18},
    {'name': '戌时', 'time': '19:00-21:00', 'hour': 20},
    {'name': '亥时', 'time': '21:00-23:00', 'hour': 22},
  ];

  int _selectedShiChenIndex = 4; // 默认选择辰时（早上8点）

  @override
  void initState() {
    super.initState();
    // 初始化日期选择器的年月日
    _selectedYear = _selectedDate.year;
    _selectedMonth = _selectedDate.month;
    _selectedDay = _selectedDate.day;
  }

  // 获取指定年月的天数
  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  // 更新选择的日期
  void _updateSelectedDate() {
    // 确保日期有效，特别是在切换月份时
    int daysInMonth = _getDaysInMonth(_selectedYear, _selectedMonth);
    int validDay = _selectedDay > daysInMonth ? daysInMonth : _selectedDay;
    
    setState(() {
      _selectedDate = DateTime(_selectedYear, _selectedMonth, validDay);
      _selectedDay = validDay; // 更新日期，确保有效
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFECFDF5),
          Color(0xFFD1FAE5),
        ],
        child: Column(
          children: [
            const IOSStatusBar(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFF1E293B),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => context.go('/'),
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                  ),
                  const Expanded(
                    child: Text(
                      '八字预测',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const ConnectionStatusWidget(),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 说明区域
                    _buildInstructionCard(),
                    
                    // 生辰信息输入
                    _buildInputForm(),
                    
                    // 八字排盘（条件显示）
                    if (_showBaziResult) _buildBaziChart(),
                    
                    // 解析结果（条件显示）
                    if (_showBaziResult) _buildAnalysisResult(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            const BottomNavigation(currentIndex: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.calendarAlt,
                color: Color(0xFF16A34A),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '生辰八字算命',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructionItem('• 请准确填写您的出生年月日时'),
              _buildInstructionItem('• 系统将自动排出您的八字'),
              _buildInstructionItem('• 分析五行强弱和命理特征'),
              _buildInstructionItem('• 提供AI解读的人生运势和改运建议'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF374151),
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildInputForm() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '生辰信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 20),
            
            // 性别选择
            _buildGenderSelection(),
            
            const SizedBox(height: 20),
            
            // 公历生日
            _buildDateSelection(),
            
            const SizedBox(height: 20),
            
            // 出生时间
            _buildTimeSelection(),
            
            const SizedBox(height: 24),
            
            // 提交按钮
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '性别',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isMale = true),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: _isMale ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.mars,
                        color: _isMale ? Colors.white : const Color(0xFF374151),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '乾命（男）',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _isMale ? Colors.white : const Color(0xFF374151),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isMale = false),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: !_isMale ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.venus,
                        color: !_isMale ? Colors.white : const Color(0xFF374151),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '坤命（女）',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: !_isMale ? Colors.white : const Color(0xFF374151),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '公历生日',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 12),
        
        // 日期展示栏
        GestureDetector(
          onTap: () {
            setState(() {
              _showDatePicker = !_showDatePicker;
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF16A34A), width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.calendarAlt,
                      color: Color(0xFF16A34A),
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      DateFormat('yyyy年MM月dd日').format(_selectedDate),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ],
                ),
                Icon(
                  _showDatePicker ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Color(0xFF16A34A),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
        
        // 可折叠的日期选择器
        if (_showDatePicker)
          Container(
            margin: const EdgeInsets.only(top: 12),
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF16A34A), width: 1),
            ),
            child: Row(
              children: [
                // 年份选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFECFDF5),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(15),
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            '年',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF16A34A),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedYear - 1900
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedYear = 1900 + index;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            DateTime.now().year - 1900 + 1,
                            (index) => Center(
                              child: Text(
                                '${1900 + index}年',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 月份选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        color: const Color(0xFFECFDF5),
                        child: const Center(
                          child: Text(
                            '月',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF16A34A),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedMonth - 1
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedMonth = index + 1;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            12,
                            (index) => Center(
                              child: Text(
                                '${index + 1}月',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 日期选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFECFDF5),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(15),
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            '日',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF16A34A),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedDay - 1
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedDay = index + 1;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            _getDaysInMonth(_selectedYear, _selectedMonth),
                            (index) => Center(
                              child: Text(
                                '${index + 1}日',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

        // 选择确认按钮
        if (_showDatePicker)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showDatePicker = false;
                    });
                  },
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFF16A34A),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '出生时辰',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 12),
        
        // 新的时辰选择器
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF16A34A), width: 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: CupertinoPicker(
            itemExtent: 40,
            scrollController: FixedExtentScrollController(initialItem: _selectedShiChenIndex),
            onSelectedItemChanged: (int index) {
              setState(() {
                _selectedShiChenIndex = index;
                _selectedHour = _shiChen[index]['hour'];
                // 根据时辰设置上午/下午
                _selectedTime = (_selectedHour >= 12 && _selectedHour < 24) ? '下午' : '上午';
              });
            },
            children: _shiChen.map((item) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item['name'],
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF16A34A),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      item['time'],
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        
        // 显示已选时辰的详细信息
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: const Color(0xFFECFDF5),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: const Color(0xFF16A34A), width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    FontAwesomeIcons.clock,
                    color: Color(0xFF16A34A),
                    size: 14,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '已选：${_shiChen[_selectedShiChenIndex]['name']} (${_shiChen[_selectedShiChenIndex]['time']})',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF16A34A),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : () async {
          if (_formKey.currentState!.validate()) {
            setState(() {
              _isLoading = true;
            });
            
            try {
              // 使用选择的时辰对应的小时
              int hour24 = _shiChen[_selectedShiChenIndex]['hour'];
              
              // 转换回上午/下午和12小时制 - 确保与后端API完全兼容
              String amPm = (hour24 >= 12 && hour24 < 24) ? '下午' : '上午';
              int hour12;
              if (hour24 == 0 || hour24 == 12) {
                hour12 = 12;
              } else {
                hour12 = hour24 % 12;
                if (hour12 == 0) hour12 = 12;
              }
              
              // 生成标准格式的时间字符串
              String birthTime = '${hour24.toString().padLeft(2, '0')}:00';
              
              print('🕐 时辰选择: ${_shiChen[_selectedShiChenIndex]['name']} -> $birthTime (${amPm} ${hour12}点)');
              
              // 构建输入数据 - 只保留原来后端接受的字段，完全符合原格式
              final inputData = {
                'gender': _isMale ? 'male' : 'female',
                'birthDate': DateFormat('yyyy-MM-dd').format(_selectedDate),
                'birthTime': birthTime,
                'inputData': {
                  'selectedTime': amPm,      // 原来的格式：上午/下午
                  'selectedHour': hour12,    // 原来的格式：12小时制
                },
              };
              
              print('🔮 开始八字预测...');
              print('📅 出生日期: ${DateFormat('yyyy-MM-dd').format(_selectedDate)}');
              print('🕐 出生时间: $birthTime');
              print('⚥ 性别: ${_isMale ? 'male' : 'female'}');
              print('📦 完整输入数据: $inputData');
              
              // 直接调用API服务
              final apiService = ApiService();
              final result = await apiService.createBaziPrediction(inputData);
              
              print('✅ 八字预测完成');
              print('📊 预测结果: $result');
              
              setState(() {
                _showBaziResult = true;
                _baziResult = result;
                _isLoading = false;
              });
              
               // 如果是登录用户，同时更新Provider
               final predictionProvider = Provider.of<PredictionProvider>(context, listen: false);
               if (result['record'] != null) {
                 // 登录用户有记录，更新provider
                 await predictionProvider.loadRecords();
               }
              
            } catch (e) {
              setState(() {
                _isLoading = false;
              });
              print('❌ 八字预测失败: $e');
              // 显示错误信息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('八字预测失败：$e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF16A34A),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading 
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'AI解读中...',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  FontAwesomeIcons.robot,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Text(
                  '开始AI解读',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
      ),
    );
  }

  Widget _buildBaziChart() {
    if (_baziResult == null) return Container();
    
    final result = _baziResult!['result'] ?? {};
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '您的八字',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 20),
          
          // 八字排盘 - 使用真实数据
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildBaziColumn('年柱', result['yearColumn'] ?? '甲子'),
              _buildBaziColumn('月柱', result['monthColumn'] ?? '乙丑'),
              _buildBaziColumn('日柱', result['dayColumn'] ?? '丙寅'),
              _buildBaziColumn('时柱', result['hourColumn'] ?? '丁卯'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBaziColumn(String title, String ganZhi) {
    // 从干支组合中提取天干和地支
    final tianGan = ganZhi.isNotEmpty ? ganZhi[0] : '甲';
    final diZhi = ganZhi.length > 1 ? ganZhi[1] : '子';
    
    // 根据天干地支查找五行
    final tianWuxing = _getWuxingByGan(tianGan);
    final diWuxing = _getWuxingByZhi(diZhi);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF16A34A), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6B7280),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            tianGan,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: _wuxingColors[tianWuxing] ?? Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            diZhi,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: _wuxingColors[diWuxing] ?? Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '$tianWuxing$diWuxing',
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF9CA3AF),
            ),
          ),
        ],
      ),
    );
  }

  // 根据天干获取五行
  String _getWuxingByGan(String gan) {
    const ganWuxing = {
      '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土',
      '己': '土', '庚': '金', '辛': '金', '壬': '水', '癸': '水'
    };
    return ganWuxing[gan] ?? '土';
  }

  // 根据地支获取五行
  String _getWuxingByZhi(String zhi) {
    const zhiWuxing = {
      '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
      '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
    };
    return zhiWuxing[zhi] ?? '土';
  }

  // 构建AI深度咨询按钮
  Widget _buildDeepConsultationButton() {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          if (_baziResult == null) return;
          
          try {
            final result = _baziResult!['result'];
            
            // 使用时辰对应的小时数
            int hour24 = _shiChen[_selectedShiChenIndex]['hour'];
            
            // 转换回上午/下午和12小时制
            String amPm = (hour24 >= 12 && hour24 < 24) ? '下午' : '上午';
            int hour12;
            if (hour24 == 0 || hour24 == 12) {
              hour12 = 12;
            } else {
              hour12 = hour24 % 12;
              if (hour12 == 0) hour12 = 12;
            }
            
            String birthTime = '${hour24.toString().padLeft(2, '0')}:00';
            
            // 构建八字上下文信息 - 确保格式统一
            final baziContext = {
              'gender': _isMale ? 'male' : 'female',
              'birthDate': DateFormat('yyyy-MM-dd').format(_selectedDate),
              'birthTime': birthTime,
              'timeInfo': {
                'selectedTime': amPm,
                'selectedHour': hour12,
              },
              'bazi': {
                'year': result['yearColumn'] ?? '甲子',
                'month': result['monthColumn'] ?? '乙丑',
                'day': result['dayColumn'] ?? '丙寅',
                'hour': result['hourColumn'] ?? '丁卯',
              },
              'wuxingCount': result['wuxingCount'] ?? {},
              'analysis': result['analysis'] ?? result['aiAnalysis'] ?? '',
            };
            
            print('🔮 开始八字深度咨询...');
            print('📋 八字上下文: $baziContext');
            
            // 跳转到AI咨询页面，并传递八字上下文
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AiChatScreen(
                  baziContext: baziContext,
                  fromBazi: true,
                ),
              ),
            );
          } catch (e) {
            print('❌ 启动深度咨询失败: $e');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('启动深度咨询失败：$e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF3B82F6),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FontAwesomeIcons.comments,
              color: Colors.white,
              size: 18,
            ),
            const SizedBox(width: 10),
            const Text(
              'AI深度咨询',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 5),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Text(
                '深入解答',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisResult() {
    if (_baziResult == null) return Container();
    
    final result = _baziResult!['result'] ?? {};
    final analysis = result['analysis'] ?? result['aiAnalysis'] ?? '解读信息暂无';
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFF0FDF4),
            const Color(0xFFDCFCE7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF16A34A), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.robot,
                color: Color(0xFF16A34A),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'AI智能解读',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // AI解读内容
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              analysis,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF374151),
                height: 1.6,
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // AI深度咨询按钮
          _buildDeepConsultationButton(),
        ],
      ),
    );
  }


} 