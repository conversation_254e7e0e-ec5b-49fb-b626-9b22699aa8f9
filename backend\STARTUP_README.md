# 后端服务启动脚本说明

## 📋 可用脚本

本后端目录提供了多个启动脚本，适用于不同的使用场景：

### 🚀 启动脚本列表

| 脚本名称 | 功能描述 | 使用场景 | 特点 |
|---------|---------|---------|------|
| `start.bat` | 完整启动脚本 | **推荐日常使用** | 全面检查、详细提示 |
| `start_dev.bat` | 开发模式启动 | 开发调试 | 自动重载、详细日志 |
| `quick_start.bat` | 快速启动 | 快速测试 | 简洁快速、最少提示 |

## 🛠️ 使用方法

### 1. 完整启动（推荐）

```batch
# 双击运行或命令行执行
start.bat
```

**功能特点：**
- ✅ 完整的环境检查
- ✅ 自动安装/更新依赖
- ✅ 环境配置检查
- ✅ 详细的状态提示
- ✅ 错误处理和建议

### 2. 开发模式启动

```batch
# 开发时使用
start_dev.bat
```

**功能特点：**
- ✅ 使用nodemon自动重载
- ✅ 详细的开发日志
- ✅ 代码变更自动重启
- ✅ 适合开发调试

### 3. 快速启动

```batch
# 快速测试使用
quick_start.bat
```

**功能特点：**
- ✅ 最少检查步骤
- ✅ 快速启动服务
- ✅ 简洁输出信息
- ✅ 适合快速测试

## 📊 服务信息

### 默认配置
- **端口**: 3001
- **环境**: Development
- **访问地址**: http://localhost:3001
- **健康检查**: http://localhost:3001/health
- **API基础路径**: http://localhost:3001/api/v1/

### API端点
- `GET /health` - 健康检查
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/user/profile` - 用户信息
- `POST /api/v1/predictions/liuyao` - 六爻预测
- `POST /api/v1/predictions/bazi` - 八字预测
- `GET /api/v1/constellations/fortune` - 星座运势
- `POST /api/v1/chat/send` - AI聊天

## ⚠️ 注意事项

### 环境要求
- Node.js 18+ 
- npm 或 yarn
- Windows 10/11

### 首次运行
1. 确保已安装Node.js
2. 双击运行 `start.bat`
3. 等待依赖安装完成
4. 服务启动后访问 http://localhost:3001/health

### 环境配置
- 复制 `env.example` 为 `.env`
- 根据需要修改 `.env` 中的配置
- 重新启动服务使配置生效

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
**错误信息**: `Error: listen EADDRINUSE: address already in use :::3001`
**解决方案**:
```batch
# 查找占用端口的进程
netstat -ano | findstr :3001
# 结束进程（替换PID）
taskkill /F /PID <PID>
```

#### 2. 依赖安装失败
**错误信息**: `npm ERR! code ENOTFOUND`
**解决方案**:
```batch
# 清理npm缓存
npm cache clean --force
# 使用淘宝镜像
npm install --registry https://registry.npmmirror.com
```

#### 3. Node.js版本过低
**错误信息**: `Error: Node.js version not supported`
**解决方案**:
- 升级Node.js到18+版本
- 访问 https://nodejs.org/ 下载最新版本

#### 4. 权限问题
**错误信息**: `Error: EACCES: permission denied`
**解决方案**:
- 以管理员身份运行命令提示符
- 或者使用 `npm config set prefix` 设置全局目录

## 📝 开发建议

### 开发流程
1. 使用 `start_dev.bat` 启动开发服务器
2. 修改代码后自动重载
3. 使用 `start.bat` 进行完整测试
4. 部署前使用生产环境配置

### 日志查看
- 服务启动日志会显示在控制台
- 错误日志保存在 `logs/` 目录（如果配置）
- 使用开发模式可以看到详细的调试信息

### 性能优化
- 首次启动需要安装依赖，较慢是正常的
- 后续启动会更快
- 使用 `quick_start.bat` 可以跳过部分检查

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查 `.env` 配置是否正确
3. 确认Node.js版本是否符合要求
4. 检查网络连接和防火墙设置

---

**版本**: 1.0.0  
**更新日期**: 2024-01-01  
**维护者**: 银发-满天神佛开发团队 