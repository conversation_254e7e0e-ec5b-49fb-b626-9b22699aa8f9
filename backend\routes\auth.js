const express = require('express');
const AV = require('leanengine');
const AuthUtils = require('../utils/auth');
const ValidationUtils = require('../utils/validation');
const User = require('../models/User');
const router = express.Router();

// 用户注册
router.post('/register', async (req, res) => {
  try {
    // 验证请求数据
    const { error, value } = ValidationUtils.validateRegister(req.body);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }

    const { nickname, password } = value;

    // 检查用户名是否已存在
    const existingUser = await User.findByNickname(nickname);
    if (existingUser) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '昵称已存在，请选择其他昵称'
        }
      });
    }

    // 创建新用户
    const user = new AV.User();
    user.set('username', nickname);
    user.set('nickname', nickname);
    user.set('password', password);

    const savedUser = await user.signUp();

    // 创建用户统计信息
    await User.createUserStats(savedUser);

    // 生成JWT Token
    const token = AuthUtils.generateToken(savedUser.id, nickname);

    // 返回用户信息和token
    res.status(201).json({
      user: {
        id: savedUser.id,
        nickname: savedUser.get('nickname'),
        avatar: 'assets/images/default_avatar.png',
        memberLevel: '普通会员',
        predictionCount: 0,
        favoriteCount: 0,
        consultationCount: 0,
        createdAt: savedUser.get('createdAt'),
        lastActiveAt: savedUser.get('createdAt')
      },
      token
    });
  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '注册失败，请稍后再试'
      }
    });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    // 验证请求数据
    const { error, value } = ValidationUtils.validateLogin(req.body);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }

    const { nickname, password } = value;

    // 使用LeanCloud内置的登录方法
    const user = await AV.User.logIn(nickname, password);

    // 获取用户统计信息
    const userStats = await User.getUserStats(user.id);

    // 更新最后活跃时间
    user.set('lastActiveAt', new Date());
    await user.save();

    // 生成JWT Token
    const token = AuthUtils.generateToken(user.id, nickname);

    // 返回用户信息和token
    res.json({
      user: {
        id: user.id,
        nickname: user.get('nickname'),
        avatar: userStats ? userStats.get('avatar') : 'assets/images/default_avatar.png',
        memberLevel: userStats ? userStats.get('memberLevel') : '普通会员',
        predictionCount: userStats ? userStats.get('predictionCount') : 0,
        favoriteCount: userStats ? userStats.get('favoriteCount') : 0,
        consultationCount: userStats ? userStats.get('consultationCount') : 0,
        createdAt: user.get('createdAt'),
        lastActiveAt: user.get('lastActiveAt')
      },
      token
    });
  } catch (error) {
    console.error('登录失败:', error);
    
    // 处理不同的错误类型
    if (error.code === 210) {
      return res.status(401).json({
        error: {
          code: 401,
          message: '用户名或密码错误'
        }
      });
    } else if (error.code === 219) {
      return res.status(401).json({
        error: {
          code: 401,
          message: '登录失败次数过多，请稍后再试'
        }
      });
    } else {
      return res.status(500).json({
        error: {
          code: 500,
          message: '登录失败，请稍后再试'
        }
      });
    }
  }
});

// 验证token（用于前端检查token有效性）
router.get('/verify', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await AuthUtils.getUserById(userId);
    const userStats = await User.getUserStats(userId);

    res.json({
      valid: true,
      user: {
        id: user.id,
        nickname: user.get('nickname'),
        avatar: userStats ? userStats.get('avatar') : 'assets/images/default_avatar.png',
        memberLevel: userStats ? userStats.get('memberLevel') : '普通会员',
        predictionCount: userStats ? userStats.get('predictionCount') : 0,
        favoriteCount: userStats ? userStats.get('favoriteCount') : 0,
        consultationCount: userStats ? userStats.get('consultationCount') : 0,
        createdAt: user.get('createdAt'),
        lastActiveAt: user.get('lastActiveAt')
      }
    });
  } catch (error) {
    console.error('Token验证失败:', error);
    res.status(401).json({
      error: {
        code: 401,
        message: 'Token无效或已过期'
      }
    });
  }
});

// 刷新token
router.post('/refresh', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const nickname = req.user.nickname;
    
    // 生成新的token
    const newToken = AuthUtils.generateToken(userId, nickname);
    
    res.json({
      token: newToken
    });
  } catch (error) {
    console.error('Token刷新失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: 'Token刷新失败'
      }
    });
  }
});

// 注销登录
router.post('/logout', AuthUtils.authenticate, async (req, res) => {
  try {
    // 在实际应用中，可以将token加入黑名单
    // 这里简单返回成功状态
    res.json({
      message: '注销成功'
    });
  } catch (error) {
    console.error('注销失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '注销失败'
      }
    });
  }
});

module.exports = router; 