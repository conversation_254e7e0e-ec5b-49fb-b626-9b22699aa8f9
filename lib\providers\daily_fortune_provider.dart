import 'package:flutter/foundation.dart';
import 'package:lunar/lunar.dart';
import '../models/daily_fortune.dart';
import '../services/api_service.dart';

class DailyFortuneProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  DailyFortune? _dailyFortune;
  bool _isLoading = false;
  String? _error;
  DateTime? _lastFetchTime;

  DailyFortune? get dailyFortune => _dailyFortune;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastFetchTime => _lastFetchTime;

  // 获取今日运势
  Future<void> fetchDailyFortune({bool forceRefresh = false}) async {
    // 如果不是强制刷新，且已有今日运势数据，且是今天的数据，则不重复获取
    if (!forceRefresh && 
        _dailyFortune != null && 
        _dailyFortune!.isToday() &&
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!).inMinutes < 30) {
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final fortuneData = await _apiService.getDailyFortune();
      _dailyFortune = DailyFortune.fromJson(fortuneData);
      _lastFetchTime = DateTime.now();
      _error = null;
      
      if (kDebugMode) {
        print('今日运势获取成功: ${_dailyFortune?.date}');
        print('运势内容: ${_dailyFortune?.content}');
      }
    } catch (e) {
      _error = e.toString();
      
      if (kDebugMode) {
        print('获取今日运势失败: $e');
      }
      
      // 如果获取失败但之前有数据，保留之前的数据
      if (_dailyFortune == null) {
        // 只有在完全没有数据时才创建默认运势
        _dailyFortune = _createDefaultFortune();
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 刷新今日运势（管理员功能）
  Future<void> refreshDailyFortune() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final fortuneData = await _apiService.refreshDailyFortune();
      _dailyFortune = DailyFortune.fromJson(fortuneData);
      _lastFetchTime = DateTime.now();
      _error = null;
      
      if (kDebugMode) {
        print('今日运势刷新成功: ${_dailyFortune?.date}');
      }
    } catch (e) {
      _error = e.toString();
      
      if (kDebugMode) {
        print('刷新今日运势失败: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 创建默认运势数据
  DailyFortune _createDefaultFortune() {
    final today = DateTime.now();
    final dateString = '${today.year}年${today.month}月${today.day}日';
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    final weekday = weekdays[today.weekday - 1];
    
    return DailyFortune(
      date: '$dateString $weekday',
      lunarDate: _getLunarDate(today),
      content: '''**今日运势概览**

**今日宜：**
• 祈福求财，拜访贵人
• 学习充电，提升技能
• 整理环境，净化心灵
• 与朋友聚会，增进感情

**今日忌：**
• 冲动投资，盲目决策
• 争执口角，意气用事
• 熬夜劳累，损伤精神
• 签订重要合同

**整体运势：** 今日运势平稳向上，适合稳中求进，把握机遇的同时要保持理性。

**幸运色彩：** 蓝色、白色
**幸运数字：** 3、7、9

**温馨提醒：**
💰 **财运：** 财运平稳，适合理财规划
🚀 **事业：** 工作顺利，团队合作佳
💕 **感情：** 感情和谐，沟通顺畅
🌱 **健康：** 注意休息，保持心情愉悦''',
      timestamp: today.toIso8601String(),
      isAIGenerated: false,
    );
  }

  // 精确的农历转换
  String _getLunarDate(DateTime date) {
    try {
      // 使用lunar库进行精确的农历转换
      final lunar = Lunar.fromDate(date);
      return lunar.toString();
    } catch (e) {
      // 如果转换失败，使用备用方法
      if (kDebugMode) {
        print('农历转换失败: $e');
      }
      return _getFallbackLunarDate(date);
    }
  }

  // 备用农历转换方法
  String _getFallbackLunarDate(DateTime date) {
    const lunarMonths = [
      '正月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '冬月', '腊月'
    ];
    
    const lunarDays = [
      '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];
    
    final month = date.month;
    final day = date.day;
    
    final lunarMonth = lunarMonths[(month - 1) % 12];
    final lunarDay = lunarDays[day.clamp(1, 30)];
    
    return '农历$lunarMonth$lunarDay';
  }

  // 清除错误状态
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // 手动设置运势数据（用于测试）
  void setDailyFortune(DailyFortune fortune) {
    _dailyFortune = fortune;
    _lastFetchTime = DateTime.now();
    _error = null;
    notifyListeners();
  }

  // 重置状态
  void reset() {
    _dailyFortune = null;
    _isLoading = false;
    _error = null;
    _lastFetchTime = null;
    notifyListeners();
  }
} 