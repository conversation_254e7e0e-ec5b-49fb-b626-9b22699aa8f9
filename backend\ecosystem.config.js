module.exports = {
  apps: [
    {
      name: 'yinfa-shenfo-backend',
      script: 'server.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // 进程管理
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 5,
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 监控配置
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      
      // 自动重启
      autorestart: true,
      
      // 其他配置
      node_args: '--max-old-space-size=4096'
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/yinfa-shenfo-backend.git',
      path: '/var/www/yinfa-shenfo-backend',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production'
    }
  }
}; 