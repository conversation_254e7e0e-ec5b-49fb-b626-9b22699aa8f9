# 银发-满天神佛 后端服务

基于 LeanCloud 开发的命理预测应用后端服务，集成火山方舟深度求索 AI 模型。

## 功能特性

### 核心功能
- 🔐 用户认证系统（注册、登录、JWT认证）
- 👤 用户信息管理
- 🔮 多种预测服务：
  - 八字预测
  - 六爻预测
  - 紫微斗数
  - 组合预测
- ⭐ 星座运势查询
- 🤖 AI智能咨询（集成火山方舟深度求索模型）
- 📱 完整的RESTful API

### 技术架构
- **后端框架**: Node.js + Express.js
- **数据库**: LeanCloud 数据存储
- **认证**: JWT Token
- **AI服务**: 火山方舟深度求索模型
- **日志**: Winston
- **数据验证**: Joi
- **安全**: Helmet + CORS + Rate Limiting

## 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- LeanCloud 账号
- 火山方舟 API 密钥

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd backend
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env
```

### 4. 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

### 5. 验证服务
```bash
curl http://localhost:3000/health
```

## API 文档

### 基础信息
- **Base URL**: `/api/v1`
- **认证方式**: Bearer Token
- **数据格式**: JSON

### 主要接口

#### 认证接口
- `POST /auth/register` - 用户注册
- `POST /auth/login` - 用户登录
- `GET /auth/verify` - 验证Token
- `POST /auth/refresh` - 刷新Token
- `POST /auth/logout` - 注销登录

#### 用户接口
- `GET /user/profile` - 获取用户信息
- `PUT /user/profile` - 更新用户信息
- `GET /user/history` - 获取预测历史
- `POST /user/history/:recordId/favorite` - 收藏/取消收藏
- `GET /user/favorites` - 获取收藏记录
- `GET /user/stats` - 获取用户统计

#### 预测接口
- `POST /predictions/bazi` - 八字预测
- `POST /predictions/liuyao` - 六爻预测
- `POST /predictions/ziwei` - 紫微斗数预测
- `POST /predictions/combo` - 组合预测
- `GET /predictions/:recordId` - 获取预测详情
- `GET /predictions/stats/summary` - 获取预测统计

#### 星座运势接口
- `GET /constellations/today` - 今日运势
- `GET /constellations/:type` - 指定星座运势
- `GET /constellations/:type/history` - 星座运势历史
- `POST /constellations/detect` - 根据生日获取星座
- `POST /constellations/compatibility` - 星座配对

#### AI咨询接口
- `GET /chat/history` - 获取聊天记录
- `POST /chat/message` - 发送消息
- `DELETE /chat/history` - 清空聊天记录
- `GET /chat/quick-questions` - 获取快捷问题
- `GET /chat/status` - 获取AI服务状态
- `GET /chat/stats` - 获取聊天统计

### 星座运势AI更新

系统会每天凌晨0点自动从火山方舟AI模型获取最新的星座运势数据并更新数据库。每个模块的内容（健康运势、财运分析等）字数严格控制在30-45字之间，确保内容简洁而有效。

#### 手动更新星座运势

可以使用以下脚本手动更新星座运势数据：

```bash
# 更新所有星座运势
node backend/scripts/update_constellation_fortunes.js

# 强制更新所有星座运势（即使今日已更新过）
node backend/scripts/update_constellation_fortunes.js --force

# 更新特定星座运势
node backend/scripts/update_constellation_fortunes.js aries
```

#### AI生成的内容字段

- `summary`: 总体运势概述（30-45字）
- `loveAdvice`: 爱情运势分析（30-45字）
- `moneyAdvice`: 财运分析（30-45字）
- `careerAdvice`: 事业运势分析（30-45字）
- `healthAdvice`: 健康运势分析（30-45字）
- `luckyColor`: 幸运颜色
- `luckyNumber`: 幸运数字
- `overallRating`: 总体评分（1-5分）
- `loveRating`: 爱情评分（1-5分）
- `moneyRating`: 财运评分（1-5分）
- `careerRating`: 事业评分（1-5分）
- `healthRating`: 健康评分（1-5分）

## 数据库设计

### 主要数据表

#### User (用户表)
- 基于 LeanCloud 内置 User 类
- 扩展字段：nickname（昵称）、lastActiveAt（最后活跃时间）

#### UserStats (用户统计表)
- user: 关联用户
- predictionCount: 预测次数
- favoriteCount: 收藏数量
- consultationCount: 咨询次数
- memberLevel: 会员等级
- avatar: 头像URL

#### PredictionRecord (预测记录表)
- user: 关联用户
- type: 预测类型
- title: 预测标题
- description: 预测描述
- result: 预测结果
- isFavorite: 是否收藏

#### ChatMessage (聊天消息表)
- user: 关联用户
- type: 消息类型（user/ai）
- content: 消息内容
- rating: 评分（仅AI消息）
- feedback: 反馈（仅AI消息）

#### ConstellationFortune (星座运势表)
- type: 星座类型
- period: 时间周期
- date: 日期
- overallRating: 整体评分
- loveRating: 爱情评分
- moneyRating: 财运评分
- careerRating: 事业评分
- healthRating: 健康评分
- summary: 运势概要
- luckyColor: 幸运颜色
- luckyNumber: 幸运数字

## 部署指南

### LeanCloud 部署

1. **安装 LeanCloud CLI**
```bash
npm install -g leancloud-cli
```

2. **登录 LeanCloud**
```bash
lean login
```

3. **初始化项目**
```bash
lean init
```

4. **部署应用**
```bash
lean deploy
```

### Docker 部署

1. **构建镜像**
```bash
docker build -t yinfa-shenfo-backend .
```

2. **运行容器**
```bash
docker run -p 3000:3000 -d yinfa-shenfo-backend
```

### 服务器部署

1. **使用 PM2 进程管理**
```bash
npm install -g pm2
pm2 start ecosystem.config.js
```

2. **使用 Nginx 反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控与维护

### 日志管理
- 使用 Winston 进行日志记录
- 日志文件位置：`./logs/`
- 日志级别：error, warn, info, debug

### 错误监控
- 集成 Sentry 进行错误监控
- 实时错误报告和性能监控

### 性能监控
- 使用 PM2 监控进程状态
- 内存使用情况监控
- API 响应时间监控

## 开发指南

### 目录结构
```
backend/
├── models/              # 数据模型
├── routes/              # 路由控制器
├── services/            # 业务逻辑服务
├── utils/               # 工具函数
├── .leancloud/          # LeanCloud配置
├── server.js            # 服务器入口
├── package.json         # 依赖配置
└── README.md           # 项目说明
```

### 开发规范
1. 使用 ESLint 进行代码检查
2. 遵循 RESTful API 设计规范
3. 统一的错误处理机制
4. 完善的输入验证
5. 适当的日志记录

### 测试
```bash
# 运行测试
npm test

# 测试覆盖率
npm run coverage
```

## 配置说明

### LeanCloud 配置
- App ID: 应用标识
- App Key: 应用密钥
- Master Key: 主密钥（用于服务端操作）
- Server URL: 服务器地址

### 火山方舟 AI 配置
- API URL: https://ark.cn-beijing.volces.com/api/v3
- API Key: 访问密钥
- Model Name: deepseek-v3-250324

### JWT 配置
- Secret: 签名密钥
- Expires In: 过期时间（默认7天）

## 常见问题

### Q: 如何更新 AI 模型？
A: 修改环境变量中的 `VOLCENGINE_MODEL_NAME` 即可。

### Q: 如何增加新的预测类型？
A: 在 `PredictionService` 中添加新的预测方法，并在路由中添加对应接口。

### Q: 如何配置数据库连接？
A: LeanCloud 使用云端数据库，无需额外配置。如需使用其他数据库，请修改相关配置。

### Q: 如何处理并发请求？
A: 使用 PM2 cluster 模式或负载均衡器来处理高并发。

## 许可证

MIT License

## 支持

如有问题，请联系：
- 邮箱: <EMAIL>
- GitHub Issues: [提交问题](https://github.com/your-repo/issues)

---

**银发-满天神佛团队**  
让传统文化与现代科技完美结合 ✨ 