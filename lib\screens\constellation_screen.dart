import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import 'package:provider/provider.dart';
import '../providers/constellation_provider.dart';
import '../widgets/connection_status_widget.dart';
import '../models/constellation_fortune.dart';
import 'package:intl/intl.dart'; // Added for DateFormat

class ConstellationScreen extends StatefulWidget {
  final String? preSelectedConstellation;
  final String? analysisType; // 分析类型：yearly, monthly, daily
  final bool isFromCombo; // 是否来自组合分析页面
  
  const ConstellationScreen({
    super.key, 
    this.preSelectedConstellation,
    this.analysisType,
    this.isFromCombo = false,
  });

  @override
  State<ConstellationScreen> createState() => _ConstellationScreenState();
}

class _ConstellationScreenState extends State<ConstellationScreen>
    with SingleTickerProviderStateMixin {
  int selectedConstellation = 0; // 默认选中白羊座
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isAiLoading = false; // 新增：标记AI请求状态
  bool _showComboAnalysis = false; // 是否显示组合分析结果
  String? _currentAnalysisType; // 当前显示的分析类型

  // 12星座数据
  final List<Map<String, dynamic>> constellations = [
    {
      'name': '白羊座',
      'symbol': '♈',
      'date': '3.21-4.19',
      'element': '火',
      'fortune': {
        'overall': 4,
        'love': 5,
        'money': 3,
        'career': 4,
        'health': 3,
        'overallText': '今日整体运势良好，精力充沛，适合处理重要事务。保持积极心态，会有意想不到的收获。',
        'loveText': '单身者有望遇到心仪对象，已婚者感情甜蜜，是表达爱意的好时机。',
        'moneyText': '财运平稳，适合稳健投资。避免冲动消费，理性理财会有不错收益。',
        'careerText': '工作效率高，容易获得上司认可。把握机会展现才能，有升职加薪的可能。',
        'healthText': '身体状况良好，但要注意劳逸结合。适当运动，保持规律作息。',
        'luckyColor': '红色、橙色',
        'luckyNumber': '3、7、21',
        'luckyDirection': '东南方',
        'luckyFood': '辣椒、胡萝卜'
      }
    },
    {
      'name': '金牛座',
      'symbol': '♉',
      'date': '4.20-5.20',
      'element': '土',
      'fortune': {
        'overall': 3,
        'love': 4,
        'money': 5,
        'career': 3,
        'health': 4,
        'overallText': '今日需要保持稳定心态，不宜做出重大决定。踏实工作，会有稳定的收获。',
        'loveText': '感情关系稳定，适合深入交流。单身者可通过朋友介绍认识新朋友。',
        'moneyText': '财运旺盛，投资理财有不错收益。适合进行长期投资规划。',
        'careerText': '工作进展平稳，需要耐心处理细节。坚持下去会有好结果。',
        'healthText': '身体状况良好，适当放松身心。注意饮食营养均衡。',
        'luckyColor': '绿色、棕色',
        'luckyNumber': '2、6、14',
        'luckyDirection': '正北方',
        'luckyFood': '坚果、蔬菜'
      }
    },
    {
      'name': '双子座',
      'symbol': '♊',
      'date': '5.21-6.21',
      'element': '风',
      'fortune': {
        'overall': 4,
        'love': 3,
        'money': 4,
        'career': 5,
        'health': 3,
        'overallText': '思维活跃，适合学习新知识。与人交流顺畅，容易获得有用信息。',
        'loveText': '感情变化较大，需要加强沟通。保持开放心态，理解对方想法。',
        'moneyText': '财运不错，可以尝试多元化投资。避免过于分散注意力。',
        'careerText': '工作中创意十足，容易得到认可。适合开展新项目。',
        'healthText': '精神状态良好，但要注意休息。避免过度用脑。',
        'luckyColor': '黄色、蓝色',
        'luckyNumber': '5、9、23',
        'luckyDirection': '正东方',
        'luckyFood': '柠檬、蓝莓'
      }
    },
    {
      'name': '巨蟹座',
      'symbol': '♋',
      'date': '6.22-7.22',
      'element': '水',
      'fortune': {
        'overall': 4,
        'love': 5,
        'money': 3,
        'career': 3,
        'health': 4,
        'overallText': '情感丰富，直觉敏锐。适合处理家庭事务，关心身边人。',
        'loveText': '感情运势极佳，适合表达真情。家庭和谐，爱情甜蜜。',
        'moneyText': '财运平稳，适合储蓄。避免情绪化消费，理性理财。',
        'careerText': '工作中需要更多耐心，团队合作很重要。关注细节。',
        'healthText': '身体健康，情绪稳定。适合温和的运动方式。',
        'luckyColor': '银色、白色',
        'luckyNumber': '4、8、16',
        'luckyDirection': '正西方',
        'luckyFood': '海鲜、牛奶'
      }
    },
    {
      'name': '狮子座',
      'symbol': '♌',
      'date': '7.23-8.22',
      'element': '火',
      'fortune': {
        'overall': 5,
        'love': 4,
        'money': 4,
        'career': 5,
        'health': 4,
        'overallText': '自信满满，魅力十足。适合展现才华，容易成为焦点。',
        'loveText': '感情运势良好，魅力提升。适合主动出击，表现自己。',
        'moneyText': '财运稳定，有意外收入。适合投资自己感兴趣的项目。',
        'careerText': '事业运势极佳，容易获得重要机会。展现领导能力。',
        'healthText': '精力充沛，状态良好。适合挑战性的运动。',
        'luckyColor': '金色、橙色',
        'luckyNumber': '1、10、19',
        'luckyDirection': '正南方',
        'luckyFood': '橙子、坚果'
      }
    },
    {
      'name': '处女座',
      'symbol': '♍',
      'date': '8.23-9.22',
      'element': '土',
      'fortune': {
        'overall': 3,
        'love': 3,
        'money': 4,
        'career': 4,
        'health': 5,
        'overallText': '注重细节，做事严谨。适合整理和规划，提高效率。',
        'loveText': '感情需要更多包容，避免过于挑剔。真诚交流很重要。',
        'moneyText': '财运不错，理财规划合理。适合长期投资和储蓄。',
        'careerText': '工作认真负责，容易获得好评。注意工作与生活平衡。',
        'healthText': '健康运势极佳，身体状况良好。保持良好生活习惯。',
        'luckyColor': '深蓝色、灰色',
        'luckyNumber': '6、12、18',
        'luckyDirection': '西南方',
        'luckyFood': '蔬菜、粗粮'
      }
    },
    {
      'name': '天秤座',
      'symbol': '♎',
      'date': '9.23-10.23',
      'element': '风',
      'fortune': {
        'overall': 4,
        'love': 5,
        'money': 3,
        'career': 4,
        'health': 3,
        'overallText': '追求平衡和和谐，人际关系良好。适合合作和协调。',
        'loveText': '感情运势极佳，桃花运旺盛。适合约会和浪漫活动。',
        'moneyText': '财运一般，需要理性消费。避免为了面子而过度花费。',
        'careerText': '工作中需要更多合作，团队精神很重要。保持平衡。',
        'healthText': '注意身体平衡，避免过度劳累。适合瑜伽等平衡运动。',
        'luckyColor': '粉色、淡蓝色',
        'luckyNumber': '7、15、24',
        'luckyDirection': '西北方',
        'luckyFood': '水果、沙拉'
      }
    },
    {
      'name': '天蝎座',
      'symbol': '♏',
      'date': '10.24-11.22',
      'element': '水',
      'fortune': {
        'overall': 4,
        'love': 4,
        'money': 5,
        'career': 4,
        'health': 3,
        'overallText': '直觉敏锐，洞察力强。适合深入研究和分析问题。',
        'loveText': '感情深入发展，需要更多信任。避免过度猜疑。',
        'moneyText': '财运旺盛，投资眼光独到。适合进行深度投资分析。',
        'careerText': '工作中展现专业能力，容易获得重要项目。保持专注。',
        'healthText': '需要注意情绪健康，适当放松。避免过度紧张。',
        'luckyColor': '深红色、黑色',
        'luckyNumber': '8、13、22',
        'luckyDirection': '东北方',
        'luckyFood': '辛辣食物、黑色食品'
      }
    },
    {
      'name': '射手座',
      'symbol': '♐',
      'date': '11.23-12.21',
      'element': '火',
      'fortune': {
        'overall': 5,
        'love': 4,
        'money': 4,
        'career': 5,
        'health': 4,
        'overallText': '充满活力，视野开阔。适合学习新知识和拓展视野。',
        'loveText': '感情轻松愉快，适合户外约会。保持乐观积极的态度。',
        'moneyText': '财运不错，有意外收获。适合投资教育和旅行。',
        'careerText': '事业运势极佳，适合开拓新领域。展现创新能力。',
        'healthText': '精力充沛，适合户外运动。保持积极的生活态度。',
        'luckyColor': '紫色、蓝色',
        'luckyNumber': '9、17、26',
        'luckyDirection': '东南方',
        'luckyFood': '异国料理、水果'
      }
    },
    {
      'name': '摩羯座',
      'symbol': '♑',
      'date': '12.22-1.19',
      'element': '土',
      'fortune': {
        'overall': 3,
        'love': 3,
        'money': 5,
        'career': 5,
        'health': 3,
        'overallText': '踏实稳重，目标明确。适合制定长期计划和执行。',
        'loveText': '感情发展稳定，需要更多耐心。真诚是最好的表达。',
        'moneyText': '财运极佳，投资理财有方。适合长期投资规划。',
        'careerText': '事业运势极佳，容易获得升职机会。坚持不懈很重要。',
        'healthText': '需要注意劳逸结合，避免过度工作。适当放松。',
        'luckyColor': '深绿色、棕色',
        'luckyNumber': '10、20、30',
        'luckyDirection': '正北方',
        'luckyFood': '坚果、根茎类'
      }
    },
    {
      'name': '水瓶座',
      'symbol': '♒',
      'date': '1.20-2.18',
      'element': '风',
      'fortune': {
        'overall': 4,
        'love': 4,
        'money': 3,
        'career': 5,
        'health': 4,
        'overallText': '思维独特，创新能力强。适合参与团队合作和创新项目。',
        'loveText': '感情新颖有趣，可能有意外的邂逅。保持开放心态。',
        'moneyText': '财运一般，需要理性消费。避免投资过于新颖的项目。',
        'careerText': '事业运势极佳，适合展现创新能力。团队合作很重要。',
        'healthText': '身体状况良好，精神状态佳。适合尝试新的运动方式。',
        'luckyColor': '天蓝色、银色',
        'luckyNumber': '11、22、33',
        'luckyDirection': '西南方',
        'luckyFood': '清淡食物、饮品'
      }
    },
    {
      'name': '双鱼座',
      'symbol': '♓',
      'date': '2.19-3.20',
      'element': '水',
      'fortune': {
        'overall': 4,
        'love': 5,
        'money': 3,
        'career': 3,
        'health': 4,
        'overallText': '想象力丰富，感受力强。适合从事创造性工作和艺术活动。',
        'loveText': '感情运势极佳，浪漫指数高。适合表达情感和创造浪漫。',
        'moneyText': '财运一般，需要理性理财。避免感情用事的消费。',
        'careerText': '工作中需要更多实际行动，避免过于理想化。',
        'healthText': '注意情绪健康，保持乐观心态。适合温和的运动。',
        'luckyColor': '海蓝色、紫色',
        'luckyNumber': '12、24、36',
        'luckyDirection': '正西方',
        'luckyFood': '海鲜、流质食物'
      }
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
    
    // 延迟执行，以确保Provider已经初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadConstellations();
      
      // 如果有预选星座，自动选中
      if (widget.preSelectedConstellation != null) {
        _selectConstellationByName(widget.preSelectedConstellation!);
      }
      
      // 如果来自组合分析，并且指定了分析类型
      if (widget.isFromCombo && widget.analysisType != null) {
        setState(() {
          _showComboAnalysis = true;
          _currentAnalysisType = widget.analysisType;
        });
      }
    });
  }

  Future<void> _loadConstellations() async {
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    await constellationProvider.loadConstellations();
  }

  Future<void> _refreshConstellations() async {
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    await constellationProvider.refreshConstellations();
  }
  
  // 新增：请求AI星座运势
  Future<void> _loadAIConstellation(ConstellationType type) async {
    setState(() {
      _isAiLoading = true;
    });
    
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    try {
      await constellationProvider.loadAIConstellationFortune(type);
      // 成功加载AI运势后显示一个简短的通知
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AI星座运势解析完成！'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // 加载失败时显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('AI星座运势解析失败: ${e.toString()}'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAiLoading = false;
        });
      }
    }
  }
  
  // 新增：加载星座详情
  Future<void> _loadConstellationDetail(ConstellationType type) async {
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    await constellationProvider.loadConstellationDetail(type);
  }

  // 根据星座名称选择对应的星座
  void _selectConstellationByName(String constellationName) {
    for (int i = 0; i < constellations.length; i++) {
      if (constellations[i]['name'] == constellationName) {
        setState(() {
          selectedConstellation = i;
        });
        
        // 加载星座详情
        final type = _getConstellationType(i);
        _loadConstellationDetail(type);
        break;
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ConstellationProvider>(
      builder: (context, provider, _) {
        final isLoadingAI = provider.isLoadingAI || _isAiLoading;
        final isLoading = provider.isLoading;
        final isLoadingCombo = provider.isLoadingComboAnalysis;
        
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFEDE9FE), Color(0xFFDDD6FE)],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // iOS状态栏
                  const IOSStatusBar(),
                  // 顶部导航栏
                  CustomNavigationBar(
                    title: _showComboAnalysis ? '星座+紫微分析' : '星座运势',
                    onBackPressed: () {
                      if (_showComboAnalysis) {
                        // 如果正在查看分析结果，返回时切换回普通星座页面
                        setState(() {
                          _showComboAnalysis = false;
                          _currentAnalysisType = null;
                        });
                      } else {
                        context.go('/');
                      }
                    },
                    rightIcon: _showComboAnalysis ? Icons.view_list : Icons.refresh,
                    onRightPressed: () {
                      if (_showComboAnalysis) {
                        // 如果在查看分析结果，右侧按钮显示分析类型选择
                        _showAnalysisTypeSelector();
                      } else {
                        // 普通星座页面下刷新当前星座运势
                        _refreshConstellations();
                      }
                    },
                  ),
                  // 主内容区
                  Expanded(
                    child: Stack(
                      children: [
                        // 主要内容
                        SingleChildScrollView(
                          child: Column(
                            children: [
                              // 如果显示组合分析，则显示分析结果；否则显示正常星座运势
                              if (_showComboAnalysis)
                                _buildComboAnalysisResult(provider)
                              else ...[
                                // 今日概览
                                Container(
                                  margin: const EdgeInsets.all(16),
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      FadeTransition(
                                        opacity: _fadeAnimation,
                                        child: Column(
                                          children: [
                                            const Text(
                                              '今日星座运势',
                                              style: TextStyle(
                                                fontSize: 24,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              '${DateTime.now().year}年${DateTime.now().month}月${DateTime.now().day}日 ${_getWeekday()}',
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.white.withOpacity(0.9),
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            Icon(
                                              Icons.nights_stay,
                                              size: 48,
                                              color: Colors.white.withOpacity(0.75),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // 星座选择
                                Container(
                                  margin: const EdgeInsets.symmetric(horizontal: 16),
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    children: [
                                      const Text(
                                        '选择您的星座',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      GridView.builder(
                                        shrinkWrap: true,
                                        physics: const NeverScrollableScrollPhysics(),
                                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 3,
                                          crossAxisSpacing: 12,
                                          mainAxisSpacing: 12,
                                          childAspectRatio: 1.0,
                                        ),
                                        itemCount: 12,
                                        itemBuilder: (context, index) {
                                          final constellation = constellations[index];
                                          final isSelected = selectedConstellation == index;
                                          final type = _getConstellationType(index);
                                          
                                          return GestureDetector(
                                            onTap: () async {
                                              setState(() {
                                                selectedConstellation = index;
                                              });
                                              
                                              // 点击星座时，先加载缓存的运势数据
                                              await _loadConstellationDetail(type);
                                            },
                                            child: AnimatedContainer(
                                              duration: const Duration(milliseconds: 300),
                                              decoration: BoxDecoration(
                                                color: isSelected 
                                                    ? const Color(0xFFF3E8FF) 
                                                    : Colors.white,
                                                borderRadius: BorderRadius.circular(16),
                                                border: Border.all(
                                                  color: isSelected 
                                                      ? const Color(0xFF8B5CF6) 
                                                      : Colors.transparent,
                                                  width: 2,
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black.withOpacity(0.1),
                                                    blurRadius: 8,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                ],
                                              ),
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Text(
                                                    constellation['symbol'],
                                                    style: const TextStyle(
                                                      fontSize: 24,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    constellation['name'],
                                                    style: const TextStyle(
                                                      fontSize: 14,
                                                      fontWeight: FontWeight.bold,
                                                      color: Colors.black87,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 2),
                                                  Text(
                                                    constellation['date'],
                                                    style: const TextStyle(
                                                      fontSize: 12,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                // 运势详情
                                Container(
                                  margin: const EdgeInsets.all(16),
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: _buildFortuneDetail(provider),
                                ),
                              ],
                              const SizedBox(height: 20),
                            ],
                          ),
                        ),
                        
                        // 加载状态遮罩
                        if (isLoadingAI || isLoading || isLoadingCombo)
                          Container(
                            color: Colors.black.withOpacity(0.3),
                            child: Center(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    isLoadingCombo 
                                      ? '火山方舟AI正在进行星座+紫微组合分析...'
                                      : (isLoadingAI ? '火山方舟AI正在解析星座运势...' : '正在加载星座运势...'),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  // 底部导航
                  const BottomNavigation(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFortuneDetail(ConstellationProvider provider) {
    final constellation = constellations[selectedConstellation];
    final selectedType = _getConstellationType(selectedConstellation);
    
    // 尝试从provider获取实时数据
    final providerData = provider.constellations.isNotEmpty 
        ? provider.constellations.firstWhere(
            (c) => c.type == selectedType,
            orElse: () => ConstellationFortune(
              type: selectedType,
              name: constellation['name'],
              symbol: constellation['symbol'],
              dateRange: constellation['date'],
              overallRating: constellation['fortune']['overall'],
              loveRating: constellation['fortune']['love'],
              moneyRating: constellation['fortune']['money'],
              careerRating: constellation['fortune']['career'],
              healthRating: constellation['fortune']['health'],
              luckyColor: constellation['fortune']['luckyColor'],
              luckyNumber: constellation['fortune']['luckyNumber'],
              luckyDirection: constellation['fortune']['luckyDirection'],
              luckyFood: constellation['fortune']['luckyFood'],
              summary: constellation['fortune']['overallText'],
              loveAdvice: constellation['fortune']['loveText'],
              moneyAdvice: constellation['fortune']['moneyText'],
              careerAdvice: constellation['fortune']['careerText'],
              healthAdvice: constellation['fortune']['healthText'],
              lastUpdated: DateTime.now(),
              isAIGenerated: false,
            ),
          )
        : null;
    
    // 使用provider数据或默认数据
    final fortune = providerData != null 
        ? {
            'overall': providerData.overallRating,
            'love': providerData.loveRating,
            'money': providerData.moneyRating,
            'career': providerData.careerRating,
            'health': providerData.healthRating,
            'overallText': providerData.summary,
            'loveText': providerData.loveAdvice,
            'moneyText': providerData.moneyAdvice,
            'careerText': providerData.careerAdvice,
            'healthText': providerData.healthAdvice,
            'luckyColor': providerData.luckyColor,
            'luckyNumber': providerData.luckyNumber,
            'luckyDirection': providerData.luckyDirection ?? '东方',
            'luckyFood': providerData.luckyFood ?? '水果',
          }
        : constellation['fortune'];
    
    return Column(
      children: [
        // 星座基本信息
        Column(
          children: [
            Text(
              constellation['symbol'],
              style: const TextStyle(fontSize: 60),
            ),
            const SizedBox(height: 12),
            Text(
              '${constellation['name']}运势',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              constellation['date'],
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            
            // AI标志
            if (providerData != null && providerData.isAIGenerated)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFFEDE9FE),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: const Color(0xFFA78BFA)),
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.auto_awesome,
                      size: 16,
                      color: Color(0xFF8B5CF6),
                    ),
                    SizedBox(width: 4),
                    Text(
                      'AI解析',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF8B5CF6),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        const SizedBox(height: 24),
        // 综合运势
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '综合运势',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  _buildStarRating(fortune['overall']),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                fortune['overallText'],
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        // 各项运势
        _buildFortuneItem(
          '爱情运势',
          FontAwesomeIcons.heart,
          Colors.red,
          fortune['love'],
          fortune['loveText'],
          Colors.red.shade50,
        ),
        const SizedBox(height: 12),
        _buildFortuneItem(
          '财运分析',
          FontAwesomeIcons.coins,
          Colors.green,
          fortune['money'],
          fortune['moneyText'],
          Colors.green.shade50,
        ),
        const SizedBox(height: 12),
        _buildFortuneItem(
          '事业运势',
          FontAwesomeIcons.briefcase,
          Colors.blue,
          fortune['career'],
          fortune['careerText'],
          Colors.blue.shade50,
        ),
        const SizedBox(height: 12),
        _buildFortuneItem(
          '健康运势',
          FontAwesomeIcons.heartbeat,
          Colors.orange,
          fortune['health'],
          fortune['healthText'],
          Colors.orange.shade50,
        ),
        const SizedBox(height: 24),
        // 今日建议
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.purple.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.purple.shade200,
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(
                    FontAwesomeIcons.lightbulb,
                    color: Colors.purple,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '今日建议',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildSuggestionItem('幸运颜色', fortune['luckyColor']),
              _buildSuggestionItem('幸运数字', fortune['luckyNumber']),
              _buildSuggestionItem('幸运方位', fortune['luckyDirection']),
              _buildSuggestionItem('开运食物', fortune['luckyFood']),
            ],
          ),
        ),
        const SizedBox(height: 24),
        // 操作按钮
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // 点击按钮手动触发AI解析
                  _loadAIConstellation(_getConstellationType(selectedConstellation));
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'AI深度解析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // 分享运势
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '分享运势',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFortuneItem(String title, IconData icon, Color color, int rating, String text, Color bgColor) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border(
          left: BorderSide(
            color: color,
            width: 4,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              _buildStarRating(rating),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStarRating(int rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating ? Icons.star : Icons.star_border,
          color: Colors.amber,
          size: 20,
        );
      }),
    );
  }

  Widget _buildSuggestionItem(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$title：',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getWeekday() {
    final weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekdays[DateTime.now().weekday - 1];
  }
  
  // 新增：根据索引获取星座类型
  ConstellationType _getConstellationType(int index) {
    switch (index) {
      case 0: return ConstellationType.aries;
      case 1: return ConstellationType.taurus;
      case 2: return ConstellationType.gemini;
      case 3: return ConstellationType.cancer;
      case 4: return ConstellationType.leo;
      case 5: return ConstellationType.virgo;
      case 6: return ConstellationType.libra;
      case 7: return ConstellationType.scorpio;
      case 8: return ConstellationType.sagittarius;
      case 9: return ConstellationType.capricorn;
      case 10: return ConstellationType.aquarius;
      case 11: return ConstellationType.pisces;
      default: return ConstellationType.aries;
    }
  }

  // 构建组合分析结果
  Widget _buildComboAnalysisResult(ConstellationProvider provider) {
    final result = provider.comboAnalysisResult;
    if (result == null) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Text(
            '暂无分析结果，请先进行星座+紫微组合分析',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }
    
    // 获取当前选中的分析类型的数据
    final analysisType = _currentAnalysisType ?? 'yearly'; // 默认显示年运
    final analysisData = provider.getFortuneByType(analysisType);
    
    if (analysisData == null) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Text(
            '暂无分析数据',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }
    
    // 根据分析类型显示不同的标题
    String analysisTitle = '';
    Icon analysisIcon;
    Color headerColor;
    
    switch (analysisType) {
      case 'yearly':
        analysisTitle = '年运分析';
        analysisIcon = const Icon(Icons.calendar_today, color: Colors.white, size: 28);
        headerColor = const Color(0xFF3B82F6);
        break;
      case 'monthly':
        analysisTitle = '月运分析';
        analysisIcon = const Icon(Icons.date_range, color: Colors.white, size: 28);
        headerColor = const Color(0xFFEF4444);
        break;
      case 'daily':
        analysisTitle = '日运分析';
        analysisIcon = const Icon(Icons.today, color: Colors.white, size: 28);
        headerColor = const Color(0xFF10B981);
        break;
      default:
        analysisTitle = '分析结果';
        analysisIcon = const Icon(Icons.insights, color: Colors.white, size: 28);
        headerColor = const Color(0xFF8B5CF6);
    }
    
    // 星座名称和出生日期
    final constellation = result['constellation'] ?? '未知星座';
    final birthDateTime = DateTime.tryParse(result['birthDateTime'] ?? '') ?? DateTime.now();
    
    return Column(
      children: [
        // 头部信息
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [headerColor, headerColor.withOpacity(0.8)],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  analysisIcon,
                  const SizedBox(width: 12),
                  Text(
                    '$constellation $analysisTitle',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                '基于出生日期：${DateFormat('yyyy-MM-dd HH:mm').format(birthDateTime)}',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '本分析由火山方舟AI基于星座与紫微斗数双重分析生成',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // 分析内容
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 概述
              if (analysisData['summary'] != null) ...[
                const Text(
                  '概述',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  analysisData['summary'],
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF4B5563),
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 24),
              ],
              
              // 各方面分析
              if (analysisData['career'] != null) _buildAnalysisSection('事业', analysisData['career'], Icons.work),
              if (analysisData['love'] != null) _buildAnalysisSection('感情', analysisData['love'], Icons.favorite),
              if (analysisData['money'] != null) _buildAnalysisSection('财运', analysisData['money'], Icons.attach_money),
              if (analysisData['health'] != null) _buildAnalysisSection('健康', analysisData['health'], Icons.favorite_border),
              if (analysisData['social'] != null) _buildAnalysisSection('人际', analysisData['social'], Icons.people),
              
              // 如果有幸运数据
              if (analysisData['lucky'] != null) ...[
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFECFDF5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '幸运指引',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF065F46),
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildLuckyItem('幸运色', analysisData['lucky']['color']),
                      _buildLuckyItem('幸运数字', analysisData['lucky']['number']),
                      _buildLuckyItem('幸运方位', analysisData['lucky']['direction']),
                      _buildLuckyItem('建议', analysisData['lucky']['advice']),
                    ],
                  ),
                ),
              ],
              
              // 如果有行动建议
              if (analysisData['advice'] != null) ...[
                const SizedBox(height: 24),
                const Text(
                  '行动建议',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  analysisData['advice'],
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF4B5563),
                    height: 1.5,
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // 切换分析类型按钮
        Container(
          margin: const EdgeInsets.all(16),
          child: ElevatedButton.icon(
            onPressed: _showAnalysisTypeSelector,
            icon: const Icon(Icons.swap_horiz),
            label: const Text('切换分析类型'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF8B5CF6),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
      ],
    );
  }
  
  // 构建分析部分
  Widget _buildAnalysisSection(String title, String content, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        Row(
          children: [
            Icon(icon, color: const Color(0xFF8B5CF6), size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          content,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF4B5563),
            height: 1.5,
          ),
        ),
      ],
    );
  }
  
  // 构建幸运项
  Widget _buildLuckyItem(String title, String? content) {
    if (content == null || content.isEmpty) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF065F46),
              ),
            ),
          ),
          Expanded(
            child: Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF065F46),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // 显示分析类型选择器
  void _showAnalysisTypeSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('选择分析类型', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildAnalysisTypeOption('yearly', '年运分析', '长期运势趋势', Icons.calendar_today, const Color(0xFF3B82F6)),
            const SizedBox(height: 12),
            _buildAnalysisTypeOption('monthly', '月运分析', '近期运势变化', Icons.date_range, const Color(0xFFEF4444)),
            const SizedBox(height: 12),
            _buildAnalysisTypeOption('daily', '日运分析', '今日运势预测', Icons.today, const Color(0xFF10B981)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
  
  // 构建分析类型选项
  Widget _buildAnalysisTypeOption(String type, String title, String description, IconData icon, Color color) {
    final isSelected = _currentAnalysisType == type;
    
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        setState(() {
          _currentAnalysisType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: isSelected ? color : const Color(0xFFE5E7EB)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected ? color.withOpacity(0.8) : const Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected) Icon(Icons.check_circle, color: color, size: 20),
          ],
        ),
      ),
    );
  }
} 