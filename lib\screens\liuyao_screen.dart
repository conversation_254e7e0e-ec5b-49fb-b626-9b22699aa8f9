import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:math' as math;
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/prediction_provider.dart';
import '../providers/chat_provider.dart';

class LiuyaoScreen extends StatefulWidget {
  const LiuyaoScreen({super.key});

  @override
  State<LiuyaoScreen> createState() => _LiuyaoScreenState();
}

class _LiuyaoScreenState extends State<LiuyaoScreen>
    with TickerProviderStateMixin {
  late AnimationController _coinAnimationController;
  late AnimationController _shakeAnimationController;
  
  final TextEditingController _questionController = TextEditingController();
  
  int _currentRound = 1;
  bool _isShaking = false;
  List<bool> _guaLines = []; // true为阳爻，false为阴爻
  bool _showResult = false;
  String _guaName = '';
  String _guaDescription = '';
  String? _currentRecordId; // 当前预测记录ID
  bool _isAutoShaking = false; // 是否正在自动摇卦
  bool _isAnalyzing = false; // 是否正在进行卦象解析
  
  // 默认问题列表
  final List<String> _defaultQuestions = [
    '我近期的事业发展如何？',
    '我这次投资能否成功？',
    '近期是否适合换工作？',
    '我的感情发展如何？',
    '这段关系适合长期发展吗？',
    '最近的健康状况如何？',
    '搬家的时机是否合适？',
    '我的学业进展如何？',
    '近期财运如何？',
    '我的决定是否正确？',
  ];
  
  // 当前显示的默认问题
  String _currentDefaultQuestion = '';

  @override
  void initState() {
    super.initState();
    _coinAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _shakeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    // 初始化随机默认问题
    _refreshDefaultQuestion();
  }
  
  // 更新默认问题
  void _refreshDefaultQuestion() {
    final random = math.Random();
    setState(() {
      _currentDefaultQuestion = _defaultQuestions[random.nextInt(_defaultQuestions.length)];
    });
  }

  // AI解析方法 - 优化版本
  void _goToAIConsult() async {
    if (_guaName.isEmpty || _guaDescription.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先完成摇卦，生成卦象解析后再进行AI咨询'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 构建详细的卦象上下文信息
    final hexagramContext = '''📋 六爻卦象深度解析

🔮 占卜问题: ${_questionController.text.trim().isEmpty ? _currentDefaultQuestion : _questionController.text.trim()}
🎯 卦象名称: $_guaName
⏰ 占卜时间: ${DateTime.now().toString().substring(0, 19)}

📊 卦象详情:
${_guaLines.asMap().entries.map((e) => 
  '第${e.key + 1}爻: ${e.value ? '阳爻 (—)' : '阴爻 (- -)'}'
).join('\n')}

🎭 解析结果:
$_guaDescription

---
作为专业的六爻占卜大师，请基于以上卦象信息为我进行详细的深度解析，并提供以下内容：

1. 卦象的深层含义和象征意义
2. 针对我的问题"${_questionController.text.trim().isEmpty ? _currentDefaultQuestion : _questionController.text.trim()}"的具体解答
3. 事情发展的时间节点预测
4. 具体的行动建议和注意事项
5. 相关的风水调理建议

请用专业而温和的语言，结合传统易学理论，为我提供深入的指导。''';

    try {
      // 跳转到AI聊天页面
      context.go('/ai-chat');
      
      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在为您进行AI深度解析，请稍候...'),
            backgroundColor: Color(0xFF059669),
            duration: Duration(seconds: 3),
          ),
        );
      }
      
      // 延迟一小段时间确保页面加载完成
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 将卦象信息发送到AI聊天（使用流式输出）
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      await chatProvider.sendMessageAndGetReply(hexagramContext);
      
    } catch (e) {
      print('❌ [LiuyaoScreen] AI深度解析失败: $e');
      
      // 如果发送失败，显示错误信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('AI解析请求失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _coinAnimationController.dispose();
    _shakeAnimationController.dispose();
    _questionController.dispose();
    super.dispose();
  }

  // 取消自动摇卦
  void _cancelAutoShaking() {
    if (_isAutoShaking) {
      setState(() {
        _isAutoShaking = false;
        _isShaking = false; // 确保摇卦状态也被重置
      });
      
      // 显示取消提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已取消自动摇卦，可以重新开始'),
          backgroundColor: Color(0xFF6B7280),
          duration: Duration(seconds: 2),
        ),
      );
      
      // 如果用户已经摇了一些卦但还没完成，询问是否要重置
      if (_currentRound > 1 && _currentRound <= 6) {
        Future.delayed(const Duration(milliseconds: 500), () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.question,
                      color: Color(0xFFD97706),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '是否重新开始',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
                content: const Text(
                  '您已停止自动摇卦，是否要重置当前进度并重新开始？',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF374151),
                    height: 1.5,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // 不重置，保留已有进度
                    },
                    child: const Text(
                      '保留进度',
                      style: TextStyle(
                        color: Color(0xFF6B7280),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _resetDivination(); // 完全重置
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD97706),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '重新开始',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        });
      }
    }
  }

  void _shakeCoins() {
    // 检测用户是否输入了问题，如果没有则使用默认问题
    final questionText = _questionController.text.trim();
    if (questionText.isEmpty) {
      // 使用默认问题
      setState(() {
        _questionController.text = _currentDefaultQuestion;
      });
    }
    
    // 防止重复点击 - 只有在自动摇卦中或已完成六次摇卦时才禁止点击
    // 修复：允许用户在取消自动摇卦后重新开始
    if (_isAutoShaking || _currentRound > 6) return;
    
    // 标记开始自动摇卦
    setState(() {
      _isAutoShaking = true;
    });
    
    // 执行单次摇卦的函数
    void performSingleShake() {
      // 如果用户取消了自动摇卦，则停止
      if (!_isAutoShaking) return;
      
      setState(() {
        _isShaking = true;
      });
      
      _shakeAnimationController.forward().then((_) {
        _shakeAnimationController.reverse();
      });
      
      // 模拟摇卦过程
      Future.delayed(const Duration(milliseconds: 1200), () {
        // 再次检查是否已取消自动摇卦
        if (!_isAutoShaking) return;
        
        final random = math.Random();
        final isYang = random.nextBool(); // 简化的阴阳判断
        
        setState(() {
          _guaLines.add(isYang);
          _currentRound++;
          _isShaking = false;
          
          if (_currentRound > 6) {
            _showResult = true;
            _isAutoShaking = false; // 完成摇卦，结束自动模式
            _generateGuaResult();
          } else if (_currentRound <= 6) {
            // 如果还没完成六次摇卦，继续下一次
            Future.delayed(const Duration(milliseconds: 800), () {
              performSingleShake();
            });
          }
        });
      });
    }
    
    // 开始第一次摇卦，后续会自动连续执行
    performSingleShake();
  }

  void _showInputDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                FontAwesomeIcons.exclamationTriangle,
                color: Color(0xFFD97706),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '温馨提示',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          content: const Text(
            '请先在上方输入您要占卜的问题，然后再进行摇卦。\n\n问题要具体明确，比如：\n• 我这次投资能否成功？\n• 近期是否适合换工作？\n• 感情发展如何？',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF374151),
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '我知道了',
                style: TextStyle(
                  color: Color(0xFFD97706),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _generateGuaResult() async {
    try {
      print('🔮 开始生成六爻卦象...');
      
      // 设置解析中状态
      setState(() {
        _isAnalyzing = true;
        _guaName = '解析中...';
        _guaDescription = '命理师正在为您解析卦象，请稍后...';
      });
      
      // 获取当前时间信息
      final now = DateTime.now();
      final currentTime = {
        'year': now.year,
        'month': now.month,
        'day': now.day,
        'hour': now.hour,
        'minute': now.minute,
        'weekday': now.weekday,
      };
      
      // 使用实际问题或默认问题
      final questionText = _questionController.text.trim().isEmpty ? 
          _currentDefaultQuestion : _questionController.text.trim();
      
      // 构建摇卦信息 - 直接构建符合后端验证规则的数据格式
      final requestData = {
        'question': questionText,
        'guaLines': _guaLines.map((line) => line ? '阳' : '阴').toList(),
        'timeInfo': currentTime,
        'divineTime': now.toIso8601String(),
      };
      
      print('📤 准备发送数据到后端: $requestData');
      
      // 向后端发起请求获取卦象解析
      final predictionProvider = Provider.of<PredictionProvider>(context, listen: false);
      final result = await predictionProvider.createLiuyaoRecord(
        question: questionText,
        inputData: requestData,
      );
      
      print('📥 收到后端响应: ${result.resultData}');
      
      // 更新UI显示
      if (mounted) {
        setState(() {
          _isAnalyzing = false; // 解析完成
          _guaName = result.resultData['hexagramName'] ?? '未知卦象';
          _guaDescription = result.resultData['analysis'] ?? '正在解析卦象...';
          _currentRecordId = result.id; // 保存记录ID用于后续操作
        });
      }
    } catch (e) {
      // 错误处理 - 使用本地默认解析
      print('卦象解析失败: $e');
      final guaNames = ['乾卦', '坤卦', '震卦', '巽卦', '坎卦', '离卦', '艮卦', '兑卦'];
      final random = math.Random();
      
      // 使用实际问题或默认问题
      final questionText = _questionController.text.trim().isEmpty ? 
          _currentDefaultQuestion : _questionController.text.trim();
      
      setState(() {
        _isAnalyzing = false; // 解析完成（失败）
        _guaName = guaNames[random.nextInt(guaNames.length)];
        _guaDescription = '网络连接异常，使用本地解析：此卦象征天道刚健，事业发展顺利，但需要持之以恒的努力。根据您的问题"${questionText}"，此卦显示事情会有好的发展，但需要耐心等待时机。建议您积极进取，把握机会，避免急躁冒进。';
        _currentRecordId = DateTime.now().millisecondsSinceEpoch.toString(); // 生成本地ID
      });
    }
  }

  void _resetDivination() {
    setState(() {
      _currentRound = 1;
      _guaLines.clear();
      _showResult = false;
      _guaName = '';
      _guaDescription = '';
      _currentRecordId = null;
      _questionController.clear();
      _isAutoShaking = false; // 重置自动摇卦状态
      _isAnalyzing = false; // 重置解析状态
      // 重新生成默认问题
      _refreshDefaultQuestion();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFFEF3C7),
          Color(0xFFFDE68A),
        ],
        child: Column(
          children: [
            const IOSStatusBar(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFF1E293B),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => context.go('/'),
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                  ),
                  const Expanded(
                    child: Text(
                      '六爻预测',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const ConnectionStatusWidget(
                    showDetails: true,
                    showRefreshButton: true,
                  ),
                ],
              ),
            ),
            
            // 连接状态横幅
            const ConnectionBanner(),
            
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 说明区域
                    _buildInstructionCard(),
                    
                    // 问题输入区域
                    _buildQuestionInput(),
                    
                    // 摇卦区域
                    _buildShakeArea(),
                    
                    // 卦象展示区域
                    if (_guaLines.isNotEmpty) _buildGuaDisplay(),
                    
                    // 解卦结果区域
                    if (_showResult) _buildResultAnalysis(),
                    
                    // AI咨询区域
                    if (_showResult) _buildAIConsultSection(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            const BottomNavigation(currentIndex: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.infoCircle,
                color: Color(0xFFD97706),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '六爻占卜说明',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructionItem('• 请在心中默念您要问的问题'),
              _buildInstructionItem('• 问题要具体明确，不宜过于宽泛'),
              _buildInstructionItem('• 保持诚心，点击下方"开始摇卦"按钮'),
              _buildInstructionItem('• 系统将自动完成六次摇卦，形成完整卦象'),
              _buildInstructionItem('• 如需中断摇卦过程，可点击"停止"按钮'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF374151),
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildQuestionInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '请输入您的问题',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _questionController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: _currentDefaultQuestion,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Color(0xFFD97706), width: 2),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Color(0xFFD97706), width: 2),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton.icon(
                onPressed: _refreshDefaultQuestion,
                icon: const Icon(
                  FontAwesomeIcons.random,
                  size: 14,
                  color: Colors.white,
                ),
                label: const Text(
                  '换一批',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFD97706),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildShakeArea() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            '第${_currentRound}次摇卦',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 24),
          
          // 铜钱动画
          AnimatedBuilder(
            animation: _coinAnimationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _coinAnimationController.value * 2 * math.pi,
                child: Icon(
                  FontAwesomeIcons.coins,
                  size: 64,
                  color: Color(0xFFD97706),
                ),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          Text(
            _isShaking ? '正在摇卦...' : _isAutoShaking ? '自动摇卦中...' : '准备摇卦中...',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF6B7280),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 摇卦按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: AnimatedBuilder(
                  animation: _shakeAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + _shakeAnimationController.value * 0.1,
                      child: ElevatedButton(
                        onPressed: _isAutoShaking || _currentRound > 6 ? null : _shakeCoins,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFD97706),
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 8,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              FontAwesomeIcons.handPaper,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _currentRound > 6 ? '摇卦完成' : '开始摇卦',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              if (_isAutoShaking)
                Padding(
                  padding: const EdgeInsets.only(left: 12.0),
                  child: ElevatedButton(
                    onPressed: _cancelAutoShaking,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6B7280),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: const Icon(
                      Icons.stop_circle_outlined,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // 进度指示
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(6, (index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: index < _currentRound - 1 
                      ? const Color(0xFFD97706) 
                      : const Color(0xFFD1D5DB),
                  shape: BoxShape.circle,
                ),
              );
            }),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '进度: ${_currentRound - 1}/6',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6B7280),
            ),
          ),
          
          if (_currentRound > 6)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: ElevatedButton(
                onPressed: _resetDivination,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6B7280),
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '重新摇卦',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGuaDisplay() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '当前卦象',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 20),
          
          // 六爻卦象展示
          Container(
            width: 120,
            child: Column(
              children: _guaLines.asMap().entries.map((entry) {
                final index = entry.key;
                final isYang = entry.value;
                return Container(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: _buildGuaLine(isYang),
                );
              }).toList().reversed.toList(), // 从下往上显示
            ),
          ),
          
          if (_showResult) ...[
            const SizedBox(height: 20),
            Text(
              _guaName,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
            // 移除卦象解析内容，只保留卦象图形和名称
          ],
        ],
      ),
    );
  }

  Widget _buildGuaLine(bool isYang) {
    return Container(
      height: 8,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
      ),
      child: isYang
          ? Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1F2937),
                borderRadius: BorderRadius.circular(4),
              ),
            )
          : Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF1F2937),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildResultAnalysis() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFFEF3C7),
            const Color(0xFFFDE68A),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFD97706), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _isAnalyzing ? 
                SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(
                    strokeWidth: 2.5, 
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD97706)),
                  )
                ) : 
                Icon(
                  FontAwesomeIcons.scroll,
                  color: Color(0xFFD97706),
                  size: 20,
                ),
              const SizedBox(width: 8),
              Text(
                _isAnalyzing ? '解析中' : '卦象解析',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          _buildAnalysisSection(_isAnalyzing ? '命理师解析中' : '卦象解析', _guaDescription),
          
          // 添加解析中的动画指示器
          if (_isAnalyzing)
            Center(
              child: Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Column(
                  children: [
                    SizedBox(
                      width: 36,
                      height: 36,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFD97706)),
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '命理师正在为您解析，请稍后...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF1F2937),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAnalysisSection(String title, String content) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF374151),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  // 构建AI解析区域
  Widget _buildAIConsultSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.robot,
                color: Color(0xFF059669),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'AI深度解析',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          const Text(
            '🎯 基于您的卦象，可以获得更深入的AI解析\n💬 将卦象信息发送到AI聊天，获得个性化建议\n🔮 支持追问更多细节问题',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF6B7280),
              height: 1.6,
            ),
          ),
          const SizedBox(height: 20),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _isAnalyzing ? null : _goToAIConsult, // 解析完成前禁用按钮
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF059669),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 4,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        FontAwesomeIcons.commentDots,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        '进入AI深度解析',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 