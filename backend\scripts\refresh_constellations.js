/**
 * 星座运势数据刷新脚本
 * 用于手动刷新所有星座运势数据
 * 
 * 使用方法：
 * node scripts/refresh_constellations.js
 * 
 * 可选参数：
 * --force: 强制刷新，即使今天已经更新过
 * 
 * 示例：
 * node scripts/refresh_constellations.js --force
 */

require('dotenv').config();
const AV = require('leanengine');
const axios = require('axios');

// 初始化LeanCloud
const { appId, appKey, masterKey } = require('../.leancloud/config.json');
AV.init({
  appId,
  appKey,
  masterKey,
  serverURL: 'https://4ahhqofx.lc-cn-n1-shared.com',
});

// 导入星座运势模型
const ConstellationFortune = require('../models/ConstellationFortune');

// 解析命令行参数
const args = process.argv.slice(2);
const forceRefresh = args.includes('--force');

async function refreshConstellations() {
  try {
    console.log('\n🌟 ===== 星座运势数据刷新脚本 =====');
    console.log(`🔄 开始${forceRefresh ? '强制' : ''}刷新所有星座运势...`);
    
    // 检查今日运势是否已更新
    const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
    if (isUpdated && !forceRefresh) {
      console.log('ℹ️ 今日星座运势已更新，无需重复更新');
      console.log('💡 如需强制刷新，请使用 --force 参数');
      return;
    }
    
    // 开始刷新所有星座运势
    const results = await ConstellationFortune.updateAllDailyFortunes();
    
    console.log(`✅ 所有星座运势刷新成功，共${results.length}条数据`);
    console.log('🎉 ===== 刷新完成 =====\n');
  } catch (error) {
    console.error('❌ 刷新星座运势失败:', error);
  } finally {
    // 脚本执行完成后退出
    process.exit();
  }

}

// 执行刷新
refreshConstellations(); 
refreshConstellations(); 