class UserModel {
  final String id;
  final String nickname;
  final String avatar;
  final String memberLevel;
  final int predictionCount;
  final int favoriteCount;
  final int consultationCount;
  final DateTime createdAt;
  final DateTime lastActiveAt;

  UserModel({
    required this.id,
    required this.nickname,
    required this.avatar,
    required this.memberLevel,
    required this.predictionCount,
    required this.favoriteCount,
    required this.consultationCount,
    required this.createdAt,
    required this.lastActiveAt,
  });

  // 添加JSON序列化方法
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      nickname: json['nickname'],
      avatar: json['avatar'],
      memberLevel: json['memberLevel'],
      predictionCount: json['predictionCount'],
      favoriteCount: json['favoriteCount'],
      consultationCount: json['consultationCount'],
      createdAt: DateTime.parse(json['createdAt']),
      lastActiveAt: DateTime.parse(json['lastActiveAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'avatar': avatar,
      'memberLevel': memberLevel,
      'predictionCount': predictionCount,
      'favoriteCount': favoriteCount,
      'consultationCount': consultationCount,
      'createdAt': createdAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? nickname,
    String? avatar,
    String? memberLevel,
    int? predictionCount,
    int? favoriteCount,
    int? consultationCount,
    DateTime? createdAt,
    DateTime? lastActiveAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      avatar: avatar ?? this.avatar,
      memberLevel: memberLevel ?? this.memberLevel,
      predictionCount: predictionCount ?? this.predictionCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      consultationCount: consultationCount ?? this.consultationCount,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, nickname: $nickname, memberLevel: $memberLevel)';
  }
}

// 默认用户数据
final UserModel defaultUser = UserModel(
  id: 'default_user_001',
  nickname: '银发用户',
  avatar: 'assets/images/default_avatar.png',
  memberLevel: '普通会员',
  predictionCount: 0,
  favoriteCount: 0,
  consultationCount: 0,
  createdAt: DateTime.now(),
  lastActiveAt: DateTime.now(),
); 