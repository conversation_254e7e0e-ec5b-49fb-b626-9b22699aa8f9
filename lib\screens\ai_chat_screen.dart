import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../widgets/common_widgets.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/chat_provider.dart';
import '../models/chat_message.dart' as models;
import '../services/api_service.dart';

class AiChatScreen extends StatefulWidget {
  final Map<String, dynamic>? baziContext;
  final bool fromBazi;
  
  const AiChatScreen({
    super.key, 
    this.baziContext,
    this.fromBazi = false,
  });

  @override
  State<AiChatScreen> createState() => _AiChatScreenState();
}

class _AiChatScreenState extends State<AiChatScreen>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;
  List<Map<String, dynamic>> _quickQuestions = [];

  @override
  void initState() {
    super.initState();
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1400),
      vsync: this,
    );
    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingAnimationController,
      curve: Curves.easeInOut,
    ));

    // 添加文本输入监听
    _textController.addListener(() {
      setState(() {});
    });

    // 初始化聊天数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 如果是从八字页面跳转来的，初始化八字咨询
      if (widget.fromBazi && widget.baziContext != null) {
        _initializeBaziConsultation();
      } else {
        // 正常初始化聊天
        _initializeChat();
      }
    });
  }

  Future<void> _initializeChat() async {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    await chatProvider.loadChatHistory();
    _loadQuickQuestions();
  }

  Future<void> _loadQuickQuestions() async {
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final questions = await chatProvider.getQuickQuestions();
    setState(() {
      _quickQuestions = questions;
    });
  }

  Future<void> _initializeBaziConsultation() async {
    try {
      final apiService = ApiService();
      
      // 发送欢迎消息
      final chatProvider = Provider.of<ChatProvider>(context, listen: false);
      chatProvider.addMessage(models.ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: '您好！我已经获得了您的八字信息，可以为您提供更深入的命理咨询。请问您希望了解哪方面的内容？比如：事业发展、感情婚姻、财运状况、健康养生等。',
        type: models.MessageType.ai,
        timestamp: DateTime.now(),
      ));
      
      print('🔮 八字深度咨询模式初始化');
      print('📋 八字上下文: ${widget.baziContext}');
      
      // 启动八字咨询会话（暂时不需要发送初始问题）
      await apiService.startBaziConsultation(
        baziContext: widget.baziContext!,
        initialQuestion: '',
      );
      
      _scrollToBottom();
    } catch (e) {
      print('❌ 初始化八字咨询失败: $e');
      // 如果失败，回退到正常聊天模式
      _initializeChat();
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  void _sendMessage(String text) {
    if (text.trim().isEmpty) return;

    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    chatProvider.sendMessageAndGetReply(text);
    
    _textController.clear();
    _scrollToBottom();
  }



  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF0F9FF), Color(0xFFE0F2FE)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // iOS状态栏
              const IOSStatusBar(),
              // 顶部导航栏
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: const BoxDecoration(
                  color: Color(0xFF1E293B),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => context.pop(),
                      icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        widget.fromBazi ? 'AI八字深度咨询' : 'AI智能咨询',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const ConnectionStatusWidget(),
                  ],
                ),
              ),
              // 聊天区域
              Expanded(
                child: Column(
                  children: [
                    // 八字上下文提示（如果是从八字来的）
                    if (widget.fromBazi) _buildBaziContextBanner(),
                    
                    // 消息列表
                    Expanded(
                      child: Consumer<ChatProvider>(
                        builder: (context, chatProvider, child) {
                          if (chatProvider.isLoading) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }

                          return ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.all(16),
                            itemCount: chatProvider.messages.length + (chatProvider.isTyping ? 1 : 0),
                            itemBuilder: (context, index) {
                              if (index == chatProvider.messages.length && chatProvider.isTyping) {
                                return _buildTypingIndicator();
                              }
                              return _buildMessage(chatProvider.messages[index]);
                            },
                          );
                        },
                      ),
                    ),
                    // 快捷问题（只在非八字咨询模式显示）
                    if (!widget.fromBazi) Consumer<ChatProvider>(
                      builder: (context, chatProvider, child) {
                        if (chatProvider.messages.length <= 1 && _quickQuestions.isNotEmpty) {
                          return _buildQuickQuestions();
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    // 输入区域
                    _buildInputArea(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessage(models.ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: message.type == models.MessageType.user 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.type != models.MessageType.user) ...[
            // AI头像
            Container(
              width: 32,
              height: 32,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                FontAwesomeIcons.robot,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
          ],
          // 消息气泡
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.8,
              ),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: message.type == models.MessageType.user ? const Color(0xFF3B82F6) : Colors.white,
                borderRadius: BorderRadius.circular(18).copyWith(
                  bottomLeft: message.type == models.MessageType.user ? const Radius.circular(18) : const Radius.circular(6),
                  bottomRight: message.type == models.MessageType.user ? const Radius.circular(6) : const Radius.circular(18),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  fontSize: 16,
                  color: message.type == models.MessageType.user ? Colors.white : Colors.black87,
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI头像
          Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              ),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              FontAwesomeIcons.robot,
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          // 正在输入指示器
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(18).copyWith(
                bottomLeft: const Radius.circular(6),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedBuilder(
                  animation: _typingAnimation,
                  builder: (context, child) {
                    return Row(
                      children: List.generate(3, (index) {
                        return Container(
                          margin: const EdgeInsets.only(right: 4),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade400,
                            shape: BoxShape.circle,
                          ),
                        );
                      }),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  '正在为您分析...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickQuestions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷问题',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _quickQuestions.map((questionData) {
              final question = questionData['text'] as String;
              final icon = questionData['icon'] as String? ?? '💬';
              return GestureDetector(
                onTap: () => _sendMessage(question),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.grey.shade300,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        icon,
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        question,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade200,
          ),
        ),
      ),
      child: Row(
        children: [
          // 输入框
          Expanded(
            child: Container(
              constraints: const BoxConstraints(
                minHeight: 44,
                maxHeight: 120,
              ),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey.shade300,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(22),
              ),
              child: TextField(
                controller: _textController,
                maxLines: null,
                decoration: const InputDecoration(
                  hintText: '请输入您的问题...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onSubmitted: (text) => _sendMessage(text),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 发送按钮
          GestureDetector(
            onTap: () => _sendMessage(_textController.text),
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: _textController.text.trim().isNotEmpty 
                    ? const Color(0xFF3B82F6) 
                    : Colors.grey.shade400,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBaziContextBanner() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF0FDF4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF16A34A), width: 1),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.scroll,
            color: const Color(0xFF16A34A),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '八字深度咨询模式',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF16A34A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '已加载您的八字信息，可进行个性化深度咨询',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF16A34A).withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

 