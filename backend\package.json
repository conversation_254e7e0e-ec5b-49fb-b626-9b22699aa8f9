{"name": "yinfa-shenfo-backend", "version": "1.0.0", "description": "银发-满天神佛应用后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "deploy": "lean deploy", "lint": "eslint ."}, "engines": {"node": ">=18.0.0"}, "dependencies": {"axios": "^1.4.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.0", "leancloud-storage": "^4.15.2", "leanengine": "^3.8.0", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.2", "node-schedule": "^2.1.1", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.42.0", "jest": "^29.5.0", "nodemon": "^2.0.22"}, "keywords": ["fortune-telling", "ai-chatbot", "flutter-backend"], "author": "YinfaShenfo Team", "license": "MIT"}