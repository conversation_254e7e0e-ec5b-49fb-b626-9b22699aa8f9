import 'package:flutter/material.dart';

enum Gender {
  male,
  female,
}

enum WuxingElement {
  jin,   // 金
  mu,    // 木
  shui,  // 水
  huo,   // 火
  tu,    // 土
}

class BaziInfo {
  final String id;
  final Gender gender;
  final DateTime birthDate;
  final TimeOfDay birthTime;
  final String birthPlace;
  final String yearColumn;
  final String monthColumn;
  final String dayColumn;
  final String hourColumn;
  final Map<WuxingElement, int> wuxingCount;
  final String analysis;
  final DateTime createdAt;

  BaziInfo({
    required this.id,
    required this.gender,
    required this.birthDate,
    required this.birthTime,
    required this.birthPlace,
    required this.yearColumn,
    required this.monthColumn,
    required this.dayColumn,
    required this.hourColumn,
    required this.wuxingCount,
    required this.analysis,
    required this.createdAt,
  });

  factory BaziInfo.fromJson(Map<String, dynamic> json) {
    return BaziInfo(
      id: json['id'],
      gender: Gender.values.firstWhere(
        (e) => e.name == json['gender'],
        orElse: () => Gender.male,
      ),
      birthDate: DateTime.parse(json['birthDate']),
      birthTime: TimeOfDay(
        hour: json['birthTime']['hour'],
        minute: json['birthTime']['minute'],
      ),
      birthPlace: json['birthPlace'],
      yearColumn: json['yearColumn'],
      monthColumn: json['monthColumn'],
      dayColumn: json['dayColumn'],
      hourColumn: json['hourColumn'],
      wuxingCount: Map<WuxingElement, int>.fromEntries(
        json['wuxingCount'].entries.map<MapEntry<WuxingElement, int>>(
          (entry) => MapEntry(
            WuxingElement.values.firstWhere((e) => e.name == entry.key),
            entry.value,
          ),
        ),
      ),
      analysis: json['analysis'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'gender': gender.name,
      'birthDate': birthDate.toIso8601String(),
      'birthTime': {
        'hour': birthTime.hour,
        'minute': birthTime.minute,
      },
      'birthPlace': birthPlace,
      'yearColumn': yearColumn,
      'monthColumn': monthColumn,
      'dayColumn': dayColumn,
      'hourColumn': hourColumn,
      'wuxingCount': wuxingCount.map(
        (key, value) => MapEntry(key.name, value),
      ),
      'analysis': analysis,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  String get genderDisplayName {
    return gender == Gender.male ? '男' : '女';
  }

  String getWuxingDisplayName(WuxingElement element) {
    switch (element) {
      case WuxingElement.jin:
        return '金';
      case WuxingElement.mu:
        return '木';
      case WuxingElement.shui:
        return '水';
      case WuxingElement.huo:
        return '火';
      case WuxingElement.tu:
        return '土';
    }
  }

  Color getWuxingColor(WuxingElement element) {
    switch (element) {
      case WuxingElement.jin:
        return const Color(0xFFFBBF24); // 金色
      case WuxingElement.mu:
        return const Color(0xFF22C55E); // 绿色
      case WuxingElement.shui:
        return const Color(0xFF3B82F6); // 蓝色
      case WuxingElement.huo:
        return const Color(0xFFEF4444); // 红色
      case WuxingElement.tu:
        return const Color(0xFFA3A3A3); // 灰色
    }
  }

  // 获取五行最强的元素
  WuxingElement get strongestElement {
    var max = wuxingCount.entries.reduce((a, b) => a.value > b.value ? a : b);
    return max.key;
  }

  // 获取五行最弱的元素
  WuxingElement get weakestElement {
    var min = wuxingCount.entries.reduce((a, b) => a.value < b.value ? a : b);
    return min.key;
  }
} 