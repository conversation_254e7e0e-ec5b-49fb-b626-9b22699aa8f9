# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 环境变量
.env
.env.local
.env.production
.env.test

# 日志文件
logs/
*.log
lerna-debug.log*

# 缓存
.npm
.eslintcache
.nyc_output

# 构建输出
dist/
build/

# 测试覆盖率
coverage/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/

# 上传文件
uploads/

# LeanCloud
.leancloud/local/

# PM2
.pm2/

# Docker
.dockerignore

# 备份文件
*.backup
*.bak

# 证书文件
*.pem
*.key
*.crt 