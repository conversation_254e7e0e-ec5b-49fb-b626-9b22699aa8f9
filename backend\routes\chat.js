const express = require('express');
const AuthUtils = require('../utils/auth');
const ValidationUtils = require('../utils/validation');
const ChatMessage = require('../models/ChatMessage');
const AIService = require('../services/AIService');
const User = require('../models/User');
const AV = require('leanengine');
const router = express.Router();

// 创建AI服务实例
const aiService = new AIService();

// 今日运势缓存（内存缓存，生产环境建议使用Redis）
let dailyFortuneCache = {
  date: null,
  fortune: null,
  lastUpdated: null
};

// 获取聊天记录
router.get('/history', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    // 如果是游客模式，返回空历史记录
    if (!req.user) {
      return res.json([]);
    }
    
    const userId = req.user.userId;
    const { limit = 50 } = req.query;
    
    // 验证limit参数
    const parsedLimit = parseInt(limit);
    if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 100) {
      return res.status(400).json({
        error: {
          code: 400,
          message: 'limit参数必须是1-100之间的数字'
        }
      });
    }
    
    // 获取聊天历史
    const messages = await ChatMessage.getChatHistory(userId, parsedLimit);
    
    res.json(messages);
  } catch (error) {
    console.error('获取聊天记录失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取聊天记录失败'
      }
    });
  }
});

// 发送消息
router.post('/message', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const userId = req.user ? req.user.userId : 'guest';
    
    // 验证消息内容
    const { error, value } = ValidationUtils.validateChatMessage(req.body);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }
    
    const { content } = value;
    
    // 保存用户消息（仅登录用户）
    let userMessage = null;
    if (req.user) {
      userMessage = await ChatMessage.createMessage(userId, 'user', content);
    }
    
    // 获取聊天历史（用于AI上下文）
    const chatHistory = req.user ? await ChatMessage.getChatHistory(userId, 10) : [];
    
    // 获取AI回复
    const aiResponse = await aiService.processMessage(content, chatHistory);
    
    // 保存AI回复（仅登录用户）
    let aiMessage = null;
    if (req.user) {
      aiMessage = await ChatMessage.createMessage(userId, 'ai', aiResponse);
      
      // 更新用户咨询统计
      await User.updateUserStats(userId, 'consultationCount');
    }
    
    // 构建响应（兼容登录用户和游客）
    const response = {
      userMessage: userMessage ? {
        id: userMessage.id,
        type: userMessage.get('type'),
        content: userMessage.get('content'),
        timestamp: userMessage.get('createdAt')
      } : {
        id: Date.now().toString(),
        type: 'user',
        content: content,
        timestamp: new Date()
      },
      aiResponse: aiMessage ? {
        id: aiMessage.id,
        type: aiMessage.get('type'),
        content: aiMessage.get('content'),
        timestamp: aiMessage.get('createdAt')
      } : {
        id: Date.now().toString() + '_ai',
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      }
    };
    
    res.status(201).json(response);
  } catch (error) {
    console.error('发送消息失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '发送消息失败，请稍后再试'
      }
    });
  }
});

// 流式发送消息
router.post('/message-stream', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const userId = req.user ? req.user.userId : 'guest';
    
    // 验证消息内容
    const { error, value } = ValidationUtils.validateChatMessage(req.body);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }
    
    const { content } = value;
    
    console.log('🚀 [ApiService] 开始发送流式消息');
    console.log('📤 [ApiService] 请求内容:', content);
    
    // 设置流式响应头
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Transfer-Encoding', 'chunked');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    // 保存用户消息（仅登录用户）
    let userMessage = null;
    if (req.user) {
      userMessage = await ChatMessage.createMessage(userId, 'user', content);
    }
    
    // 获取聊天历史（用于AI上下文）
    const chatHistory = req.user ? await ChatMessage.getChatHistory(userId, 10) : [];
    
    // 构建上下文信息
    let contextInfo = '';
    if (content.includes('六爻卦象深度解析')) {
      console.log('📋 [ApiService] 上下文信息:', content.substring(0, 200) + '...');
      contextInfo = content;
    }
    
    try {
      // 获取AI流式回复
      const aiResponse = await aiService.processMessageStream(content, chatHistory, (chunk) => {
        // 发送数据块
        res.write(chunk);
      });
      
      // 结束响应
      res.end();
      
      // 保存完整的AI回复（仅登录用户）
      if (req.user) {
        await ChatMessage.createMessage(userId, 'ai', aiResponse);
        
        // 更新用户咨询统计
        await User.updateUserStats(userId, 'consultationCount');
      }
      
      console.log('✅ [ApiService] 流式消息发送完成');
      
    } catch (aiError) {
      console.log('❌ [ApiService] 流式AI处理失败:', aiError);
      
      // 发送错误信息
      res.write('抱歉，AI服务暂时不可用，请稍后重试。');
      res.end();
      
      // 保存错误回复（仅登录用户）
      if (req.user) {
        await ChatMessage.createMessage(userId, 'ai', '抱歉，AI服务暂时不可用，请稍后重试。');
      }
    }
    
  } catch (error) {
    console.error('❌ [ApiService] 流式请求失败:', error);
    console.log('📝 [ApiService] 错误类型:', error.constructor.name);
    
    if (error.name === 'DioException') {
      console.log('🔍 [ApiService] DioException详情:');
      console.log('   状态码:', error.response?.status);
      console.log('   错误类型:', error.type);
      console.log('   错误信息:', error.message);
      console.log('   响应数据:', error.response?.data);
    }
    
    if (!res.headersSent) {
      res.status(500).json({
        error: {
          code: 500,
          message: '流式消息发送失败，请稍后再试'
        }
      });
    }
  }
});

// 清空聊天记录
router.delete('/history', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    await ChatMessage.clearHistory(userId);
    
    res.json({
      message: '聊天记录已清空'
    });
  } catch (error) {
    console.error('清空聊天记录失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '清空聊天记录失败'
      }
    });
  }
});

// 获取快捷问题列表
router.get('/quick-questions', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const quickQuestions = [
      {
        id: 1,
        text: '我的财运如何？',
        category: '财运',
        icon: '💰'
      },
      {
        id: 2,
        text: '感情运势怎样？',
        category: '感情',
        icon: '💕'
      },
      {
        id: 3,
        text: '事业发展建议',
        category: '事业',
        icon: '🚀'
      },
      {
        id: 4,
        text: '健康需要注意什么？',
        category: '健康',
        icon: '🌱'
      },
      {
        id: 5,
        text: '近期有什么需要注意的？',
        category: '运势',
        icon: '🔮'
      }
    ];
    
    res.json(quickQuestions);
  } catch (error) {
    console.error('获取快捷问题失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取快捷问题失败'
      }
    });
  }
});

// 获取AI服务状态
router.get('/status', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const status = await aiService.getServiceStatus();
    res.json(status);
  } catch (error) {
    console.error('获取AI服务状态失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取AI服务状态失败'
      }
    });
  }
});

// 获取聊天统计信息
router.get('/stats', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 统计消息数量
    const userMessageQuery = new AV.Query('ChatMessage');
    userMessageQuery.equalTo('user', AV.Object.createWithoutData('_User', userId));
    userMessageQuery.equalTo('type', 'user');
    const userMessageCount = await userMessageQuery.count();
    
    const aiMessageQuery = new AV.Query('ChatMessage');
    aiMessageQuery.equalTo('user', AV.Object.createWithoutData('_User', userId));
    aiMessageQuery.equalTo('type', 'ai');
    const aiMessageCount = await aiMessageQuery.count();
    
    // 获取最近聊天时间
    const recentMessageQuery = new AV.Query('ChatMessage');
    recentMessageQuery.equalTo('user', AV.Object.createWithoutData('_User', userId));
    recentMessageQuery.descending('createdAt');
    recentMessageQuery.limit(1);
    const recentMessage = await recentMessageQuery.first();
    
    // 获取聊天会话数（简化处理，按天分组）
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    const todayMessageQuery = new AV.Query('ChatMessage');
    todayMessageQuery.equalTo('user', AV.Object.createWithoutData('_User', userId));
    todayMessageQuery.greaterThanOrEqualTo('createdAt', startOfDay);
    const todayMessageCount = await todayMessageQuery.count();
    
    res.json({
      userMessageCount,
      aiMessageCount,
      totalMessages: userMessageCount + aiMessageCount,
      todayMessages: todayMessageCount,
      lastChatTime: recentMessage ? recentMessage.get('createdAt') : null
    });
  } catch (error) {
    console.error('获取聊天统计失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取聊天统计失败'
      }
    });
  }
});

// 评价AI回复
router.post('/message/:messageId/rate', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const messageId = req.params.messageId;
    const { rating, feedback } = req.body;
    
    // 验证评分
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '评分必须是1-5之间的数字'
        }
      });
    }
    
    // 查找消息
    const query = new AV.Query('ChatMessage');
    query.equalTo('objectId', messageId);
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    query.equalTo('type', 'ai');
    
    const message = await query.first();
    if (!message) {
      return res.status(404).json({
        error: {
          code: 404,
          message: 'AI消息不存在'
        }
      });
    }
    
    // 保存评价
    message.set('rating', rating);
    if (feedback) {
      message.set('feedback', feedback);
    }
    await message.save();
    
    res.json({
      message: '评价已保存',
      rating,
      feedback
    });
  } catch (error) {
    console.error('评价AI回复失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '评价AI回复失败'
      }
    });
  }
});

// 重新生成AI回复
router.post('/message/:messageId/regenerate', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const messageId = req.params.messageId;
    
    // 查找用户消息
    const query = new AV.Query('ChatMessage');
    query.equalTo('objectId', messageId);
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    query.equalTo('type', 'user');
    
    const userMessage = await query.first();
    if (!userMessage) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '用户消息不存在'
        }
      });
    }
    
    const userContent = userMessage.get('content');
    
    // 获取聊天历史
    const chatHistory = await ChatMessage.getChatHistory(userId, 10);
    
    // 重新生成AI回复
    const aiResponse = await aiService.processMessage(userContent, chatHistory);
    
    // 保存新的AI回复
    const newAiMessage = await ChatMessage.createMessage(userId, 'ai', aiResponse);
    
    res.json({
      id: newAiMessage.id,
      type: newAiMessage.get('type'),
      content: newAiMessage.get('content'),
      timestamp: newAiMessage.get('createdAt')
    });
  } catch (error) {
    console.error('重新生成AI回复失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '重新生成AI回复失败'
      }
    });
  }
});

// 获取今日运势
router.get('/daily-fortune', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const today = new Date();
    const todayDateString = today.toDateString();
    
    // 检查缓存是否需要更新（每日24点更新）
    const needUpdate = !dailyFortuneCache.date || 
                      dailyFortuneCache.date !== todayDateString ||
                      !dailyFortuneCache.fortune;
    
    if (needUpdate) {
      console.log('生成新的今日运势...');
      
      // 尝试从数据库获取今日运势
      let storedFortune = null;
      try {
        const fortuneQuery = new AV.Query('DailyFortune');
        fortuneQuery.equalTo('date', todayDateString);
        fortuneQuery.descending('createdAt');
        fortuneQuery.limit(1);
        
        const fortuneResult = await fortuneQuery.find();
        if (fortuneResult.length > 0) {
          storedFortune = fortuneResult[0];
        }
      } catch (dbError) {
        console.log('数据库查询失败，将生成新运势:', dbError.message);
      }
      
      if (storedFortune) {
        // 从数据库获取
        dailyFortuneCache = {
          date: todayDateString,
          fortune: {
            date: storedFortune.get('displayDate'),
            lunarDate: storedFortune.get('lunarDate'),
            content: storedFortune.get('content'),
            timestamp: storedFortune.get('createdAt').toISOString(),
            isAIGenerated: storedFortune.get('isAIGenerated') || false
          },
          lastUpdated: new Date()
        };
        console.log('从数据库获取今日运势');
      } else {
        // 生成新的运势并保存到数据库
        const newFortune = await aiService.generateDailyFortune();
        
        // 保存到数据库
        try {
          const DailyFortune = AV.Object.extend('DailyFortune');
          const fortuneObject = new DailyFortune();
          
          fortuneObject.set('date', todayDateString);
          fortuneObject.set('displayDate', newFortune.date);
          fortuneObject.set('lunarDate', newFortune.lunarDate);
          fortuneObject.set('content', newFortune.content);
          fortuneObject.set('isAIGenerated', newFortune.isAIGenerated);
          
          await fortuneObject.save();
          console.log('今日运势已保存到数据库');
        } catch (saveError) {
          console.error('保存运势到数据库失败:', saveError);
        }
        
        // 更新缓存
        dailyFortuneCache = {
          date: todayDateString,
          fortune: newFortune,
          lastUpdated: new Date()
        };
        console.log('生成并缓存新的今日运势');
      }
    }
    
    // 返回运势数据
    res.json({
      success: true,
      data: {
        ...dailyFortuneCache.fortune,
        cached: !needUpdate,
        lastUpdated: dailyFortuneCache.lastUpdated
      }
    });
    
  } catch (error) {
    console.error('获取今日运势失败:', error);
    
    // 返回默认运势
    const defaultFortune = aiService.getDefaultDailyFortune();
    res.json({
      success: true,
      data: {
        ...defaultFortune,
        cached: false,
        error: '服务暂时不可用，返回默认运势'
      }
    });
  }
});

// 强制刷新今日运势（管理员功能）
router.post('/daily-fortune/refresh', AuthUtils.authenticate, async (req, res) => {
  try {
    // 这里可以添加管理员权限验证
    // const userRole = req.user.role;
    // if (userRole !== 'admin') {
    //   return res.status(403).json({ error: { message: '权限不足' } });
    // }
    
    console.log('手动刷新今日运势...');
    
    // 生成新的运势
    const newFortune = await aiService.generateDailyFortune();
    const todayDateString = new Date().toDateString();
    
    // 保存到数据库
    try {
      const DailyFortune = AV.Object.extend('DailyFortune');
      const fortuneObject = new DailyFortune();
      
      fortuneObject.set('date', todayDateString);
      fortuneObject.set('displayDate', newFortune.date);
      fortuneObject.set('lunarDate', newFortune.lunarDate);
      fortuneObject.set('content', newFortune.content);
      fortuneObject.set('isAIGenerated', newFortune.isAIGenerated);
      fortuneObject.set('isManualRefresh', true);
      
      await fortuneObject.save();
    } catch (saveError) {
      console.error('保存刷新的运势失败:', saveError);
    }
    
    // 更新缓存
    dailyFortuneCache = {
      date: todayDateString,
      fortune: newFortune,
      lastUpdated: new Date()
    };
    
    res.json({
      success: true,
      message: '今日运势已刷新',
      data: newFortune
    });
    
  } catch (error) {
    console.error('刷新今日运势失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '刷新今日运势失败'
      }
    });
  }
});

// 开始八字深度咨询 - 带上下文
router.post('/bazi-consultation', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    console.log('\n🎯 ===== 八字深度咨询开始 =====');
    const userId = req.user ? req.user.userId : 'guest';
    const isGuest = !req.user;
    console.log('👤 用户ID:', userId);
    console.log('🎭 游客模式:', isGuest);
    
    const { baziContext, initialQuestion } = req.body;
    
    console.log('📋 八字上下文:', JSON.stringify(baziContext, null, 2));
    console.log('❓ 初始问题:', initialQuestion);
    
    // 构建带有八字上下文的系统消息
    const baziContextMsg = `【八字背景信息】
性别：${baziContext.gender === 'male' ? '男' : '女'}
出生日期：${baziContext.birthDate}
出生时间：${baziContext.birthTime}
八字：年柱${baziContext.bazi.year}，月柱${baziContext.bazi.month}，日柱${baziContext.bazi.day}，时柱${baziContext.bazi.hour}
五行分布：金${baziContext.wuxingCount.jin || 0}，木${baziContext.wuxingCount.mu || 0}，水${baziContext.wuxingCount.shui || 0}，火${baziContext.wuxingCount.huo || 0}，土${baziContext.wuxingCount.tu || 0}

【之前的八字解读】
${baziContext.analysis}

现在用户希望进行更深入的咨询，请基于以上八字信息回答用户的问题，保持专业性和连贯性。`;
    
    // 如果有初始问题，先回答
    let response = '';
    if (initialQuestion && initialQuestion.trim()) {
      console.log('🤖 开始AI咨询回答...');
      
      // 构建带上下文的对话历史
      const conversationHistory = [
        {
          type: 'system',
          content: baziContextMsg
        }
      ];
      
      response = await aiService.sendMessage(initialQuestion, conversationHistory);
      console.log('✅ AI咨询回答完成:', response.substring(0, 200) + '...');
    }
    
    // 返回咨询会话信息
    res.json({
      success: true,
      consultationId: `bazi_${Date.now()}`,
      baziContext: baziContext,
      contextMessage: baziContextMsg,
      initialResponse: response,
      message: '八字深度咨询会话已开始'
    });
    
    console.log('🎉 ===== 八字深度咨询初始化完成 =====\n');
    
  } catch (error) {
    console.log('\n❌ ===== 八字深度咨询失败 =====');
    console.log('🚨 错误信息:', error.message);
    console.log('💥 ===== 八字深度咨询结束 =====\n');
    
    console.error('❌ 八字深度咨询失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '开始八字深度咨询失败，请稍后再试'
      }
    });
  }
});

module.exports = router; 