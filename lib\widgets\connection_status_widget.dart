import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connection_provider.dart';
import '../config.dart';

class ConnectionStatusWidget extends StatelessWidget {
  final bool showDetails;
  final bool showRefreshButton;
  
  const ConnectionStatusWidget({
    Key? key,
    this.showDetails = false,
    this.showRefreshButton = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectionProvider>(
      builder: (context, connectionProvider, child) {
        return GestureDetector(
          onTap: showDetails ? () => _showConnectionDetails(context, connectionProvider) : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: connectionProvider.statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: connectionProvider.statusColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (connectionProvider.isConnecting) ...[
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        connectionProvider.statusColor,
                      ),
                    ),
                  ),
                ] else ...[
                  Icon(
                    connectionProvider.statusIcon,
                    size: 12,
                    color: connectionProvider.statusColor,
                  ),
                ],
                const SizedBox(width: 8),
                Text(
                  connectionProvider.statusText,
                  style: TextStyle(
                    fontSize: 12,
                    color: connectionProvider.statusColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (showRefreshButton && !connectionProvider.isConnected) ...[
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () => connectionProvider.refreshConnection(),
                    child: Icon(
                      Icons.refresh,
                      size: 12,
                      color: connectionProvider.statusColor,
                    ),
                  ),
                ],
                if (connectionProvider.retryCount > 0) ...[
                  const SizedBox(width: 4),
                  Text(
                    '(${connectionProvider.retryCount})',
                    style: TextStyle(
                      fontSize: 10,
                      color: connectionProvider.statusColor.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  void _showConnectionDetails(BuildContext context, ConnectionProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              provider.statusIcon,
              color: provider.statusColor,
            ),
            const SizedBox(width: 8),
            const Text('连接状态详情'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('状态', provider.statusText, provider.statusColor),
            const SizedBox(height: 8),
            _buildDetailRow('后端地址', 'http://localhost:3000', Colors.grey[600]!),
            const SizedBox(height: 8),
            _buildDetailRow('重试次数', '${provider.retryCount}', Colors.grey[600]!),
            if (provider.lastConnectedTime != null) ...[
              const SizedBox(height: 8),
              _buildDetailRow(
                '最后连接时间',
                _formatTime(provider.lastConnectedTime!),
                Colors.grey[600]!,
              ),
            ],
            if (provider.errorMessage.isNotEmpty) ...[
              const SizedBox(height: 8),
              _buildDetailRow('错误信息', provider.errorMessage, Colors.red),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              provider.refreshConnection();
              Navigator.of(context).pop();
            },
            child: const Text('重新连接'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: color),
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);
    
    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}小时前';
    } else {
      return '${time.month}/${time.day} ${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}

// 大型连接状态横幅组件
class ConnectionBanner extends StatelessWidget {
  const ConnectionBanner({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectionProvider>(
      builder: (context, connectionProvider, child) {
        if (connectionProvider.isConnected) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: connectionProvider.statusColor.withOpacity(0.1),
            border: Border(
              bottom: BorderSide(
                color: connectionProvider.statusColor.withOpacity(0.3),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                connectionProvider.statusIcon,
                color: connectionProvider.statusColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      connectionProvider.statusText,
                      style: TextStyle(
                        color: connectionProvider.statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    if (connectionProvider.errorMessage.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        connectionProvider.errorMessage,
                        style: TextStyle(
                          color: connectionProvider.statusColor.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (!connectionProvider.isConnecting) ...[
                TextButton.icon(
                  onPressed: () => connectionProvider.refreshConnection(),
                  icon: Icon(
                    Icons.refresh,
                    size: 16,
                    color: connectionProvider.statusColor,
                  ),
                  label: Text(
                    '重试',
                    style: TextStyle(
                      color: connectionProvider.statusColor,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
} 