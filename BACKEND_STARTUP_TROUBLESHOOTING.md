# 后端启动故障排除指南

## 问题现象
启动后端服务时出现端口占用错误：
```
Error: listen EADDRINUSE: address already in use :::3001
```

## 解决方案

### 方案1：使用强制端口启动脚本（推荐）
```bash
start_backend_force_3002.bat
```
这个脚本会：
1. 停止所有现有的Node.js进程
2. 强制设置环境变量使用3002端口
3. 启动后端服务

### 方案2：手动清理进程
1. 打开任务管理器
2. 找到所有`node.exe`进程
3. 结束这些进程
4. 运行启动脚本

### 方案3：使用命令行清理
```bash
# 在PowerShell中执行
Get-Process -Name "node" | Stop-Process -Force
```

### 方案4：重启电脑
如果以上方案都不行，重启电脑可以清理所有进程。

## 验证服务状态

### 使用测试脚本
```bash
test_backend.bat
```

### 手动验证
1. 打开浏览器
2. 访问：http://localhost:3002/health
3. 应该看到：`{"status":"OK","timestamp":"..."}`

### 检查端口占用
```bash
netstat -an | findstr :3002
```

## 常见问题

### Q1: 为什么改了端口还是报3001错误？
A1: 可能的原因：
- 有缓存的Node.js进程还在运行
- 环境变量设置了固定端口
- 使用了错误的启动脚本

### Q2: 3002端口也被占用怎么办？
A2: 可以修改为其他端口：
1. 编辑`backend/server.js`第109行
2. 编辑`lib/services/api_service.dart`第7行
3. 使用相同的端口号

### Q3: 前端连接不上后端怎么办？
A3: 检查：
1. 后端是否正常启动（访问健康检查端点）
2. 前端API配置是否正确
3. 防火墙是否阻止了连接

## 启动脚本说明

### start_backend_force_3002.bat
- 强制使用3002端口
- 自动清理进程
- 最稳定的启动方式

### start_backend_3002.bat
- 简单启动脚本
- 不清理进程
- 适合正常情况

### backend/start.bat
- 原始启动脚本
- 现在也使用3002端口
- 包含完整的环境检查

## 调试信息

如果问题仍然存在，请收集以下信息：
1. 运行`test_backend.bat`的输出
2. 任务管理器中的Node.js进程列表
3. 错误日志的完整内容
4. 使用的启动命令

## 联系支持
如果以上方案都无法解决问题，请提供调试信息以获得进一步帮助。 