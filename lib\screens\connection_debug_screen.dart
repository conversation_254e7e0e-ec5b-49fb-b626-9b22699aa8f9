import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/connection_provider.dart';
import '../services/api_service.dart';
import '../widgets/common_widgets.dart';
import '../widgets/connection_status_widget.dart';

class ConnectionDebugScreen extends StatefulWidget {
  const ConnectionDebugScreen({Key? key}) : super(key: key);

  @override
  State<ConnectionDebugScreen> createState() => _ConnectionDebugScreenState();
}

class _ConnectionDebugScreenState extends State<ConnectionDebugScreen> {
  String _testResult = '';
  bool _isTesting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFF8FAFC),
          Color(0xFFE2E8F0),
        ],
        child: Column(
          children: [
            const IOSStatusBar(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFF1E293B),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                  ),
                  const Expanded(
                    child: Text(
                      '连接状态调试',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const ConnectionStatusWidget(
                    showDetails: false,
                    showRefreshButton: true,
                  ),
                ],
              ),
            ),
            const ConnectionBanner(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildConnectionStatusCard(),
                    const SizedBox(height: 24),
                    _buildDiagnosticsCard(),
                    const SizedBox(height: 24),
                    _buildTestCard(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatusCard() {
    return Consumer<ConnectionProvider>(
      builder: (context, connectionProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      connectionProvider.statusIcon,
                      color: connectionProvider.statusColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      '连接状态',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStatusRow('状态', connectionProvider.statusText, connectionProvider.statusColor),
                const SizedBox(height: 8),
                _buildStatusRow('重试次数', '${connectionProvider.retryCount}', Colors.grey[600]!),
                if (connectionProvider.lastConnectedTime != null) ...[
                  const SizedBox(height: 8),
                  _buildStatusRow(
                    '最后连接时间',
                    _formatTime(connectionProvider.lastConnectedTime!),
                    Colors.grey[600]!,
                  ),
                ],
                if (connectionProvider.errorMessage.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildStatusRow('错误信息', connectionProvider.errorMessage, Colors.red),
                ],
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => connectionProvider.refreshConnection(),
                    icon: const Icon(Icons.refresh),
                    label: const Text('刷新连接状态'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDiagnosticsCard() {
    return Consumer<ConnectionProvider>(
      builder: (context, connectionProvider, child) {
        final diagnostics = connectionProvider.getConnectionDiagnostics();
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '诊断信息',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                ...diagnostics.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: _buildStatusRow(
                      entry.key,
                      entry.value?.toString() ?? 'null',
                      Colors.grey[600]!,
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '连接测试',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTesting ? null : _testConnection,
                    icon: _isTesting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.network_check),
                    label: Text(_isTesting ? '测试中...' : '测试连接'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isTesting ? null : _testAPI,
                    icon: _isTesting 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.api),
                    label: Text(_isTesting ? '测试中...' : '测试API'),
                  ),
                ),
              ],
            ),
            if (_testResult.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _testResult,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(color: color),
          ),
        ),
      ],
    );
  }

  String _formatTime(DateTime time) {
    return '${time.month}/${time.day} ${time.hour}:${time.minute.toString().padLeft(2, '0')}:${time.second.toString().padLeft(2, '0')}';
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTesting = true;
      _testResult = '';
    });

    try {
      final apiService = ApiService();
      final startTime = DateTime.now();
      
      final result = await apiService.testConnection();
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      setState(() {
        _testResult = '''连接测试结果:
状态: ${result ? '成功' : '失败'}
耗时: ${duration.inMilliseconds}ms
测试时间: ${_formatTime(endTime)}
目标地址: ${ApiService.baseUrl}''';
      });
    } catch (e) {
      setState(() {
        _testResult = '''连接测试失败:
错误: $e
测试时间: ${_formatTime(DateTime.now())}''';
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  Future<void> _testAPI() async {
    setState(() {
      _isTesting = true;
      _testResult = '';
    });

    try {
      final apiService = ApiService();
      final startTime = DateTime.now();
      
      // 测试不同的API端点
      final results = <String, dynamic>{};
      
      try {
        await apiService.testConnection();
        results['基础连接'] = '成功';
      } catch (e) {
        results['基础连接'] = '失败: $e';
      }
      
      try {
        await apiService.getQuickQuestions();
        results['快捷问题API'] = '成功';
      } catch (e) {
        results['快捷问题API'] = '失败: $e';
      }
      
      try {
        await apiService.getPredictionHistory(limit: 1);
        results['预测历史API'] = '成功';
      } catch (e) {
        results['预测历史API'] = '失败: $e';
      }
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      final resultText = results.entries.map((e) => '${e.key}: ${e.value}').join('\n');
      
      setState(() {
        _testResult = '''API测试结果:
$resultText

总耗时: ${duration.inMilliseconds}ms
测试时间: ${_formatTime(endTime)}''';
      });
    } catch (e) {
      setState(() {
        _testResult = '''API测试失败:
错误: $e
测试时间: ${_formatTime(DateTime.now())}''';
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }
} 