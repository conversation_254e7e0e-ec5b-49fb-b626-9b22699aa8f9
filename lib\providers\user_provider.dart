import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';

class UserProvider with ChangeNotifier {
  UserModel _user = defaultUser;
  bool _isLoading = false;
  bool _isGuest = false;

  UserModel get user => _user;
  bool get isLoading => _isLoading;
  bool get isGuest => _isGuest;
  bool get isLoggedIn => _user.id != defaultUser.id;

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void updateUser(UserModel user) {
    _user = user;
    notifyListeners();
  }

  void updateUserInfo({
    String? nickname,
    String? avatar,
    String? memberLevel,
  }) {
    _user = _user.copyWith(
      nickname: nickname,
      avatar: avatar,
      memberLevel: memberLevel,
      lastActiveAt: DateTime.now(),
    );
    notifyListeners();
  }

  void incrementPredictionCount() {
    _user = _user.copyWith(
      predictionCount: _user.predictionCount + 1,
      lastActiveAt: DateTime.now(),
    );
    notifyListeners();
  }

  void incrementFavoriteCount() {
    _user = _user.copyWith(
      favoriteCount: _user.favoriteCount + 1,
      lastActiveAt: DateTime.now(),
    );
    notifyListeners();
  }

  void incrementConsultationCount() {
    _user = _user.copyWith(
      consultationCount: _user.consultationCount + 1,
      lastActiveAt: DateTime.now(),
    );
    notifyListeners();
  }

  void decrementFavoriteCount() {
    if (_user.favoriteCount > 0) {
      _user = _user.copyWith(
        favoriteCount: _user.favoriteCount - 1,
        lastActiveAt: DateTime.now(),
      );
      notifyListeners();
    }
  }

  String get memberStatusText {
    switch (_user.memberLevel) {
      case '游客':
        return '游客模式';
      case '普通会员':
        return '普通会员';
      case '白银会员':
        return '白银会员';
      case '黄金会员':
        return '黄金会员';
      case '钻石会员':
        return '钻石会员';
      default:
        return '普通会员';
    }
  }

  // 用户登录
  Future<bool> login(String username, String password) async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final response = await apiService.login(username, password);
      
      final userData = response['user'];
      _user = UserModel.fromJson(userData);
      
      setLoading(false);
      return true;
    } catch (e) {
      setLoading(false);
      return false;
    }
  }

  // 用户注册
  Future<bool> register(String username, String password) async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final response = await apiService.register(username, password);
      
      final userData = response['user'];
      _user = UserModel.fromJson(userData);
      
      setLoading(false);
      return true;
    } catch (e) {
      setLoading(false);
      return false;
    }
  }

  // 游客登录
  Future<bool> guestLogin() async {
    try {
      setLoading(true);
      
      // 设置游客用户信息
      _user = UserModel(
        id: 'guest_${DateTime.now().millisecondsSinceEpoch}',
        nickname: '游客用户',
        avatar: '',
        memberLevel: '游客',
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
        predictionCount: 0,
        favoriteCount: 0,
        consultationCount: 0,
      );
      
      _isGuest = true;
      setLoading(false);
      notifyListeners(); // 确保通知监听器
      return true;
    } catch (e) {
      setLoading(false);
      return false;
    }
  }

  // 用户退出
  Future<void> logout() async {
    try {
      // 如果不是游客，调用API退出
      if (!_isGuest) {
        final apiService = ApiService();
        await apiService.logout();
      }
    } catch (e) {
      // 即使API调用失败也要清除本地状态
      print('Logout API failed: $e');
    } finally {
      _user = defaultUser;
      _isGuest = false;
      notifyListeners();
    }
  }
  
  // 验证token并自动登录
  Future<bool> autoLogin() async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final response = await apiService.verifyToken();
      
      if (response['valid'] == true) {
        final userData = response['user'];
        _user = UserModel.fromJson(userData);
        setLoading(false);
        return true;
      }
      
      setLoading(false);
      return false;
    } catch (e) {
      setLoading(false);
      return false;
    }
  }
} 