enum ConstellationType {
  aries,      // 白羊座
  taurus,     // 金牛座
  gemini,     // 双子座
  cancer,     // 巨蟹座
  leo,        // 狮子座
  virgo,      // 处女座
  libra,      // 天秤座
  scorpio,    // 天蝎座
  sagittarius, // 射手座
  capricorn,  // 摩羯座
  aquarius,   // 水瓶座
  pisces,     // 双鱼座
}

class ConstellationFortune {
  final ConstellationType type;
  final String name;
  final String symbol;
  final String dateRange;
  final int overallRating;
  final int loveRating;
  final int moneyRating;
  final int careerRating;
  final int healthRating;
  final String luckyColor;
  final String luckyNumber;
  final String luckyDirection;
  final String luckyFood;
  final String summary;
  final String loveAdvice;
  final String moneyAdvice;
  final String careerAdvice;
  final String healthAdvice;
  final DateTime lastUpdated;
  final bool isAIGenerated;  // 新增字段

  ConstellationFortune({
    required this.type,
    required this.name,
    required this.symbol,
    required this.dateRange,
    required this.overallRating,
    required this.loveRating,
    required this.moneyRating,
    required this.careerRating,
    required this.healthRating,
    required this.luckyColor,
    required this.luckyNumber,
    required this.luckyDirection,
    required this.luckyFood,
    required this.summary,
    required this.loveAdvice,
    required this.moneyAdvice,
    required this.careerAdvice,
    required this.healthAdvice,
    required this.lastUpdated,
    this.isAIGenerated = false,  // 新增字段，默认为false
  });

  factory ConstellationFortune.fromJson(Map<String, dynamic> json) {
    return ConstellationFortune(
      type: ConstellationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ConstellationType.aries,
      ),
      name: json['name'] ?? '未知星座',
      symbol: json['symbol'] ?? '♈',
      dateRange: json['dateRange'] ?? '日期范围未知',
      overallRating: json['overallRating'] ?? 3,
      loveRating: json['loveRating'] ?? 3,
      moneyRating: json['moneyRating'] ?? 3,
      careerRating: json['careerRating'] ?? 3,
      healthRating: json['healthRating'] ?? 3,
      luckyColor: json['luckyColor'] ?? '紫色',
      luckyNumber: json['luckyNumber']?.toString() ?? '7',
      luckyDirection: json['luckyDirection'] ?? '东方',
      luckyFood: json['luckyFood'] ?? '水果',
      summary: json['summary'] ?? '今日运势平稳，保持积极心态，会有不错的收获。',
      loveAdvice: json['loveAdvice'] ?? '感情方面要多沟通，增进彼此了解。',
      moneyAdvice: json['moneyAdvice'] ?? '财运方面需要谨慎理财，避免冲动消费。',
      careerAdvice: json['careerAdvice'] ?? '工作上要保持积极态度，认真对待每一项任务。',
      healthAdvice: json['healthAdvice'] ?? '注意身体健康，适当运动，保持良好作息。',
      lastUpdated: json['lastUpdated'] != null ? DateTime.parse(json['lastUpdated']) : DateTime.now(),
      isAIGenerated: json['isAIGenerated'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'name': name,
      'symbol': symbol,
      'dateRange': dateRange,
      'overallRating': overallRating,
      'loveRating': loveRating,
      'moneyRating': moneyRating,
      'careerRating': careerRating,
      'healthRating': healthRating,
      'luckyColor': luckyColor,
      'luckyNumber': luckyNumber,
      'luckyDirection': luckyDirection,
      'luckyFood': luckyFood,
      'summary': summary,
      'loveAdvice': loveAdvice,
      'moneyAdvice': moneyAdvice,
      'careerAdvice': careerAdvice,
      'healthAdvice': healthAdvice,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isAIGenerated': isAIGenerated,  // 新增字段
    };
  }

  ConstellationFortune copyWith({
    ConstellationType? type,
    String? name,
    String? symbol,
    String? dateRange,
    int? overallRating,
    int? loveRating,
    int? moneyRating,
    int? careerRating,
    int? healthRating,
    String? luckyColor,
    String? luckyNumber,
    String? luckyDirection,
    String? luckyFood,
    String? summary,
    String? loveAdvice,
    String? moneyAdvice,
    String? careerAdvice,
    String? healthAdvice,
    DateTime? lastUpdated,
    bool? isAIGenerated,  // 新增字段
  }) {
    return ConstellationFortune(
      type: type ?? this.type,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      dateRange: dateRange ?? this.dateRange,
      overallRating: overallRating ?? this.overallRating,
      loveRating: loveRating ?? this.loveRating,
      moneyRating: moneyRating ?? this.moneyRating,
      careerRating: careerRating ?? this.careerRating,
      healthRating: healthRating ?? this.healthRating,
      luckyColor: luckyColor ?? this.luckyColor,
      luckyNumber: luckyNumber ?? this.luckyNumber,
      luckyDirection: luckyDirection ?? this.luckyDirection,
      luckyFood: luckyFood ?? this.luckyFood,
      summary: summary ?? this.summary,
      loveAdvice: loveAdvice ?? this.loveAdvice,
      moneyAdvice: moneyAdvice ?? this.moneyAdvice,
      careerAdvice: careerAdvice ?? this.careerAdvice,
      healthAdvice: healthAdvice ?? this.healthAdvice,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isAIGenerated: isAIGenerated ?? this.isAIGenerated,  // 新增字段
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConstellationFortune && other.type == type;
  }

  @override
  int get hashCode => type.hashCode;

  @override
  String toString() {
    return 'ConstellationFortune(type: $type, name: $name, overallRating: $overallRating)';
  }
}

// 星座数据生成器
class ConstellationData {
  static List<ConstellationFortune> generateAllConstellations() {
    final now = DateTime.now();
    return [
      ConstellationFortune(
        type: ConstellationType.aries,
        name: '白羊座',
        symbol: '♈',
        dateRange: '3月21日 - 4月19日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '红色',
        luckyNumber: '7',
        luckyDirection: '东方',
        luckyFood: '辣椒',
        summary: '今日运势较好，适合主动出击',
        loveAdvice: '感情方面要主动表达，会有好结果',
        moneyAdvice: '财运不错，但要避免冲动消费',
        careerAdvice: '工作上要展现领导力，会有机会',
        healthAdvice: '注意控制情绪，避免过度激动',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.taurus,
        name: '金牛座',
        symbol: '♉',
        dateRange: '4月20日 - 5月20日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '绿色',
        luckyNumber: '6',
        luckyDirection: '西方',
        luckyFood: '牛肉',
        summary: '稳定发展，财运较好',
        loveAdvice: '感情稳定，适合长期规划',
        moneyAdvice: '适合投资理财，收益稳定',
        careerAdvice: '踏实工作，会有回报',
        healthAdvice: '注意饮食健康，避免暴饮暴食',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.gemini,
        name: '双子座',
        symbol: '♊',
        dateRange: '5月21日 - 6月21日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '黄色',
        luckyNumber: '3',
        luckyDirection: '北方',
        luckyFood: '坚果',
        summary: '思维活跃，适合创新',
        loveAdvice: '多与伴侣沟通，增进理解',
        moneyAdvice: '理财要灵活，把握机会',
        careerAdvice: '发挥沟通优势，拓展人脉',
        healthAdvice: '注意休息，避免过度用脑',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.cancer,
        name: '巨蟹座',
        symbol: '♋',
        dateRange: '6月22日 - 7月22日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '银色',
        luckyNumber: '2',
        luckyDirection: '南方',
        luckyFood: '海鲜',
        summary: '情感丰富，直觉敏锐',
        loveAdvice: '用心感受，真诚待人',
        moneyAdvice: '理财保守，注重安全',
        careerAdvice: '发挥责任心，获得认可',
        healthAdvice: '注意情绪管理，保持平和',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.leo,
        name: '狮子座',
        symbol: '♌',
        dateRange: '7月23日 - 8月22日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '金色',
        luckyNumber: '1',
        luckyDirection: '东南',
        luckyFood: '橙子',
        summary: '自信满满，领导力强',
        loveAdvice: '展现魅力，吸引注意',
        moneyAdvice: '投资大胆，但要谨慎',
        careerAdvice: '承担责任，展现才华',
        healthAdvice: '注意心脏健康，适度运动',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.virgo,
        name: '处女座',
        symbol: '♍',
        dateRange: '8月23日 - 9月22日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '蓝色',
        luckyNumber: '9',
        luckyDirection: '西北',
        luckyFood: '蔬菜',
        summary: '细致入微，完美主义',
        loveAdvice: '放松心态，享受感情',
        moneyAdvice: '精打细算，理财有道',
        careerAdvice: '追求完美，注重细节',
        healthAdvice: '注意肠胃健康，规律饮食',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.libra,
        name: '天秤座',
        symbol: '♎',
        dateRange: '9月23日 - 10月23日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '粉色',
        luckyNumber: '4',
        luckyDirection: '西南',
        luckyFood: '水果',
        summary: '平衡协调，人际和谐',
        loveAdvice: '寻求平衡，和谐相处',
        moneyAdvice: '理财均衡，分散投资',
        careerAdvice: '发挥协调能力，促进合作',
        healthAdvice: '注意腰部健康，保持平衡',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.scorpio,
        name: '天蝎座',
        symbol: '♏',
        dateRange: '10月24日 - 11月22日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '紫色',
        luckyNumber: '8',
        luckyDirection: '东北',
        luckyFood: '巧克力',
        summary: '神秘深沉，洞察力强',
        loveAdvice: '深入了解，真诚相待',
        moneyAdvice: '投资眼光独到，收益可期',
        careerAdvice: '发挥洞察力，把握机会',
        healthAdvice: '注意生殖系统健康，定期检查',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.sagittarius,
        name: '射手座',
        symbol: '♐',
        dateRange: '11月23日 - 12月21日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '橙色',
        luckyNumber: '5',
        luckyDirection: '正南',
        luckyFood: '肉类',
        summary: '自由奔放，乐观向上',
        loveAdvice: '保持自由，享受恋爱',
        moneyAdvice: '投资多元，把握机会',
        careerAdvice: '追求理想，勇于冒险',
        healthAdvice: '注意大腿健康，多做运动',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.capricorn,
        name: '摩羯座',
        symbol: '♑',
        dateRange: '12月22日 - 1月19日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '黑色',
        luckyNumber: '10',
        luckyDirection: '正北',
        luckyFood: '鱼类',
        summary: '踏实稳重，目标明确',
        loveAdvice: '认真对待，长期规划',
        moneyAdvice: '稳健投资，积累财富',
        careerAdvice: '脚踏实地，步步为营',
        healthAdvice: '注意骨骼健康，补充钙质',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.aquarius,
        name: '水瓶座',
        symbol: '♒',
        dateRange: '1月20日 - 2月18日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '青色',
        luckyNumber: '11',
        luckyDirection: '正东',
        luckyFood: '茶叶',
        summary: '独立创新，思维独特',
        loveAdvice: '保持个性，寻找知音',
        moneyAdvice: '投资新兴领域，前景看好',
        careerAdvice: '发挥创新能力，引领潮流',
        healthAdvice: '注意血液循环，多做有氧运动',
        lastUpdated: now,
        isAIGenerated: false,
      ),
      ConstellationFortune(
        type: ConstellationType.pisces,
        name: '双鱼座',
        symbol: '♓',
        dateRange: '2月19日 - 3月20日',
        overallRating: _randomRating(),
        loveRating: _randomRating(),
        moneyRating: _randomRating(),
        careerRating: _randomRating(),
        healthRating: _randomRating(),
        luckyColor: '海蓝色',
        luckyNumber: '12',
        luckyDirection: '正西',
        luckyFood: '海带',
        summary: '温柔浪漫，直觉敏锐',
        loveAdvice: '用心感受，浪漫相伴',
        moneyAdvice: '理财需谨慎，避免被骗',
        careerAdvice: '发挥想象力，创造价值',
        healthAdvice: '注意足部健康，防止受寒',
        lastUpdated: now,
        isAIGenerated: false,
      ),
    ];
  }

  static int _randomRating() {
    return 1 + DateTime.now().millisecondsSinceEpoch % 5;
  }

  static String getConstellationSymbol(ConstellationType type) {
    switch (type) {
      case ConstellationType.aries:
        return '♈';
      case ConstellationType.taurus:
        return '♉';
      case ConstellationType.gemini:
        return '♊';
      case ConstellationType.cancer:
        return '♋';
      case ConstellationType.leo:
        return '♌';
      case ConstellationType.virgo:
        return '♍';
      case ConstellationType.libra:
        return '♎';
      case ConstellationType.scorpio:
        return '♏';
      case ConstellationType.sagittarius:
        return '♐';
      case ConstellationType.capricorn:
        return '♑';
      case ConstellationType.aquarius:
        return '♒';
      case ConstellationType.pisces:
        return '♓';
    }
  }

  static String getConstellationName(ConstellationType type) {
    switch (type) {
      case ConstellationType.aries:
        return '白羊座';
      case ConstellationType.taurus:
        return '金牛座';
      case ConstellationType.gemini:
        return '双子座';
      case ConstellationType.cancer:
        return '巨蟹座';
      case ConstellationType.leo:
        return '狮子座';
      case ConstellationType.virgo:
        return '处女座';
      case ConstellationType.libra:
        return '天秤座';
      case ConstellationType.scorpio:
        return '天蝎座';
      case ConstellationType.sagittarius:
        return '射手座';
      case ConstellationType.capricorn:
        return '摩羯座';
      case ConstellationType.aquarius:
        return '水瓶座';
      case ConstellationType.pisces:
        return '双鱼座';
    }
  }

  static String getConstellationDateRange(ConstellationType type) {
    switch (type) {
      case ConstellationType.aries:
        return '3月21日 - 4月19日';
      case ConstellationType.taurus:
        return '4月20日 - 5月20日';
      case ConstellationType.gemini:
        return '5月21日 - 6月21日';
      case ConstellationType.cancer:
        return '6月22日 - 7月22日';
      case ConstellationType.leo:
        return '7月23日 - 8月22日';
      case ConstellationType.virgo:
        return '8月23日 - 9月22日';
      case ConstellationType.libra:
        return '9月23日 - 10月23日';
      case ConstellationType.scorpio:
        return '10月24日 - 11月22日';
      case ConstellationType.sagittarius:
        return '11月23日 - 12月21日';
      case ConstellationType.capricorn:
        return '12月22日 - 1月19日';
      case ConstellationType.aquarius:
        return '1月20日 - 2月18日';
      case ConstellationType.pisces:
        return '2月19日 - 3月20日';
    }
  }
} 