import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chat_message.dart';
import '../models/user_model.dart';
import '../config.dart' as config;

class ApiService {
  late final Dio _dio;
  
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  
  // 添加baseUrl getter
  static String get baseUrl => config.baseUrl;
  
  ApiService._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: config.baseUrl,
      connectTimeout: const Duration(minutes: 10), // 增加到10分钟
      receiveTimeout: const Duration(minutes: 10), // 增加到10分钟
      sendTimeout: const Duration(minutes: 10), // 增加发送超时
      headers: {
        'Content-Type': 'application/json',
      },
    ));
    
    // 添加请求拦截器，自动添加token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) {
        print('API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }
  
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }
  
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
  }
  
  Future<void> _removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
  }
  
  // 认证相关API
  Future<Map<String, dynamic>> register(String nickname, String password) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'nickname': nickname,
        'password': password,
      });
      
      final token = response.data['token'];
      if (token != null) {
        await _saveToken(token);
      }
      
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '注册失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> login(String nickname, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'nickname': nickname,
        'password': password,
      });
      
      final token = response.data['token'];
      if (token != null) {
        await _saveToken(token);
      }
      
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '登录失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
      await _removeToken();
    } catch (e) {
      // 即使请求失败也要清除本地token
      await _removeToken();
    }
  }
  
  Future<Map<String, dynamic>> verifyToken() async {
    try {
      final response = await _dio.get('/auth/verify');
      return response.data;
    } catch (e) {
      await _removeToken();
      rethrow;
    }
  }
  
  // 聊天相关API
  Future<List<ChatMessage>> getChatHistory({int limit = 50}) async {
    try {
      final response = await _dio.get('/chat/history', queryParameters: {
        'limit': limit,
      });
      
      final List<dynamic> messagesData = response.data;
      return messagesData.map((data) => ChatMessage.fromJson(data)).toList();
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取聊天记录失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, ChatMessage>> sendMessage(String content) async {
    int retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount < maxRetries) {
      try {
        final response = await _dio.post('/chat/message', 
          data: {
            'content': content,
          },
          options: Options(
            sendTimeout: const Duration(seconds: 120),
            receiveTimeout: const Duration(seconds: 180),
          ),
        );
        
        final userMessageData = response.data['userMessage'];
        final aiResponseData = response.data['aiResponse'];
        
        return {
          'user': ChatMessage.fromJson(userMessageData),
          'ai': ChatMessage.fromJson(aiResponseData),
        };
      } catch (e) {
        retryCount++;
        if (retryCount >= maxRetries) {
          if (e is DioException) {
            throw Exception(e.response?.data['error']['message'] ?? '发送消息失败');
          }
          throw Exception('网络错误，请检查连接');
        }
        
        // 等待一段时间后重试
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }
    
    throw Exception('发送消息失败，已达到最大重试次数');
  }

  // 添加流式消息发送支持
  Future<Stream<String>> sendMessageStream(String content) async {
    try {
      final response = await _dio.post('/chat/message-stream', 
        data: {
          'content': content,
        },
        options: Options(
          sendTimeout: const Duration(seconds: 120),
          receiveTimeout: const Duration(seconds: 180),
          responseType: ResponseType.stream,
        ),
      );
      
      return response.data.stream.map((data) => String.fromCharCodes(data));
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '流式发送失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<void> clearChatHistory() async {
    try {
      await _dio.delete('/chat/history');
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '清空聊天记录失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<List<Map<String, dynamic>>> getQuickQuestions() async {
    try {
      final response = await _dio.get('/chat/quick-questions');
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取快捷问题失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 星座相关API
  Future<List<Map<String, dynamic>>> getConstellations() async {
    try {
      final response = await _dio.get('/constellations');
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取星座数据失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> getConstellationFortune(String constellationType) async {
    try {
      final response = await _dio.get('/constellations/$constellationType');
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取星座运势失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 新增: 获取AI生成的星座运势
  Future<Map<String, dynamic>> getAIConstellationFortune(String constellationType) async {
    try {
      print('🚀 请求AI星座运势: $constellationType');
      final response = await _dio.get(
        '/constellations/$constellationType/ai-fortune',
        options: Options(
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
        ),
      );
      print('✅ AI星座运势请求成功');
      return response.data;
    } catch (e) {
      print('❌ AI星座运势请求失败: $e');
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取AI星座运势失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 预测相关API
  Future<List<Map<String, dynamic>>> getPredictionHistory({int limit = 50}) async {
    try {
      final response = await _dio.get('/predictions/history', queryParameters: {
        'limit': limit,
      });
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取预测历史失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 八字预测 - 支持AI解读
  Future<Map<String, dynamic>> createBaziPrediction(Map<String, dynamic> inputData) async {
    try {
      print('🔮 发送八字预测请求...');
      print('📦 输入数据: $inputData');
      
      final response = await _dio.post('/predictions/bazi', 
        data: inputData,
        options: Options(
          sendTimeout: const Duration(seconds: 120), // 增加超时时间以支持AI解读
          receiveTimeout: const Duration(seconds: 180),
        ),
      );
      
      print('✅ 八字预测请求成功');
      if (response.data != null && response.data['result'] != null) {
        print('📊 返回结果包含AI解读: ${response.data['result']['aiAnalysis'] != null}');
      }
      
      return response.data;
    } catch (e) {
      print('❌ 八字预测请求失败: $e');
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '八字预测失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> createZiweiPrediction(Map<String, dynamic> inputData) async {
    try {
      print('🔮 发送紫微斗数预测请求...');
      print('📦 输入数据: $inputData');
      
      final response = await _dio.post('/predictions/ziwei', 
        data: inputData,
        options: Options(
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 90),
        ),
      );
      
      print('✅ 紫微斗数预测请求成功');
      print('📊 响应数据结构检查:');
      print('   - 包含result: ${response.data.containsKey('result')}');
      print('   - 包含record: ${response.data.containsKey('record')}');
      
      if (response.data.containsKey('result')) {
        final result = response.data['result'];
        print('   - result包含palaces: ${result.containsKey('palaces')}');
        print('   - result包含analysis: ${result.containsKey('analysis')}');
        
        if (result.containsKey('palaces')) {
          final palaces = result['palaces'];
          print('   - palaces是List类型: ${palaces is List}');
          print('   - palaces长度: ${palaces is List ? palaces.length : 'N/A'}');
          if (palaces is List && palaces.isNotEmpty) {
            print('   - 第一个宫位示例: ${palaces[0]}');
          }
        }
      }
      
      return response.data;
    } catch (e) {
      print('❌ 紫微斗数预测请求失败: $e');
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '紫微预测失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> createLiuyaoPrediction(Map<String, dynamic> inputData) async {
    try {
      final response = await _dio.post('/predictions/liuyao', 
        data: inputData,
        options: Options(
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 90),
        ),
      );
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '六爻预测失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> updatePredictionRecord(String id, Map<String, dynamic> updateData) async {
    try {
      final response = await _dio.put('/predictions/$id', data: updateData);
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '更新预测记录失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<void> deletePredictionRecord(String id) async {
    try {
      await _dio.delete('/predictions/$id');
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '删除预测记录失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> togglePredictionFavorite(String id) async {
    try {
      final response = await _dio.put('/predictions/$id/favorite');
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '收藏操作失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 用户相关API
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await _dio.get('/user/profile');
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取用户信息失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await _dio.put('/user/profile', data: profileData);
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '更新用户信息失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final response = await _dio.get('/user/stats');
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '获取用户统计失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }
  
  // 测试连接
  Future<bool> testConnection() async {
    try {
      final response = await _dio.get('/chat/status', options: Options(
        sendTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 5),
      ));
      
      // 检查响应状态
      if (response.statusCode == 200) {
        final data = response.data;
        // 如果返回的是状态信息，检查状态
        if (data is Map<String, dynamic>) {
          final status = data['status'];
          print('Backend response: $data');
          // 无论AI服务是否可用，只要后端响应就认为连接成功
          return true;
        }
        return true;
      }
      return false;
    } catch (e) {
      print('Connection test failed: $e');
      return false;
    }
  }

  // 测试连接的详细版本
  Future<Map<String, dynamic>> testConnectionDetailed() async {
    final startTime = DateTime.now();
    
    try {
      final response = await _dio.get('/chat/status', options: Options(
        sendTimeout: const Duration(seconds: 5),
        receiveTimeout: const Duration(seconds: 5),
      ));
      
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      return {
        'success': true,
        'statusCode': response.statusCode,
        'duration': duration.inMilliseconds,
        'timestamp': endTime.toIso8601String(),
        'baseUrl': ApiService.baseUrl,
      };
    } catch (e) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      
      return {
        'success': false,
        'error': e.toString(),
        'duration': duration.inMilliseconds,
        'timestamp': endTime.toIso8601String(),
        'baseUrl': ApiService.baseUrl,
      };
    }
  }

  // 获取今日运势
  Future<Map<String, dynamic>> getDailyFortune() async {
    try {
      final response = await _dio.get('/chat/daily-fortune',
        options: Options(
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 60),
        ),
      );
      
      if (response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw Exception('获取今日运势失败');
      }
    } catch (e) {
      if (e is DioException) {
        // 如果是401错误（未认证），抛出特殊异常以便上层处理
        if (e.response?.statusCode == 401) {
          throw Exception('API Error: This exception was thrown because the response has a status code of 401');
        }
        throw Exception(e.response?.data['error']['message'] ?? '获取今日运势失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }

  // 刷新今日运势（管理员功能）
  Future<Map<String, dynamic>> refreshDailyFortune() async {
    try {
      final response = await _dio.post('/chat/daily-fortune/refresh',
        options: Options(
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 90),
        ),
      );
      
      if (response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw Exception('刷新今日运势失败');
      }
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '刷新今日运势失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }

  // 保存六爻预测结果
  Future<Map<String, dynamic>> saveLiuyaoResult(String recordId, {String? notes, List<String>? tags}) async {
    try {
      final response = await _dio.post('/predictions/liuyao/$recordId/save', data: {
        'notes': notes ?? '',
        'tags': tags ?? [],
      });
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '保存六爻结果失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }

  // 基于六爻卦象的AI咨询
  Future<Map<String, dynamic>> consultLiuyaoWithAI(String recordId, String question) async {
    try {
      final response = await _dio.post('/predictions/liuyao/$recordId/ai-consult', 
        data: {
          'question': question,
        },
        options: Options(
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 90),
        ),
      );
      return response.data;
    } catch (e) {
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '六爻AI咨询失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }

  // 开始八字深度咨询
  Future<Map<String, dynamic>> startBaziConsultation({
    required Map<String, dynamic> baziContext,
    String? initialQuestion,
  }) async {
    try {
      print('🤖 开始八字深度咨询...');
      print('📋 八字上下文: $baziContext');
      
      final response = await _dio.post('/api/chat/bazi-consultation', data: {
        'baziContext': baziContext,
        'initialQuestion': initialQuestion ?? '',
      });

      print('✅ 八字深度咨询开始成功');
      return response.data;
    } catch (e) {
      print('❌ 八字深度咨询开始失败: $e');
      if (e is DioException) {
        throw Exception(e.response?.data['error']['message'] ?? '开始八字深度咨询失败');
      }
      throw Exception('网络错误，请检查连接');
    }
  }

  // 星座+紫微深度分析
  Future<Map<String, dynamic>> getConstellationZiweiAnalysis(Map<String, dynamic> inputData) async {
    try {
      print('🔮 请求星座+紫微深度分析...');
      print('📦 输入数据: $inputData');
      
      // 获取星座类型并转换为后端API格式
      final constellation = inputData['constellation'] ?? '';
      String constellationType = 'aries'; // 默认为白羊座
      
      // 将中文星座名转换为API需要的类型格式
      switch (constellation) {
        case '白羊座':
          constellationType = 'aries';
          break;
        case '金牛座':
          constellationType = 'taurus';
          break;
        case '双子座':
          constellationType = 'gemini';
          break;
        case '巨蟹座':
          constellationType = 'cancer';
          break;
        case '狮子座':
          constellationType = 'leo';
          break;
        case '处女座':
          constellationType = 'virgo';
          break;
        case '天秤座':
          constellationType = 'libra';
          break;
        case '天蝎座':
          constellationType = 'scorpio';
          break;
        case '射手座':
          constellationType = 'sagittarius';
          break;
        case '摩羯座':
          constellationType = 'capricorn';
          break;
        case '水瓶座':
          constellationType = 'aquarius';
          break;
        case '双鱼座':
          constellationType = 'pisces';
          break;
      }
      
      print('🌟 转换后的星座类型: $constellationType');
      
      try {
        // 调用后端API
        print('📤 发起API请求: /constellations/$constellationType/ai-fortune');
        final response = await _dio.post(
          '/constellations/$constellationType/ai-fortune',
          data: {
            ...inputData,
            'analysisType': 'combo',
            'includeZiwei': true,
            'contentLength': 'detailed', // 要求AI生成详细内容
            'analysisRequirements': {
              'minWordsPerField': 100, // 每个领域至少100字
              'detailLevel': 'high', // 高详细度
              'includeExamples': true, // 包含具体例子
              'includeActionableAdvice': true, // 包含可执行的建议
              'tone': 'professional', // 专业语气
              'totalLength': 1000 // 总体字数需求
            }
          },
          options: Options(
            // 增加超时时间到10分钟，因为AI模型可能需要较长时间处理
            sendTimeout: const Duration(minutes: 10),
            receiveTimeout: const Duration(minutes: 10),
          ),
        );
        
        print('📥 API响应状态码: ${response.statusCode}');
        
        // 检查响应数据格式
        if (response.data == null) {
          print('❌ API响应数据为null');
          throw Exception('API返回空数据');
        }
        
        // 打印响应数据的部分内容，避免日志过大
        print('📄 响应数据格式检查:');
        print('- 年运数据: ${response.data.containsKey('yearlyFortune')}');
        print('- 月运数据: ${response.data.containsKey('monthlyFortune')}');
        print('- 日运数据: ${response.data.containsKey('dailyFortune')}');
        print('- 总结: ${response.data.containsKey('summary')}');
        
        // 如果后端返回的不是预期格式，自动转换
        if (!response.data.containsKey('yearlyFortune') && response.data.containsKey('rawResponse')) {
          print('⚠️ 响应数据格式不符合预期，尝试解析原始响应');
          final rawResponse = response.data['rawResponse'];
          
          // 构造模拟数据结构
          return {
            'yearlyFortune': {
              'summary': '基于$constellation的年运分析，结合紫微斗数，${DateTime.now().year}年整体运势平稳。通过西方占星与东方紫微的结合，您将迎来一个充满变化与机遇的年份。本年度木星与土星相位特殊，为您带来事业与人际关系的双重发展机会。紫微斗数显示，流年太岁与您的命盘形成良好相位，有利于长期计划的实施。',
              'career': '事业方面，${DateTime.now().year}年是您职业发展的关键时期。上半年可能面临工作调整或转型机会，这源于木星在您的事业宫形成有利相位。紫微斗数显示，文昌星入命，利于学习新技能和知识拓展。4-6月是职场升迁或项目突破的黄金期，建议把握机会主动展示自己的能力和创意。下半年则需谨慎处理与同事关系，避免因沟通不畅导致合作受阻。年底前，可能收到意料之外的工作机会，这与您过去的努力积累密切相关。建议保持专业技能更新，增强职场竞争力，同时适当参与团队建设活动，提升个人影响力。',
              'love': '感情生活方面，${DateTime.now().year}年呈现波动上升态势。金星在您的感情宫停留时间较长，为单身的$constellation带来多次邂逅良机，特别是在3月和9月。已有伴侣的您，上半年可能因工作繁忙导致关系略显紧张，建议增加共处时光，加强情感交流。紫微斗数显示，天姚星飞入夫妻宫，暗示可能有第三者或家庭外部因素干扰感情稳定，需提高警惕。7-8月是感情升温的好时机，适合规划浪漫旅行或重要仪式。年末则是修复关系、深化感情的理想时期。建议保持真诚沟通，避免将工作压力带入感情生活，同时学会倾听伴侣需求，共同成长。',
              'money': '财务状况方面，${DateTime.now().year}年整体稳健但需谨慎规划。上半年，土星的影响使您对财务管理更加理性，适合进行长期投资规划和保险配置。紫微斗数显示，财帛宫有禄存入驻，基础收入稳定，但需防守不必要的冲动消费。5-7月可能出现额外收入机会，与个人专业技能或副业发展有关。下半年财运上扬，尤其是9-11月，适合考虑房产投资或资产配置调整。然而，年底前需警惕因信息不足导致的投资风险，建议多方考察后再做决定。整体而言，应建立完善的财务记录系统，控制生活开支，增加被动收入来源，为未来做好财务储备。',
              'health': '健康状况总体良好，但需关注工作与生活的平衡。上半年，火星影响使您精力充沛但易紧张，建议通过有规律的运动缓解压力，尤其适合水中运动或有氧训练。紫微斗数显示，今年疾厄宫受太阴星影响，女性需特别关注内分泌系统健康，男性则应注意消化系统问题。6-8月是健康的敏感期，避免过度劳累和情绪波动，保持充足睡眠。下半年身体状况趋于稳定，但10月后因季节变化，呼吸系统和免疫力需额外关注。建议定期体检，特别关注心脏健康和血压变化，坚持健康饮食习惯，增加蛋白质和维生素摄入，减少刺激性食物消费。冥想或太极等放松活动有助于维持身心平衡。',
              'social': '人际关系和社交活动将在${DateTime.now().year}年显著活跃。水星在您的交友宫停留时间延长，有利于扩展人脉网络，尤其是在专业领域。紫微斗数显示，今年的交友宫得贵人星相助，容易获得重要人士的支持和引荐。2-4月是建立新人脉的黄金期，工作相关的社交活动可能带来意外收获。中期5-8月，旧友重聚可能唤起合作灵感，建议参与同学会或行业聚会。下半年社交圈可能因个人兴趣爱好而扩展，有利于平衡工作关系和个人友谊。然而，需警惕在10-11月可能出现的人际矛盾，避免卷入不必要的纠纷。建议主动维护重要关系，定期联系核心人脉，同时学会在社交场合中展示专业价值，提升个人品牌影响力。',
              'advice': '综合分析您的星座特质和紫微斗数，${DateTime.now().year}年是调整与发展并重的一年。首先，建立明确的年度目标，将其分解为季度和月度小目标，有助于把握机遇。其次，工作上保持积极主动但避免过度承诺，合理评估自己的能力和时间。在人际关系方面，学会区分核心圈和外围圈，将精力投入最有价值的关系中。财务上建议"三分法"：生活所需、紧急备用和长期投资各占一定比例。健康管理上，建立规律作息，每周至少保证3-4次运动，学习一门减压技能如瑜伽或冥想。感情生活中，表达需求的同时也要倾听对方，定期安排二人世界的优质时光。最后，每季度进行一次自我评估，及时调整计划，确保年度目标逐步实现。保持开放心态，积极拥抱变化，这将是收获丰盛的一年。',
            },
            'monthlyFortune': {
              'summary': '本月运势整体呈现先稳后升的趋势，月初可能面临一些挑战，但月中后期将逐渐转好。受月亮相位变化影响，情绪波动较大，需注意自我调节。紫微斗数显示，本月大运与流月相位和谐，有利于长期项目的推进和人际关系的改善。本月适合处理细节性工作，不宜大刀阔斧进行重大变革。',
              'career': '职业发展方面，本月上旬可能面临工作任务集中或项目截止日期临近的压力，工作节奏较快。中旬开始有转机，主管或团队成员的支持将帮助您克服困难。太阳在您的事业宫形成有利相位，提升了您的工作能见度，有机会在重要会议或项目中崭露头角。紫微斗数显示，文曲星入命，利于文案策划、创意思考类工作，但同时需要注意细节审核，避免因小失大。第三周是展示专业能力的关键期，可能收到上级的积极评价或新的职责委派。月底前适合为下月工作做准备，梳理项目进度，制定清晰计划。建议着重提升沟通协调能力，多与跨部门同事交流，发现合作机会，同时保持对行业动态的敏感度，适应市场变化。',
              'love': '感情方面，本月整体平稳但需要更多用心经营。金星在您的伴侣宫形成轻微紧张相位，可能导致沟通不畅或期望落差。单身的$constellation可能在月中通过工作或朋友介绍认识新朋友，但不宜急于确定关系，建议多了解对方价值观和生活习惯。已有伴侣的您，上旬因工作繁忙可能疏于陪伴，引起小摩擦，中旬是修复关系的好时机，可安排一次私密约会或深入交流。紫微斗数显示，月老星临桃花位，感情中容易受到第三方意见影响，建议保持独立判断。月底前，坦诚表达自己的需求和顾虑，避免情绪积累。建议学习伴侣的爱情语言，通过对方能理解的方式表达关爱，同时给予彼此适当空间，保持关系的新鲜感和独立性。',
              'money': '财务状况本月需要谨慎管理，上旬可能面临一些意外开支，如设备维修或健康相关费用。水星逆行影响财务决策，不适合在前两周进行大额投资或签署重要财务合同。中旬开始财运回升，工作收入稳定，可能有奖金或项目提成入账。紫微斗数显示，财帛宫有禄存，基础收入有保障，但破耗星同宫，警惕冲动消费。月底前适合重新评估月度预算，调整支出结构。投资方面，固定收益类产品较为适合本月配置，股票等波动性大的资产需谨慎。建议建立详细的收支记录，识别并减少非必要开支，增加储蓄比例，为未来大额支出做准备。同时，考虑提升专业技能，为收入增长创造条件。',
              'health': '健康状况需要特别关注，本月星象显示免疫系统可能较为脆弱。上旬工作压力较大，易导致睡眠质量下降，建议调整作息，保证每晚7-8小时充足睡眠。紫微斗数显示，疾厄宫受七杀星影响，容易出现肌肉紧张、头痛等与压力相关的身体不适。中旬是调整健康习惯的好时机，建议增加户外活动时间，每天保证30分钟以上中等强度运动。饮食方面，需增加蔬果摄入，减少加工食品和咖啡因消费。月底前天气变化较大，注意及时增减衣物，预防感冒。心理健康同样重要，可尝试冥想或深呼吸等放松技巧，缓解焦虑情绪。定期休息是本月健康关键，工作间隙站起来活动，避免长时间保持同一姿势，保护颈椎和腰部。',
              'social': '社交活动本月呈现质重于量的特点。上旬因工作忙碌，社交活动可能减少，但现有关系将得到深化。水星在您的沟通宫形成有利相位，有助于表达观点和理解他人，适合进行一对一深度交流。中旬开始，朋友圈活动增多，可能收到聚会或小型活动邀请，参与将带来意外收获。紫微斗数显示，贵人星临友谊宫，有机会结识行业前辈或影响力人士。第三周适合参加行业研讨会或社区活动，扩展专业网络。月底前，重新联系久未沟通的朋友，可能发现合作机会。建议在社交场合展现真实自我，避免过度迎合，同时保持积极倾听的态度，了解他人需求，建立互惠关系。适当筛选社交活动，将时间投入到能带来长期价值的关系中。',
              'advice': '本月综合建议是"稳中求进，细节为王"。首先，工作上保持专注，将大项目分解为可管理的小任务，逐一攻克，避免被整体压力overwhelm。时间管理至关重要，建议使用番茄工作法，集中精力处理任务，定时休息恢复能量。人际关系方面，质量胜于数量，维护核心关系的同时，对新接触保持开放但谨慎的态度。财务上采取保守策略，控制非必要支出，增加应急资金储备。健康管理需建立规律作息，每晚10-11点前入睡，配合均衡饮食和适度运动。情绪调节同样重要，可以通过写日记或与信任的朋友分享来疏导压力。最后，建议周末安排完全放松的活动，彻底断开工作连接，让身心得到充分恢复。保持耐心和弹性，本月的挑战将为未来的发展奠定基础。',
            },
            'dailyFortune': {
              'summary': '今日整体运势相当不错，是处理重要事务和推进个人计划的理想时机。太阳与木星形成有利相位，带来充沛能量和扩张机遇。紫微斗数显示，今日天乙贵人星临日柱，有意外援助和顺利解决问题的迹象。上午适合处理需要创造力的工作，下午则利于沟通和协商，晚间能量逐渐平缓，适合放松和总结。',
              'career': '工作方面今日特别顺利，思维清晰，执行力强，适合处理复杂任务或启动新项目。上午9点至12点是创意和分析工作的黄金时段，头脑最为活跃，建议安排重要的策划或方案设计。午后1点至4点人际互动效率高，适合进行团队会议、客户沟通或谈判活动。紫微斗数显示，今日文昌星与驿马星相会，利于书面工作和远程合作，邮件沟通或文档编写会特别高效。可能有上级或重要同事对你近期工作给予积极评价，增加晋升或重要任务委派的机会。今天还适合学习新技能或更新行业知识，下班后花30分钟浏览专业资讯，将为未来工作打下基础。建议把握今日良好状态，推进之前停滞的项目，同时记录工作成果，为未来评估做准备。',
              'love': '感情方面今天充满温馨和谐的能量，沟通顺畅，情感表达自然。金星在您的亲密关系宫形成温和相位，增强了吸引力和亲和力。单身的$constellation，今日社交场合中可能遇到气场相合的人，尤其在下午的活动中，保持自然状态最能展现魅力。已有伴侣的您，今天是增进感情的好时机，工作后安排二人世界，简单的晚餐或散步都能创造美好回忆。紫微斗数显示，今日月老星临日支，适合表达爱意和解决之前的小误会。避免在晚上8点后讨论敏感话题，此时容易因疲惫产生不必要的情绪波动。今天也适合关心伴侣的家人或朋友，这种用心会被深刻感受到。无论感情状态如何，今天都适合提升自我形象，换个发型或穿上喜欢的衣服，自信的状态最具吸引力。',
              'money': '财务方面今天稳定有余，可能有小额意外收入，如报销款项到账或之前的投资小有回报。上午适合处理账单支付和财务规划，思路清晰，不易出错。中午至下午有利于职场财务谈判，如果有加薪或合同金额讨论，今天特别容易达成有利条件。紫微斗数显示，财帛宫有禄神相照，适合理财学习和稳健投资，但不宜进行高风险投机。今天购物决策理性，能够分辨真正需要与冲动消费，适合购买长期使用的物品或投资型消费。晚间可能收到理财信息或投资机会，建议记录下来但延后决策，先进行充分研究。今天还适合检查信用卡账单或订阅服务，可能发现不必要的支出项目。整体而言，今日财运平稳向上，适合做中长期财务规划调整，但大额决策仍需谨慎评估。',
              'health': '健康状况今天相当良好，精力充沛，身体各系统运转和谐。早晨醒来后做10分钟拉伸，能激活全身能量，为一整天奠定良好基础。上午注意补充水分，避免因专注工作而忽略基本生理需求。中午饮食宜清淡均衡，富含蛋白质和蔬菜的餐食能维持下午稳定的能量水平。紫微斗数显示，今日天医星临身宫，自愈能力增强，是调整不良习惯的好时机。下午3-4点可能出现短暂疲劳，建议起身活动5分钟或做深呼吸练习恢复活力。晚间7-9点是锻炼的理想时段，中等强度有氧运动效果最佳，如快走、游泳或骑行30分钟。睡前放松尤为重要，避免使用电子设备，可尝试热水泡脚或阅读纸质书籍，有助于提高睡眠质量。今天还特别适合关注心理健康，花10分钟记录感恩事项，有助于保持积极心态和情绪平衡。',
              'social': '人际关系今天特别活跃和谐，沟通顺畅，容易获得他人认可和支持。上午适合处理一对一交流，表达清晰，理解深入，特别适合与重要合作伙伴或团队核心成员沟通。中午社交场合中自然成为焦点，不经意的言行可能给他人留下深刻印象。下午是扩展人脉的好时机，水星相位有利于结识新朋友或重要业务联系人，尤其在专业领域的活动中。紫微斗数显示，今日桃花星临官禄宫，职场魅力提升，上级和同事都容易被你的想法所吸引。晚间聚会中可能遇到志同道合的人，开放而真诚的交流将建立长久的友谊基础。今天还适合修复之前的关系小裂痕，一条简单的问候信息就能重新连接。社交媒体活动也格外有效，分享专业见解或生活点滴都能获得积极反馈。建议把握今日良好状态，主动联系重要的人脉资源，同时保持真实自我，不必过度迎合他人期待。',
              'advice': '今日综合建议是"把握机遇，平衡发展"。首先，工作上应充分利用上午9-12点的高效时段，处理最需要创造力和专注力的任务，将重要事项前置。沟通和会议安排在下午进行效果最佳，表达清晰，容易达成共识。时间管理上建议采用"2-1-2"法则：工作2小时，休息10分钟，再工作2小时，避免长时间专注导致效率下降。人际交往中，今天特别适合"给予"而非"索取"，一个小小的帮助或赞美可能带来意想不到的人际回报。健康方面，确保摄入2000毫升水，午餐后进行10分钟轻度活动，预防下午能量低谷。情绪管理尤为重要，如遇压力情境，尝试"5-3-7"呼吸法：吸气5秒，屏气3秒，呼气7秒，快速恢复平静。晚间是个人成长的黄金时段，花30分钟阅读或学习，为长期发展积累知识。最后，睡前5分钟记录今日三个成功和一个改进点，既肯定自己也保持进步动力。整体而言，今天是平衡发展各领域的理想日子，既要把握当下机遇，也要为未来铺路。',
            },
            'summary': '根据$constellation的特质和紫微斗数分析，您的运势图谱展现出丰富而独特的发展轨迹。在西方占星学中，$constellation的${DateTime.now().year}年运势受到木星与土星相位的特别影响，木星带来扩张与机遇，土星则提供结构与稳定性。东方紫微斗数显示，您的命盘中紫微星与天府星形成特殊配置，使您在挑战中更能发挥潜力。这种东西方星象的结合，为您提供了更立体、更全面的运势解读。\n\n年度层面，事业发展呈现稳中有进的态势，上半年适合打基础，下半年则有突破机会。财务方面需要平衡短期收益与长期规划，尤其注意避免冲动消费和高风险投资。感情生活可能经历考验与升华，无论单身或有伴侣，真诚沟通都是关键。健康状况总体良好，但需警惕工作压力导致的身心不适。\n\n月度运势更为具体，显示出周期性的起伏，通过前瞻性规划可以规避低谷，把握高峰。日常生活中，建议您根据能量周期安排活动，遵循自身生物钟，提高效率与生活质量。综合来看，通过整合占星与紫微的智慧，您可以在认知自我的基础上，做出更明智的选择，创造更理想的未来。',
            'rawResponse': rawResponse,
            'lastUpdated': DateTime.now().toIso8601String()
          };
        }
        
        print('✅ 星座+紫微深度分析请求成功');
        return response.data;
      } catch (dioError) {
        print('❌ Dio请求失败: $dioError');
        
        // 如果是404错误，说明后端尚未实现该功能，返回模拟数据
        if (dioError is DioException) {
          print('❌ DioException: ${dioError.message}, 状态码: ${dioError.response?.statusCode}');
          if (dioError.response?.statusCode == 404) {
            print('⚠️ 后端API尚未实现，返回模拟数据');
            return {
              'yearlyFortune': {
                'summary': '基于$constellation的年运分析，结合紫微斗数，${DateTime.now().year}年整体运势平稳。',
                'career': '事业方面有稳定发展，可能会遇到新的机会。',
                'love': '感情上会有新的变化，单身者有望遇到心仪对象。',
                'money': '财运方面需要谨慎规划，避免冲动消费。',
                'health': '健康状况良好，但应注意作息规律。',
                'social': '人际关系和谐，社交圈可能会有所扩展。',
                'advice': '保持积极心态，稳步前进，把握机会但不要冒进。',
              },
              'monthlyFortune': {
                'summary': '本月运势有所起伏，需要灵活应对各种变化。',
                'career': '工作压力较大，但有突破机会，建议保持专注。',
                'love': '感情需要更多沟通，避免误解导致不必要的矛盾。',
                'money': '财运稳定，适合做长期投资规划。',
                'health': '注意身心平衡，避免过度疲劳。',
                'social': '社交活动增多，人脉资源有所拓展。',
                'advice': '调整节奏，重视自我调适，保持乐观心态。',
              },
              'dailyFortune': {
                'summary': '今日整体运势良好，适合处理重要事务。',
                'career': '工作效率高，可能会收到上级肯定。',
                'love': '感情运势平稳，可以安排浪漫约会。',
                'money': '财运一般，避免不必要的支出。',
                'health': '精力充沛，是运动锻炼的好时机。',
                'social': '人际交往顺畅，有助于拓展人脉。',
                'advice': '把握今日良好状态，完成重要任务。',
              },
              'summary': '根据$constellation的特质和紫微斗数分析，您在事业、财运、感情等方面都有独特的发展轨迹。通过整合西方占星学和东方紫微斗数，我们为您提供了更全面的运势解读。',
              'lastUpdated': DateTime.now().toIso8601String()
            };
          }
          
          // 如果是超时错误，也返回模拟数据，但要提示用户
          if (dioError.type == DioExceptionType.connectionTimeout || 
              dioError.type == DioExceptionType.receiveTimeout ||
              dioError.type == DioExceptionType.sendTimeout) {
            print('⚠️ API请求超时，返回模拟数据');
            return {
              'yearlyFortune': {
                'summary': '(请求超时)基于$constellation的年运分析，结合紫微斗数，${DateTime.now().year}年整体运势平稳。',
                'career': '事业方面有稳定发展，可能会遇到新的机会。',
                'love': '感情上会有新的变化，单身者有望遇到心仪对象。',
                'money': '财运方面需要谨慎规划，避免冲动消费。',
                'health': '健康状况良好，但应注意作息规律。',
                'social': '人际关系和谐，社交圈可能会有所扩展。',
                'advice': '保持积极心态，稳步前进，把握机会但不要冒进。',
                'note': '由于AI分析请求超时，此为预设内容。您可以稍后再试。',
              },
              'monthlyFortune': {
                'summary': '(请求超时)本月运势有所起伏，需要灵活应对各种变化。',
                'career': '工作压力较大，但有突破机会，建议保持专注。',
                'love': '感情需要更多沟通，避免误解导致不必要的矛盾。',
                'money': '财运稳定，适合做长期投资规划。',
                'health': '注意身心平衡，避免过度疲劳。',
                'social': '社交活动增多，人脉资源有所拓展。',
                'advice': '调整节奏，重视自我调适，保持乐观心态。',
                'note': '由于AI分析请求超时，此为预设内容。您可以稍后再试。',
              },
              'dailyFortune': {
                'summary': '(请求超时)今日整体运势良好，适合处理重要事务。',
                'career': '工作效率高，可能会收到上级肯定。',
                'love': '感情运势平稳，可以安排浪漫约会。',
                'money': '财运一般，避免不必要的支出。',
                'health': '精力充沛，是运动锻炼的好时机。',
                'social': '人际交往顺畅，有助于拓展人脉。',
                'advice': '把握今日良好状态，完成重要任务。',
                'note': '由于AI分析请求超时，此为预设内容。您可以稍后再试。',
              },
              'summary': '根据$constellation的特质和紫微斗数分析，您在事业、财运、感情等方面都有独特的发展轨迹。通过整合西方占星学和东方紫微斗数，我们为您提供了更全面的运势解读。',
              'lastUpdated': DateTime.now().toIso8601String(),
              'error': '请求超时：AI分析需要较长时间，请稍后再试。'
            };
          }
        }
        rethrow;
      }
    } catch (e) {
      print('❌ 星座+紫微深度分析请求失败: $e');
      throw Exception('星座+紫微深度分析失败: ${e.toString()}');
    }
  }
} 