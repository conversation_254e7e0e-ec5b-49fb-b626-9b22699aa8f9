enum MessageType {
  user,
  ai,
  system,
}

class ChatMessage {
  final String id;
  final MessageType type;
  final String content;
  final DateTime timestamp;
  final bool isTyping;
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.type,
    required this.content,
    required this.timestamp,
    this.isTyping = false,
    this.metadata,
  });

  // 添加JSON序列化方法
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'].toString(),
      type: _typeFromString(json['type']),
      content: json['content'],
      timestamp: DateTime.parse(json['timestamp']),
      isTyping: json['isTyping'] ?? false,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': _typeToString(type),
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'isTyping': isTyping,
      'metadata': metadata,
    };
  }

  static MessageType _typeFromString(String typeString) {
    switch (typeString) {
      case 'user':
        return MessageType.user;
      case 'ai':
        return MessageType.ai;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.user;
    }
  }

  static String _typeToString(MessageType type) {
    switch (type) {
      case MessageType.user:
        return 'user';
      case MessageType.ai:
        return 'ai';
      case MessageType.system:
        return 'system';
    }
  }

  ChatMessage copyWith({
    String? id,
    MessageType? type,
    String? content,
    DateTime? timestamp,
    bool? isTyping,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      type: type ?? this.type,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      isTyping: isTyping ?? this.isTyping,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isUser => type == MessageType.user;
  bool get isAI => type == MessageType.ai;
  bool get isSystem => type == MessageType.system;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatMessage(id: $id, type: $type, content: ${content.substring(0, content.length > 20 ? 20 : content.length)}...)';
  }

  String get timeString {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}

// AI回复模板
class AIResponseTemplates {
  static const Map<String, List<String>> _responses = {
    'greeting': [
      '您好！我是您的专属命理顾问，很高兴为您服务。',
      '欢迎！我可以为您解答各种命理问题。',
      '您好，有什么可以帮助您的吗？',
    ],
    'fortune': [
      '根据您的情况，我建议您最近要注意把握机会，运势整体向好。',
      '您的运势正在逐步提升，保持积极的心态会更有利。',
      '近期要注意人际关系的处理，会对您的运势产生影响。',
    ],
    'career': [
      '事业方面，您需要耐心等待时机，同时提升自己的能力。',
      '建议您多关注行业动态，为职业发展做好准备。',
      '在工作中要保持专注和耐心，会有好的结果。',
    ],
    'love': [
      '感情方面，真诚待人是最重要的。',
      '桃花运正在提升，要主动把握机会。',
      '与伴侣要多沟通，理解彼此的想法。',
    ],
    'health': [
      '健康方面要注意劳逸结合，保持良好的生活习惯。',
      '最近要特别注意情绪管理，心情愉悦有利于身体健康。',
      '建议您多进行户外运动，增强体质。',
    ],
    'money': [
      '财运方面，要理性投资，避免冲动消费。',
      '正财运不错，但要注意守财，避免不必要的开支。',
      '投资理财要谨慎，建议多听取专业意见。',
    ],
    'default': [
      '感谢您的问题，我会根据您的情况给出建议。',
      '这是一个很好的问题，让我为您分析一下。',
      '我理解您的关注，这确实是值得思考的问题。',
    ],
  };

  static String getResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    // 简单的关键词匹配
    if (message.contains('你好') || message.contains('hello') || message.contains('hi')) {
      return _getRandomResponse('greeting');
    } else if (message.contains('运势') || message.contains('命运') || message.contains('运气')) {
      return _getRandomResponse('fortune');
    } else if (message.contains('事业') || message.contains('工作') || message.contains('职业')) {
      return _getRandomResponse('career');
    } else if (message.contains('感情') || message.contains('爱情') || message.contains('婚姻')) {
      return _getRandomResponse('love');
    } else if (message.contains('健康') || message.contains('身体') || message.contains('疾病')) {
      return _getRandomResponse('health');
    } else if (message.contains('财运') || message.contains('金钱') || message.contains('财富')) {
      return _getRandomResponse('money');
    } else {
      return _getRandomResponse('default');
    }
  }

  static String _getRandomResponse(String category) {
    final responses = _responses[category] ?? _responses['default']!;
    return responses[DateTime.now().millisecondsSinceEpoch % responses.length];
  }
}

// 快捷问题模板
class QuickQuestions {
  static const List<String> _questions = [
    '我最近的运势如何？',
    '事业方面有什么建议？',
    '感情运势怎么样？',
    '健康方面需要注意什么？',
    '财运如何？',
    '今年的整体运势？',
    '适合什么职业发展？',
    '何时结婚比较好？',
    '投资理财要注意什么？',
    '如何提升运势？',
  ];

  static List<String> getRandomQuestions([int count = 6]) {
    final shuffled = List<String>.from(_questions);
    shuffled.shuffle();
    return shuffled.take(count).toList();
  }

  static List<String> getAllQuestions() {
    return List<String>.from(_questions);
  }
} 