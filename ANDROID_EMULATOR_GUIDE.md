# 📱 Android Studio 模拟器连接指南

## 🎯 目标
在Android Studio中创建、启动和连接Android模拟器来运行"满天神佛"应用。

## 📋 前提条件
✅ Android Studio已安装 (版本 2024.3.2)  
✅ Android SDK已配置  
✅ Flutter环境已就绪  

## 🚀 详细步骤

### 步骤1: 打开AVD Manager

#### 方法1: 通过工具栏
1. 打开 **Android Studio**
2. 在工具栏中找到 **AVD Manager** 图标 📱
3. 点击 **AVD Manager**

#### 方法2: 通过菜单
1. 打开 **Android Studio**
2. 选择 `Tools` → `AVD Manager`

#### 方法3: 欢迎界面
1. 如果在Android Studio欢迎界面
2. 点击 `More Actions` → `AVD Manager`

### 步骤2: 创建新的虚拟设备

1. 在AVD Manager窗口中，点击 **"+ Create Virtual Device"**

2. **选择设备类型**:
   ```
   推荐选择:
   📱 Phone → Pixel 6 Pro
   📱 Phone → Pixel 7 Pro  
   📱 Phone → Pixel 8 Pro
   
   屏幕规格: 1440 x 3120, 512 ppi
   ```

3. 点击 **"Next"**

### 步骤3: 选择系统映像

1. **推荐系统映像**:
   ```
   🎯 API Level 33 (Android 13.0) - 推荐
   🎯 API Level 34 (Android 14.0)
   🎯 API Level 32 (Android 12L)
   
   选择: x86_64 Images → Google APIs
   ```

2. 如果系统映像未下载，点击 **"Download"**
3. 下载完成后，点击 **"Next"**

### 步骤4: 验证配置

确认以下设置:
```
AVD Name: Pixel_6_Pro_API_33 (可自定义)
System Image: API 33, Google APIs
Startup Orientation: Portrait
RAM: 2048 MB (推荐)
VM Heap: 256 MB
Internal Storage: 6 GB
SD Card: 512 MB (可选)
```

点击 **"Finish"** 创建模拟器

### 步骤5: 启动模拟器

1. 在AVD Manager列表中找到刚创建的模拟器
2. 点击 **▶️ 播放按钮** 启动模拟器
3. 等待模拟器完全启动 (首次启动可能需要2-3分钟)

## 🔌 连接模拟器到Flutter项目

### 方法1: Android Studio中运行

1. **打开项目**:
   ```
   File → Open → 选择 mantianshenfo/Front 文件夹
   ```

2. **等待项目同步**:
   - Gradle sync 完成
   - Flutter插件识别项目

3. **选择设备**:
   - 在顶部工具栏找到设备选择器
   - 选择你创建的模拟器 (例如: Pixel_6_Pro_API_33)

4. **运行应用**:
   - 点击绿色播放按钮 ▶️
   - 或按快捷键 `Shift + F10`

### 方法2: 命令行运行

```bash
# 1. 检查连接的设备
flutter devices

# 应该看到类似输出:
# Pixel_6_Pro_API_33 (mobile) • emulator-5554 • android-x64 • Android 13 (API 33)

# 2. 运行到模拟器
flutter run -d emulator-5554

# 或者直接运行到任何Android设备
flutter run -d android
```

## 🛠️ 高级配置

### 性能优化设置

在AVD Manager中编辑模拟器，调整以下设置:

```
Graphics: Hardware - GLES 2.0 (推荐)
Boot Option: Cold Boot (首次启动)
RAM: 4096 MB (如果电脑内存充足)
VM Acceleration: Automatic
```

### 网络配置

确保模拟器可以访问本地后端:
```
模拟器网络: ********:3001 映射到 localhost:3001
前端API配置: 已设置为 localhost:3001 (会自动处理)
```

## 📊 检查连接状态

### 命令行检查
```bash
# 检查ADB设备
adb devices

# 检查Flutter设备
flutter devices

# 检查模拟器详情
flutter devices -v
```

### 应用内检查
启动应用后，查看右上角的连接状态指示器:
- 🟢 绿色: 后端已连接
- 🔴 红色: 后端离线

## 🐛 常见问题解决

### 1. 模拟器启动失败
```bash
# 解决方案1: 启用虚拟化
确保BIOS中启用了 Intel VT-x 或 AMD-V

# 解决方案2: 重启ADB
adb kill-server
adb start-server

# 解决方案3: 冷启动
在AVD Manager中选择 Cold Boot Now
```

### 2. 模拟器卡在启动画面
```bash
# 解决方案1: 增加RAM分配
编辑AVD → Advanced Settings → RAM: 4096 MB

# 解决方案2: 清除模拟器数据
AVD Manager → 设备旁边的 ⬇️ → Wipe Data
```

### 3. Flutter不识别模拟器
```bash
# 重启Flutter工具
flutter clean
flutter pub get

# 重启ADB
adb kill-server
adb start-server
```

### 4. 网络连接问题
```bash
# 确保后端运行
cd backend
npm run dev

# 检查端口
netstat -an | findstr :3001
```

## 🎯 推荐模拟器配置

### 测试配置1: 高性能
```
设备: Pixel 8 Pro
API: 34 (Android 14)
RAM: 4GB
存储: 8GB
图形: Hardware - GLES 2.0
```

### 测试配置2: 兼容性
```
设备: Pixel 6
API: 30 (Android 11)
RAM: 2GB  
存储: 6GB
图形: Software - GLES 1.1
```

### 测试配置3: 低端设备模拟
```
设备: Nexus 5X
API: 28 (Android 9)
RAM: 1.5GB
存储: 4GB
```

## ✅ 成功验证清单

- [ ] AVD Manager可以打开
- [ ] 成功创建虚拟设备
- [ ] 模拟器可以正常启动
- [ ] `flutter devices` 显示模拟器
- [ ] 应用可以安装到模拟器
- [ ] 应用在模拟器中正常运行
- [ ] 网络连接状态显示正常

## 🚀 一键启动脚本

创建批处理文件快速启动:

```batch
@echo off
echo 🚀 启动满天神佛开发环境...

echo 📱 启动Android模拟器...
start "" "F:\android\bin\studio64.exe"

echo ⏳ 等待5秒...
timeout /t 5 /nobreak >nul

echo 🖥️ 启动后端服务器...
cd backend
start cmd /k "npm run dev"

echo 📱 启动Flutter应用...
cd ..
flutter run -d android

pause
```

现在你可以轻松在Android Studio中创建和连接Android模拟器来测试你的"满天神佛"应用了！🎉📱 