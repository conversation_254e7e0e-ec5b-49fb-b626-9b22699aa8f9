import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/services.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'screens/home_screen.dart';
import 'screens/bazi_screen.dart';
import 'screens/liuyao_screen.dart';
import 'screens/ziwei_screen.dart';
import 'screens/constellation_screen.dart';
import 'screens/ai_chat_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/history_screen.dart';
import 'screens/prediction_combo_screen.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'providers/user_provider.dart';
import 'providers/prediction_provider.dart';
import 'providers/chat_provider.dart';
import 'providers/constellation_provider.dart';
import 'providers/daily_fortune_provider.dart';
import 'providers/connection_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化中文本地化数据
  await initializeDateFormatting('zh_CN', null);
  
  // 设置状态栏样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Color(0xFF1E293B),
      statusBarIconBrightness: Brightness.light,
    ),
  );
  
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final GoRouter _router;

  @override
  void initState() {
    super.initState();
    _router = _createRouter();
  }

  GoRouter _createRouter() {
    return GoRouter(
      initialLocation: '/auth-check',
      redirect: (context, state) {
        try {
          final userProvider = Provider.of<UserProvider>(context, listen: false);
          final isLoggedIn = userProvider.isLoggedIn || userProvider.isGuest;
          
          // 检查当前路径
          final currentPath = state.fullPath ?? '/';
          
          // 如果在认证检查页面，根据登录状态重定向
          if (currentPath == '/auth-check') {
            return isLoggedIn ? '/' : '/login';
          }
          
          // 如果已登录（包括游客），访问登录/注册页面时重定向到首页
          if (isLoggedIn && (currentPath == '/login' || currentPath == '/register')) {
            return '/';
          }
          
          // 如果未登录且不是游客，访问需要认证的页面时重定向到登录页
          if (!isLoggedIn && !_isPublicRoute(currentPath)) {
            return '/login';
          }
          
          return null; // 不需要重定向
        } catch (e) {
          print('Router redirect error: $e');
          // 发生错误时，允许访问首页
          return null;
        }
      },
      routes: [
        // 认证检查路由
        GoRoute(
          path: '/auth-check',
          builder: (context, state) => const AuthCheckScreen(),
        ),
        
        // 登录和注册路由（公开访问）
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) => const RegisterScreen(),
        ),
        
        // 主应用路由（需要认证）
        GoRoute(
          path: '/',
          name: 'home',
          builder: (context, state) => const HomeScreen(),
        ),
        GoRoute(
          path: '/bazi',
          name: 'bazi',
          builder: (context, state) => const BaziScreen(),
        ),
        GoRoute(
          path: '/liuyao',
          name: 'liuyao',
          builder: (context, state) => const LiuyaoScreen(),
        ),
        GoRoute(
          path: '/ziwei',
          name: 'ziwei',
          builder: (context, state) => const ZiweiScreen(),
        ),
        GoRoute(
          path: '/constellation',
          name: 'constellation',
          builder: (context, state) {
            final Map<String, dynamic>? extra = state.extra as Map<String, dynamic>?;
            final String? constellation = extra?['constellation'];
            final String? analysisType = extra?['analysisType'];
            final bool isFromCombo = extra?['isFromCombo'] ?? false;
            return ConstellationScreen(
              preSelectedConstellation: constellation,
              analysisType: analysisType,
              isFromCombo: isFromCombo,
            );
          },
        ),
        GoRoute(
          path: '/ai-chat',
          name: 'ai-chat',
          builder: (context, state) => const AiChatScreen(),
        ),
        GoRoute(
          path: '/profile',
          name: 'profile',
          builder: (context, state) => const ProfileScreen(),
        ),
        GoRoute(
          path: '/history',
          name: 'history',
          builder: (context, state) => const HistoryScreen(),
        ),
        GoRoute(
          path: '/prediction-combo',
          name: 'prediction-combo',
          builder: (context, state) => const PredictionComboScreen(),
        ),
      ],
    );
  }

  bool _isPublicRoute(String path) {
    return path == '/login' || path == '/register' || path == '/auth-check';
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => PredictionProvider()),
        ChangeNotifierProvider(create: (_) => ChatProvider()),
        ChangeNotifierProvider(create: (_) => ConstellationProvider()),
        ChangeNotifierProvider(create: (_) => DailyFortuneProvider()),
        ChangeNotifierProvider(create: (_) => ConnectionProvider()),
      ],
      child: MaterialApp.router(
        title: '银发-满天神佛',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          // 自定义主题配置
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF3B82F6),
            brightness: Brightness.light,
          ),
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF1E293B),
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            ),
          ),
          cardTheme: CardThemeData(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 4,
          ),
        ),
        routerConfig: _router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

// 认证检查页面 - 启动时检查用户登录状态
class AuthCheckScreen extends StatefulWidget {
  const AuthCheckScreen({super.key});

  @override
  State<AuthCheckScreen> createState() => _AuthCheckScreenState();
}

class _AuthCheckScreenState extends State<AuthCheckScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    // 等待一会儿显示启动动画
    await Future.delayed(const Duration(milliseconds: 1000));
    
    if (!mounted) return;
    
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    try {
      // 尝试自动登录
      final isLoggedIn = await userProvider.autoLogin();
      
      if (!mounted) return;
      
      if (isLoggedIn) {
        // 如果登录成功，初始化其他Provider数据
        await _initializeProviders();
        
        if (mounted) {
          context.go('/');
        }
      } else {
        // 如果自动登录失败，尝试游客模式
        final guestLoginSuccess = await userProvider.guestLogin();
        
        if (!mounted) return;
        
        if (guestLoginSuccess) {
          // 游客模式成功，直接进入首页
          if (mounted) {
            context.go('/');
          }
        } else {
          // 游客模式也失败，跳转到登录页
          if (mounted) {
            context.go('/login');
          }
        }
      }
    } catch (e) {
      print('Auth check failed: $e');
      
      if (!mounted) return;
      
      // 发生错误时，尝试游客模式
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final guestLoginSuccess = await userProvider.guestLogin();
        
        if (mounted) {
          if (guestLoginSuccess) {
            context.go('/');
          } else {
            context.go('/login');
          }
        }
      } catch (guestError) {
        print('Guest login failed: $guestError');
        if (mounted) {
          context.go('/login');
        }
      }
    }
  }

  Future<void> _initializeProviders() async {
    final context = this.context;
    if (!mounted) return;

    // 初始化各个Provider的数据
    final chatProvider = Provider.of<ChatProvider>(context, listen: false);
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    final predictionProvider = Provider.of<PredictionProvider>(context, listen: false);

    try {
      // 并行加载数据
      await Future.wait([
        chatProvider.loadChatHistory(),
        constellationProvider.loadConstellations(),
        predictionProvider.loadRecords(),
      ]);
    } catch (e) {
      print('Provider initialization failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF1E293B),
              const Color(0xFF334155),
              const Color(0xFF475569),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo动画
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 1000),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30),
                        gradient: LinearGradient(
                          colors: [
                            Colors.amber.shade400,
                            Colors.orange.shade600,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.amber.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.auto_awesome,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 32),
              
              // 标题动画
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 1200),
                tween: Tween(begin: 0.0, end: 1.0),
                curve: Curves.easeOut,
                builder: (context, value, child) {
                  return Opacity(
                    opacity: value,
                    child: Transform.translate(
                      offset: Offset(0, 20 * (1 - value)),
                      child: const Column(
                        children: [
                          Text(
                            '银发-满天神佛',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 2.0,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            '探索命理奥秘，指引人生方向',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                              letterSpacing: 1.0,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 60),
              
              // 加载指示器
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 800),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Opacity(
                    opacity: value,
                    child: Column(
                      children: [
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: CircularProgressIndicator(
                            strokeWidth: 3,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.amber.shade400,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          '正在初始化...',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
} 