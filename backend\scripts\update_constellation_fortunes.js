#!/usr/bin/env node

/**
 * 星座运势数据更新脚本
 * 
 * 使用方法:
 * node update_constellation_fortunes.js [--force]
 * 
 * 参数:
 * --force: 强制更新所有星座运势，即使今日已更新过
 */

// 初始化LeanCloud
const AV = require('leanengine');
const { appId, appKey, masterKey } = require('../.leancloud/config.json');
AV.init({
  appId,
  appKey,
  masterKey,
  serverURL: 'https://4ahhqofx.lc-cn-n1-shared.com',
});

// 导入星座运势模型
const ConstellationFortune = require('../models/ConstellationFortune');

// 解析命令行参数
const args = process.argv.slice(2);
const forceRefresh = args.includes('--force');
const specificType = args.find(arg => !arg.startsWith('--'));

async function updateConstellationFortunes() {
  try {
    console.log('\n🌟 ===== 星座运势数据更新脚本 =====');
    
    if (specificType) {
      // 更新特定星座
      const validTypes = Object.values(ConstellationFortune.TYPES);
      if (!validTypes.includes(specificType)) {
        console.error(`❌ 无效的星座类型: ${specificType}`);
        console.log(`💡 有效的星座类型: ${validTypes.join(', ')}`);
        return;
      }
      
      console.log(`🔄 正在更新 ${ConstellationFortune.NAMES[specificType]} 的运势...`);
      const fortune = await ConstellationFortune.getAIConstellationFortune(specificType);
      console.log(`✅ ${ConstellationFortune.NAMES[specificType]} 运势更新成功`);
      
      // 输出生成的运势内容摘要
      console.log('\n📊 运势内容摘要:');
      console.log(`总体运势 (${fortune.summary.length}字): ${fortune.summary}`);
      console.log(`爱情运势 (${fortune.loveAdvice.length}字): ${fortune.loveAdvice}`);
      console.log(`财运分析 (${fortune.moneyAdvice.length}字): ${fortune.moneyAdvice}`);
      console.log(`事业运势 (${fortune.careerAdvice.length}字): ${fortune.careerAdvice}`);
      console.log(`健康运势 (${fortune.healthAdvice.length}字): ${fortune.healthAdvice}`);
    } else {
      // 更新所有星座
      console.log(`🔄 开始${forceRefresh ? '强制' : ''}更新所有星座运势...`);
      
      // 检查今日运势是否已更新
      if (!forceRefresh) {
        const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
        if (isUpdated) {
          console.log('ℹ️ 今日星座运势已更新，无需重复更新');
          console.log('💡 如需强制更新，请使用 --force 参数');
          return;
        }
      }
      
      // 开始更新所有星座运势
      const results = await ConstellationFortune.updateAllDailyFortunes();
      
      console.log(`✅ 所有星座运势更新成功，共${results.length}条数据`);
      
      // 输出每个星座运势的字数统计
      console.log('\n📊 字数统计:');
      results.forEach(fortune => {
        console.log(`${fortune.name}: 总体(${fortune.summary.length}字), 爱情(${fortune.loveAdvice.length}字), 财运(${fortune.moneyAdvice.length}字), 事业(${fortune.careerAdvice.length}字), 健康(${fortune.healthAdvice.length}字)`);
      });
    }
    
    console.log('\n🎉 ===== 更新完成 =====');
  } catch (error) {
    console.error('❌ 更新星座运势失败:', error);
  } finally {
    // 脚本执行完成后退出
    process.exit();
  }
}

// 执行更新
updateConstellationFortunes(); 