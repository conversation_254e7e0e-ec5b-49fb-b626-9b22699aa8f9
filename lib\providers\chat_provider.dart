import 'package:flutter/foundation.dart';
import '../models/chat_message.dart';
import '../services/api_service.dart';

class ChatProvider with ChangeNotifier {
  List<ChatMessage> _messages = [];
  bool _isTyping = false;
  bool _isLoading = false;

  List<ChatMessage> get messages => _messages;
  bool get isTyping => _isTyping;
  bool get isLoading => _isLoading;

  void setTyping(bool typing) {
    _isTyping = typing;
    notifyListeners();
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void addMessage(ChatMessage message) {
    _messages.add(message);
    notifyListeners();
  }

  void addUserMessage(String content) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: MessageType.user,
      content: content,
      timestamp: DateTime.now(),
    );
    addMessage(message);
  }

  void addAIMessage(String content) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: MessageType.ai,
      content: content,
      timestamp: DateTime.now(),
    );
    addMessage(message);
  }

  void addSystemMessage(String content) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: MessageType.system,
      content: content,
      timestamp: DateTime.now(),
    );
    addMessage(message);
  }

  void removeMessage(String messageId) {
    _messages.removeWhere((message) => message.id == messageId);
    notifyListeners();
  }

  void clearMessages() {
    _messages.clear();
    notifyListeners();
  }

  void updateMessage(ChatMessage updatedMessage) {
    final index = _messages.indexWhere((msg) => msg.id == updatedMessage.id);
    if (index != -1) {
      _messages[index] = updatedMessage;
      notifyListeners();
    }
  }

  // 添加流式消息发送方法
  Future<void> sendMessageStreamAndGetReply(String userMessage) async {
    print('📝 [ChatProvider] 开始发送流式消息: $userMessage');
    
    try {
      // 显示正在输入状态
      setTyping(true);
      
      // 先添加用户消息
      final userMsg = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: MessageType.user,
        content: userMessage,
        timestamp: DateTime.now(),
      );
      addMessage(userMsg);
      
      // 创建流式AI消息
      final aiMessageId = '${DateTime.now().millisecondsSinceEpoch + 1}';
      final aiMessage = ChatMessage(
        id: aiMessageId,
        type: MessageType.ai,
        content: '',
        timestamp: DateTime.now(),
      );
      addMessage(aiMessage);
      
      // 获取API服务
      final apiService = ApiService();
      
      // 尝试流式发送
      try {
        final stream = await apiService.sendMessageStream(userMessage);
        
        String accumulatedContent = '';
        await for (final chunk in stream) {
          accumulatedContent += chunk;
          
          // 更新AI消息内容
          final updatedMessage = ChatMessage(
            id: aiMessageId,
            type: MessageType.ai,
            content: accumulatedContent,
            timestamp: DateTime.now(),
          );
          updateMessage(updatedMessage);
        }
        
        print('✅ [ChatProvider] 流式消息发送完成');
      } catch (streamError) {
        print('❌ [ChatProvider] 流式发送失败，回退到普通发送: $streamError');
        
        // 移除空的AI消息
        removeMessage(aiMessageId);
        
        // 回退到普通方式
        final messages = await apiService.sendMessage(userMessage);
        addMessage(messages['ai']!);
      }
      
    } catch (e) {
      print('❌ [ChatProvider] 发送失败: $e');
      // 发送失败时显示错误信息
      addSystemMessage('发送失败: ${e.toString()}');
    } finally {
      // 隐藏正在输入状态
      setTyping(false);
      print('✅ [ChatProvider] 消息发送完成');
    }
  }

  // 原有的发送方法，作为备用
  Future<void> _sendMessageNormal(String userMessage) async {
    try {
      // 显示正在输入状态
      setTyping(true);
      
      // 调用后端API
      final apiService = ApiService();
      final messages = await apiService.sendMessage(userMessage);
      
      // 添加用户消息和AI回复
      addMessage(messages['user']!);
      addMessage(messages['ai']!);
      
    } catch (e) {
      // 发送失败时显示错误信息
      addSystemMessage('发送失败: ${e.toString()}');
    } finally {
      // 隐藏正在输入状态
      setTyping(false);
    }
  }

  // 发送消息并获取AI回复 - 修改为支持流式和非流式
  Future<void> sendMessageAndGetReply(String userMessage) async {
    // 尝试流式发送，如果失败则回退到普通方式
    try {
      await sendMessageStreamAndGetReply(userMessage);
    } catch (e) {
      // 如果流式发送失败，回退到原来的方式
      await _sendMessageNormal(userMessage);
    }
  }

  // 加载聊天历史
  Future<void> loadChatHistory() async {
    try {
      setLoading(true);
      
      // 调用后端API获取聊天历史
      final apiService = ApiService();
      final messages = await apiService.getChatHistory();
      
      _messages = messages;
      
      // 如果没有历史记录，添加欢迎消息
      if (_messages.isEmpty) {
        addSystemMessage('欢迎使用AI咨询服务！我是您的专属命理顾问，可以为您解答各种问题。');
      }
      
    } catch (e) {
      // 加载失败时显示错误信息并使用默认欢迎消息
      _messages = [
        ChatMessage(
          id: '1',
          type: MessageType.system,
          content: '网络连接失败，正在使用离线模式。请检查网络连接后重试。',
          timestamp: DateTime.now(),
        ),
      ];
    } finally {
      setLoading(false);
    }
  }

  // 获取今日消息数量
  int get todayMessageCount {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    return _messages.where((msg) => 
      msg.timestamp.isAfter(today) && 
      msg.type == MessageType.user
    ).length;
  }

  // 获取总消息数量
  int get totalMessageCount => _messages.where((msg) => msg.type == MessageType.user).length;

  // 获取最近的消息
  List<ChatMessage> get recentMessages {
    if (_messages.length <= 10) return _messages;
    return _messages.sublist(_messages.length - 10);
  }

  // 按日期分组消息
  Map<String, List<ChatMessage>> get messagesByDate {
    final Map<String, List<ChatMessage>> grouped = {};
    final now = DateTime.now();
    
    for (final message in _messages) {
      final difference = now.difference(message.timestamp);
      String key;
      
      if (difference.inDays == 0) {
        key = '今天';
      } else if (difference.inDays == 1) {
        key = '昨天';
      } else if (difference.inDays < 7) {
        key = '${difference.inDays}天前';
      } else {
        key = '${message.timestamp.month}月${message.timestamp.day}日';
      }
      
      if (!grouped.containsKey(key)) {
        grouped[key] = [];
      }
      grouped[key]!.add(message);
    }
    
    return grouped;
  }

  // 搜索消息
  List<ChatMessage> searchMessages(String query) {
    if (query.isEmpty) return _messages;
    
    return _messages.where((message) => 
      message.content.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // 获取快捷问题
  Future<List<Map<String, dynamic>>> getQuickQuestions() async {
    try {
      final apiService = ApiService();
      return await apiService.getQuickQuestions();
    } catch (e) {
      // 返回默认快捷问题
      return [
        {'id': 1, 'text': '我的财运如何？', 'category': '财运', 'icon': '💰'},
        {'id': 2, 'text': '感情运势怎样？', 'category': '感情', 'icon': '💕'},
        {'id': 3, 'text': '事业发展建议', 'category': '事业', 'icon': '🚀'},
        {'id': 4, 'text': '健康需要注意什么？', 'category': '健康', 'icon': '🌱'},
        {'id': 5, 'text': '近期有什么需要注意的？', 'category': '运势', 'icon': '🔮'},
      ];
    }
  }

  // 清空聊天记录
  Future<void> clearChatHistory() async {
    try {
      final apiService = ApiService();
      await apiService.clearChatHistory();
      clearMessages();
      addSystemMessage('聊天记录已清空');
    } catch (e) {
      addSystemMessage('清空失败: ${e.toString()}');
    }
  }

  // 导出聊天记录
  String exportChatHistory() {
    final buffer = StringBuffer();
    buffer.writeln('聊天记录导出');
    buffer.writeln('导出时间: ${DateTime.now().toString()}');
    buffer.writeln('消息总数: ${_messages.length}');
    buffer.writeln('${'=' * 50}');
    
    for (final message in _messages) {
      final typeLabel = message.isUser ? '用户' : message.isAI ? 'AI' : '系统';
      buffer.writeln('[$typeLabel] ${message.timestamp.toString()}');
      buffer.writeln(message.content);
      buffer.writeln('-' * 30);
    }
    
    return buffer.toString();
  }
} 