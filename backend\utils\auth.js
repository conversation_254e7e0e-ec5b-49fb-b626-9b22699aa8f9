const jwt = require('jsonwebtoken');
const AV = require('leanengine');

// JWT密钥（实际使用时应该放在环境变量中）
const JWT_SECRET = process.env.JWT_SECRET || 'yinfa-shenfo-secret-key-2024';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

class AuthUtils {
  // 生成JWT Token
  static generateToken(userId, nickname) {
    return jwt.sign(
      { 
        userId, 
        nickname,
        iat: Math.floor(Date.now() / 1000)
      },
      JWT_SECRET,
      { 
        expiresIn: JWT_EXPIRES_IN 
      }
    );
  }

  // 验证JWT Token
  static verifyToken(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw new Error('Token验证失败');
    }
  }

  // 从请求头中提取Token
  static extractTokenFromHeader(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  // 认证中间件
  static authenticate(req, res, next) {
    try {
      console.log('\n🔐 ===== 认证中间件开始 =====');
      console.log('🌐 请求路径:', req.method, req.path);
      console.log('📋 请求头Authorization:', req.headers.authorization ? 'Present' : 'Missing');
      console.log('📋 完整请求头:', JSON.stringify(req.headers, null, 2));
      
      const token = AuthUtils.extractTokenFromHeader(req);
      if (!token) {
        console.log('❌ 认证失败: 缺少Token');
        console.log('🔍 Authorization头内容:', req.headers.authorization);
        console.log('💥 ===== 认证中间件结束 =====\n');
        return res.status(401).json({
          error: {
            code: 401,
            message: '缺少认证Token'
          }
        });
      }

      console.log('🔑 Token提取成功:', token.substring(0, 10) + '...');
      
      const decoded = AuthUtils.verifyToken(token);
      console.log('✅ Token验证成功');
      console.log('👤 用户信息:', { userId: decoded.userId, nickname: decoded.nickname });
      
      req.user = decoded;
      console.log('🎉 认证通过，继续处理请求');
      console.log('✨ ===== 认证中间件完成 =====\n');
      next();
    } catch (error) {
      console.log('❌ 认证失败:', error.message);
      console.log('🔍 Token内容:', req.headers.authorization);
      console.log('📋 错误详情:', error);
      console.log('💥 ===== 认证中间件结束 =====\n');
      return res.status(401).json({
        error: {
          code: 401,
          message: '认证失败'
        }
      });
    }
  }

  // 可选认证中间件（用于可选登录的接口）
  static optionalAuthenticate(req, res, next) {
    try {
      console.log('\n🎭 ===== 可选认证中间件开始 =====');
      console.log('🌐 请求路径:', req.method, req.path);
      console.log('📋 请求头Authorization:', req.headers.authorization ? 'Present' : 'Missing');
      
      const token = AuthUtils.extractTokenFromHeader(req);
      if (token) {
        console.log('🔑 Token存在，尝试验证...');
        const decoded = AuthUtils.verifyToken(token);
        req.user = decoded;
        console.log('✅ Token验证成功');
        console.log('👤 登录用户:', { userId: decoded.userId, nickname: decoded.nickname });
      } else {
        console.log('🎭 无Token，作为游客继续');
        req.user = null;
      }
      console.log('🎉 可选认证完成，继续处理请求');
      console.log('✨ ===== 可选认证中间件完成 =====\n');
      next();
    } catch (error) {
      console.log('⚠️ Token验证失败，但继续处理请求（游客模式）');
      console.log('📋 错误详情:', error.message);
      console.log('🎭 ===== 可选认证中间件完成 =====\n');
      // 可选认证失败不阻止请求
      req.user = null;
      next();
    }
  }

  // 管理员认证中间件
  static adminAuthenticate(req, res, next) {
    try {
      console.log('\n👑 ===== 管理员认证中间件开始 =====');
      console.log('🌐 请求路径:', req.method, req.path);
      console.log('📋 请求头Authorization:', req.headers.authorization ? 'Present' : 'Missing');
      
      const token = AuthUtils.extractTokenFromHeader(req);
      if (!token) {
        console.log('❌ 认证失败: 缺少Token');
        console.log('💥 ===== 管理员认证中间件结束 =====\n');
        return res.status(401).json({
          error: {
            code: 401,
            message: '缺少认证Token'
          }
        });
      }

      console.log('🔑 Token提取成功');
      
      const decoded = AuthUtils.verifyToken(token);
      console.log('✅ Token验证成功');
      
      // 检查用户是否为管理员
      AV.Object.createWithoutData('_User', decoded.userId).fetch().then(user => {
        const isAdmin = user.get('isAdmin') || false;
        
        if (!isAdmin) {
          console.log('❌ 用户不是管理员');
          console.log('💥 ===== 管理员认证中间件结束 =====\n');
          return res.status(403).json({
            error: {
              code: 403,
              message: '需要管理员权限'
            }
          });
        }
        
        console.log('👑 管理员身份验证成功');
        req.user = decoded;
        req.user.isAdmin = true;
        console.log('🎉 管理员认证通过，继续处理请求');
        console.log('✨ ===== 管理员认证中间件完成 =====\n');
        next();
      }).catch(error => {
        console.log('❌ 查询用户信息失败:', error.message);
        console.log('💥 ===== 管理员认证中间件结束 =====\n');
        return res.status(401).json({
          error: {
            code: 401,
            message: '认证失败'
          }
        });
      });
    } catch (error) {
      console.log('❌ 认证失败:', error.message);
      console.log('💥 ===== 管理员认证中间件结束 =====\n');
      return res.status(401).json({
        error: {
          code: 401,
          message: '认证失败'
        }
      });
    }
  }

  // 获取用户信息
  static async getUserById(userId) {
    const user = await AV.Object.createWithoutData('_User', userId).fetch();
    return user;
  }

  // 密码加密
  static async hashPassword(password) {
    const bcrypt = require('bcryptjs');
    return await bcrypt.hash(password, 10);
  }

  // 密码验证
  static async verifyPassword(password, hashedPassword) {
    const bcrypt = require('bcryptjs');
    return await bcrypt.compare(password, hashedPassword);
  }
}

module.exports = AuthUtils; 