# 满天神佛 APP 打包指南

本文档提供了如何构建和发布满天神佛应用的详细指南。

## 打包前准备

### 1. 确保 key.properties 文件配置正确

`key.properties` 文件应该放在 `android/` 目录下，包含以下内容：

```
storePassword=您的密钥库密码
keyPassword=您的密钥密码
keyAlias=您的密钥别名
storeFile=您的密钥库文件路径，例如 ../app/upload-keystore.jks
```

### 2. 确保密钥库文件(.jks)存在

如果您还没有创建密钥库文件，可以使用以下命令创建：

```
keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

将生成的 .jks 文件放在 `key.properties` 文件中 `storeFile` 指定的位置。

## 构建 APK

我们提供了便捷的批处理脚本来构建 APK：

1. 打开命令提示符或PowerShell
2. 进入项目根目录
3. 运行 `build_apk.bat`
4. 按照提示选择要构建的版本类型

构建完成后，APK 将位于 `build/app/outputs/flutter-apk/` 目录下。

### 构建版本说明

- **Debug 版本**：用于开发和测试，包含调试信息，体积较大
- **Release 版本**：用于正式发布，已优化体积和性能
- **Beta 版本**：用于内部测试，具有独立的应用ID，可与正式版共存

## 构建 App Bundle (Google Play 发布)

如果您计划将应用上传到 Google Play 商店，建议使用 App Bundle 格式：

1. 运行 `build_bundle.bat`
2. 按照提示选择要构建的版本类型

构建完成后，Bundle 将位于 `build/app/outputs/bundle/` 目录下。

## 发布注意事项

### 版本号管理

在 `pubspec.yaml` 文件中管理应用版本号：

```yaml
version: 1.0.0+1  # 格式为 versionName+versionCode
```

- **versionName**：用户可见的版本号（例如 1.0.0）
- **versionCode**：内部版本号，每次发布到应用商店时必须增加

### 签名验证

发布前验证 APK 签名信息：

```
keytool -list -v -keystore your_keystore_file.jks -alias your_key_alias
```

### 应用权限

应用使用的权限已在 `AndroidManifest.xml` 中声明，包括：
- 网络访问
- 存储读写
- 震动

## 常见问题解决

### 1. 签名问题

如果遇到签名相关的错误，请检查：
- key.properties 文件路径是否正确
- 密码、别名是否输入正确
- .jks 文件是否存在且路径正确

### 2. 构建失败

如果构建过程中出现错误：
- 检查 Flutter 和 Dart 版本是否兼容
- 运行 `flutter doctor` 检查环境配置
- 尝试 `flutter clean` 然后重新构建

### 3. 应用崩溃

如果应用在设备上崩溃：
- 检查 Proguard 规则是否正确保留了必要的类
- 确保所有依赖正确配置
- 使用 Debug 版本进行测试以获取更多错误信息

## 附录

### Proguard 规则说明

项目中的 Proguard 规则已经包含以下配置：
- Flutter 框架类保留
- Gson 序列化相关类保留
- 应用模型类保留
- HTTP 客户端相关类保留
- SQLite 数据库相关类保留

### 构建变体

项目配置了多个构建变体：
- **Debug**：调试版本，不混淆代码，包含调试信息
- **Release**：发布版本，代码混淆，优化体积和性能
- **Beta**：内部测试版本，基于发布版本，具有独立应用ID 