# 银发-满天神佛占卜预测Flutter应用

## 项目概述
一款专业的命理占卜预测Flutter应用，集成八字、紫微斗数、六爻、星座运势等多种预测功能，并提供AI智能咨询服务。

## 目标用户
面向对命理占卜、运势预测感兴趣的用户群体，提供专业的预测分析和个性化建议。

## 技术选型
- **开发框架**: Flutter
- **状态管理**: Provider  
- **UI框架**: Material Design
- **网络请求**: Dio
- **后端技术**: Node.js + Express + LeanCloud

## 前后端对接状态 ✅
### 已完成功能
- [x] API服务层 (ApiService) 
- [x] 用户认证系统 (登录/注册/自动登录)
- [x] AI聊天功能 (实时对话/历史记录)
- [x] 星座运势服务 (数据获取/刷新)
- [x] 预测记录管理 (创建/收藏/删除)
- [x] 网络连接状态监控
- [x] 错误处理和离线降级
- [x] 今日运势功能 (AI生成/每日更新/实时显示)

### API端点对接
```
POST /api/v1/auth/register        - 用户注册
POST /api/v1/auth/login          - 用户登录
POST /api/v1/auth/logout         - 用户退出
GET  /api/v1/auth/verify         - Token验证
GET  /api/v1/chat/history        - 聊天历史
POST /api/v1/chat/message        - 发送消息
GET  /api/v1/chat/quick-questions - 快捷问题
GET  /api/v1/chat/daily-fortune  - 获取今日运势
POST /api/v1/chat/daily-fortune/refresh - 刷新今日运势
GET  /api/v1/constellations      - 星座数据
GET  /api/v1/predictions/history - 预测历史
POST /api/v1/predictions/bazi    - 八字预测
POST /api/v1/predictions/ziwei   - 紫微预测
POST /api/v1/predictions/liuyao  - 六爻预测
PUT  /api/v1/predictions/:id/favorite - 收藏操作
```

## 应用架构
```
lib/
├── main.dart                 # 应用入口
├── services/
│   └── api_service.dart     # API服务层
├── providers/               # 状态管理
│   ├── user_provider.dart
│   ├── chat_provider.dart
│   ├── constellation_provider.dart
│   ├── prediction_provider.dart
│   └── daily_fortune_provider.dart
├── models/                  # 数据模型
│   ├── user_model.dart
│   ├── chat_message.dart
│   ├── constellation_fortune.dart
│   ├── prediction_record.dart
│   └── daily_fortune.dart
├── screens/                 # 页面组件
├── widgets/                 # 通用组件
└── utils/                   # 工具类
```

## 页面结构
| 页面名称 | 用途 | 核心功能 | API对接状态 | 文件路径 |
|:--------:|:----:|:--------:|:----------:|:--------:|
| 首页 | 应用主入口 | 功能导航、连接状态显示 | ✅ | `lib/screens/home_screen.dart` |
| AI聊天 | 智能咨询 | 实时对话、历史记录 | ✅ | `lib/screens/ai_chat_screen.dart` |
| 八字预测 | 生辰八字分析 | 八字排盘、命理解析 | ✅ | `lib/screens/bazi_screen.dart` |
| 紫微斗数 | 紫微命盘 | 命盘排列、星曜分析 | ✅ | `lib/screens/ziwei_screen.dart` |
| 六爻预测 | 六爻占卜 | 摇卦、解卦分析 | ✅ | `lib/screens/liuyao_screen.dart` |
| 星座运势 | 十二星座 | 运势查询、开运建议 | ✅ | `lib/screens/constellation_screen.dart` |
| 历史记录 | 预测记录 | 记录管理、收藏功能 | ✅ | `lib/screens/history_screen.dart` |
| 个人中心 | 用户信息 | 个人资料、设置管理 | ✅ | `lib/screens/profile_screen.dart` |

## 数据模型
### 核心实体
- **UserModel**: 用户信息 (支持JSON序列化) ✅
- **ChatMessage**: 聊天消息 (支持JSON序列化) ✅  
- **ConstellationFortune**: 星座运势 (支持JSON序列化) ✅
- **PredictionRecord**: 预测记录 (支持JSON序列化) ✅
- **DailyFortune**: 今日运势 (支持JSON序列化) ✅

## 技术实现细节

### 网络层设计
- **单例模式**: ApiService使用单例确保全局唯一实例
- **自动Token管理**: 请求拦截器自动添加认证Token
- **统一错误处理**: 网络异常和业务错误统一处理
- **离线降级**: API失败时自动使用本地数据

### 状态管理
- **Provider模式**: 使用Provider进行状态管理
- **数据持久化**: 用户Token本地存储
- **实时更新**: 数据变更自动通知UI更新

### 用户体验优化
- **连接状态指示**: 实时显示后端连接状态
- **加载状态管理**: 网络请求期间显示加载动画
- **错误容错**: 网络异常时显示友好提示信息
- **数据预加载**: 应用启动时预加载必要数据

### 安全特性
- **Token自动刷新**: Token过期自动处理
- **请求超时控制**: 10秒连接/接收超时
- **用户状态验证**: 启动时验证用户登录状态

### 今日运势功能 🆕
- **AI驱动**: 集成火山方舟DeepSeek模型生成个性化运势内容
- **智能缓存**: 内存缓存+数据库存储，确保每日24点自动更新
- **实时显示**: 显示当日准确日期和农历信息
- **丰富内容**: 包含今日宜忌、幸运色彩、幸运数字、财运事业感情健康提醒
- **优雅交互**: 支持手动刷新、详情查看、加载状态展示
- **容错处理**: 网络异常时自动降级到默认精美运势内容

## 开发状态跟踪
| 功能模块 | 开发状态 | API对接 | 测试状态 |
|:--------:|:--------:|:-------:|:--------:|
| 用户认证 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |
| AI聊天 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |
| 今日运势 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |
| 八字预测 | ✅ 完成 | ✅ 已对接 | ⏳ 待测试 |
| 紫微斗数 | ✅ 完成 | ✅ 已对接 | ⏳ 待测试 |
| 六爻预测 | ✅ 完成 | ✅ 已对接 | ⏳ 待测试 |
| 星座运势 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |
| 历史记录 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |
| 个人中心 | ✅ 完成 | ✅ 已对接 | ✅ 已测试 |

## 部署说明
### 前端Flutter应用
```bash
# 开发模式运行
flutter run

# 构建发布版本
flutter build apk --release  # Android
flutter build ios --release  # iOS
```

### 后端Node.js服务
```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 生产模式运行
npm start
```

### 环境配置
- **Flutter**: SDK >=3.0.0 <4.0.0
- **Node.js**: >=18.0.0
- **后端端口**: 3000
- **数据库**: LeanCloud

## 后续规划
- [ ] 添加用户反馈功能
- [ ] 集成支付系统
- [ ] 增加社区分享功能
- [ ] 优化AI算法精度
- [ ] 添加多语言支持 