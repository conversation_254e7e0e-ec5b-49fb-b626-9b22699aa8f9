<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>星座运势数据管理</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #6366F1;
      text-align: center;
      margin-bottom: 30px;
    }
    .card {
      background-color: #EDE9FE;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }
    .card h2 {
      color: #8B5CF6;
      margin-top: 0;
    }
    button {
      background-color: #8B5CF6;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      transition: background-color 0.3s;
    }
    button:hover {
      background-color: #7C3AED;
    }
    button:disabled {
      background-color: #C4B5FD;
      cursor: not-allowed;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 5px;
      display: none;
    }
    .success {
      background-color: #D1FAE5;
      color: #047857;
    }
    .error {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
      margin-left: 10px;
      vertical-align: middle;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    pre {
      background-color: #F3F4F6;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .token-input {
      width: 100%;
      padding: 10px;
      margin-bottom: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>星座运势数据管理</h1>
    
    <div class="card">
      <h2>管理员认证</h2>
      <p>请输入管理员令牌以继续操作：</p>
      <input type="password" id="adminToken" class="token-input" placeholder="管理员令牌">
    </div>

    <div class="card">
      <h2>刷新星座运势数据</h2>
      <p>点击下方按钮刷新所有星座的今日运势数据。这将使用火山方舟AI重新生成所有星座的详细运势。</p>
      <button id="refreshBtn">刷新星座运势</button>
      <button id="forceRefreshBtn">强制刷新</button>
      <span id="loadingIndicator" class="loading" style="display: none;"></span>
      
      <div id="resultBox" class="result">
        <h3>操作结果：</h3>
        <pre id="resultContent"></pre>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const refreshBtn = document.getElementById('refreshBtn');
      const forceRefreshBtn = document.getElementById('forceRefreshBtn');
      const adminTokenInput = document.getElementById('adminToken');
      const loadingIndicator = document.getElementById('loadingIndicator');
      const resultBox = document.getElementById('resultBox');
      const resultContent = document.getElementById('resultContent');
      
      // 刷新星座运势
      refreshBtn.addEventListener('click', function() {
        refreshConstellations(false);
      });
      
      // 强制刷新星座运势
      forceRefreshBtn.addEventListener('click', function() {
        refreshConstellations(true);
      });
      
      // 刷新函数
      function refreshConstellations(force) {
        const token = adminTokenInput.value.trim();
        if (!token) {
          showResult('请输入管理员令牌', 'error');
          return;
        }
        
        // 显示加载状态
        setLoading(true);
        
        // 发送请求
        fetch('/api/v1/constellations/refresh-all' + (force ? '?force=true' : ''), {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
        .then(response => {
          if (!response.ok) {
            throw new Error('请求失败: ' + response.status);
          }
          return response.json();
        })
        .then(data => {
          showResult(JSON.stringify(data, null, 2), 'success');
        })
        .catch(error => {
          showResult('操作失败: ' + error.message, 'error');
        })
        .finally(() => {
          setLoading(false);
        });
      }
      
      // 显示结果
      function showResult(message, type) {
        resultContent.textContent = message;
        resultBox.style.display = 'block';
        resultBox.className = 'result ' + type;
      }
      
      // 设置加载状态
      function setLoading(isLoading) {
        refreshBtn.disabled = isLoading;
        forceRefreshBtn.disabled = isLoading;
        adminTokenInput.disabled = isLoading;
        loadingIndicator.style.display = isLoading ? 'inline-block' : 'none';
      }
    });
  </script>
</body>
</html> 