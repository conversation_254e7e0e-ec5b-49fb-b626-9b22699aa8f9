const AV = require('leanengine');

// 预测记录类
const PredictionRecord = AV.Object.extend('PredictionRecord');

// 预测类型枚举
PredictionRecord.TYPES = {
  BAZI: 'bazi',
  ZIWEI: 'ziwei',
  LIUYAO: 'liuyao',
  CONSTELLATION: 'constellation',
  COMBO: 'combo',
  AI: 'ai'
};

// 创建预测记录
PredictionRecord.createRecord = async (userId, type, title, description, result) => {
  const record = new PredictionRecord();
  record.set('user', AV.Object.createWithoutData('_User', userId));
  record.set('type', type);
  record.set('title', title);
  record.set('description', description);
  record.set('result', result);
  record.set('isFavorite', false);
  
  return await record.save();
};

// 获取用户的预测记录
PredictionRecord.getUserRecords = async (userId, page = 1, limit = 10, type = null) => {
  const query = new AV.Query(PredictionRecord);
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  
  if (type) {
    query.equalTo('type', type);
  }
  
  query.descending('createdAt');
  query.skip((page - 1) * limit);
  query.limit(limit);
  
  const records = await query.find();
  const count = await query.count();
  
  return {
    records: records.map(record => ({
      id: record.id,
      type: record.get('type'),
      title: record.get('title'),
      description: record.get('description'),
      result: record.get('result'),
      isFavorite: record.get('isFavorite'),
      createdAt: record.get('createdAt')
    })),
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(count / limit),
      totalRecords: count
    }
  };
};

// 切换收藏状态
PredictionRecord.toggleFavorite = async (recordId, userId) => {
  const query = new AV.Query(PredictionRecord);
  query.equalTo('objectId', recordId);
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  
  const record = await query.first();
  if (!record) {
    throw new Error('记录不存在');
  }
  
  const currentFavorite = record.get('isFavorite');
  record.set('isFavorite', !currentFavorite);
  await record.save();
  
  return {
    id: record.id,
    isFavorite: !currentFavorite
  };
};

module.exports = PredictionRecord; 