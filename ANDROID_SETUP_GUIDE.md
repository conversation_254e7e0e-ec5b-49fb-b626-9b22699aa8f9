# 📱 Android Studio 运行配置指南

## ✅ 已完成的配置

### 1. 基础配置
- ✅ **包名**: `com.mantianshenfo.yinfa_shenfo`
- ✅ **应用名**: 满天神佛
- ✅ **最低SDK**: Android 5.0 (API 21)
- ✅ **目标SDK**: Android 14 (API 34)
- ✅ **版本**: 1.0.0 (Build 1)

### 2. 权限配置
```xml
<!-- 已添加的权限 -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### 3. 构建配置
- ✅ **Gradle配置**: 已优化内存使用和并行编译
- ✅ **ProGuard规则**: 已配置代码混淆规则
- ✅ **主题样式**: 支持日夜间模式
- ✅ **Debug/Release配置**: 分别优化

### 4. 网络配置
- ✅ **HTTP支持**: Debug模式允许明文传输
- ✅ **API地址**: http://localhost:3001/api/v1
- ✅ **连接状态检测**: 实时显示后端连接状态

## 🚀 在Android Studio中运行

### 方法1: 直接运行
1. 打开Android Studio
2. 选择 `File` → `Open` → 选择项目根目录 `mantianshenfo/Front`
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击绿色播放按钮 ▶️

### 方法2: 命令行运行
```bash
# 1. 检查设备连接
flutter devices

# 2. 运行到Android设备
flutter run -d android

# 3. 构建APK
flutter build apk --debug
```

## 📱 设备要求

### 推荐配置
- **Android版本**: 7.0+ (API 24+)
- **RAM**: 至少2GB
- **存储**: 至少100MB可用空间
- **网络**: WiFi或移动数据

### 测试设备
- **模拟器**: Pixel 6 Pro (API 33)
- **真机**: 任何Android 5.0+设备

## 🔧 调试功能

### 热重载
- ✅ 保存文件自动重载
- ✅ 按`r`键手动重载
- ✅ 按`R`键完全重启

### 开发工具
- ✅ Flutter Inspector
- ✅ 网络请求监控
- ✅ 性能分析
- ✅ 日志查看

## 🐛 常见问题解决

### 1. 网络连接问题
**问题**: 后端显示离线
```bash
# 解决方案
cd backend
npm run dev  # 确保后端运行在3001端口
```

### 2. Gradle同步失败
**问题**: 代理或网络错误
```bash
# 解决方案
flutter clean
flutter pub get
# 或使用镜像源（已在配置中预留）
```

### 3. 设备连接问题
```bash
# 检查设备
adb devices
flutter devices

# 重启ADB
adb kill-server
adb start-server
```

### 4. 首次运行慢
**原因**: 需要下载依赖
**解决**: 耐心等待，后续运行会很快

## 📦 打包发布

### Debug版本
```bash
flutter build apk --debug
# 输出: build/app/outputs/flutter-apk/app-debug.apk
```

### Release版本
```bash
flutter build apk --release
# 输出: build/app/outputs/flutter-apk/app-release.apk
```

### Google Play版本
```bash
flutter build appbundle --release
# 输出: build/app/outputs/bundle/release/app-release.aab
```

## 🎯 VS Code调试配置

已创建调试配置文件 `.vscode/launch.json`:
- 满天神佛 (Debug - Android)
- 满天神佛 (Release - Android)
- 满天神佛 (Profile - Android)

## 📝 开发注意事项

### 性能优化
- ✅ 启用R8代码混淆
- ✅ 资源压缩
- ✅ 多线程编译
- ✅ Gradle缓存

### 安全配置
- ✅ 网络安全配置
- ✅ 权限最小化
- ✅ 代码混淆保护

### 用户体验
- ✅ 启动画面优化
- ✅ 状态栏透明
- ✅ 夜间模式支持
- ✅ 手势导航适配

## 🔍 当前状态

✅ **配置完成**: 所有Android运行配置已就绪
✅ **编译通过**: Flutter analyze通过（仅有样式建议）
✅ **权限正确**: 网络和存储权限已配置
✅ **主题美观**: 支持日夜间模式
✅ **性能优化**: Gradle和构建优化完成

## 🎉 准备就绪！

你的"满天神佛"应用现在已经完全配置好在Android Studio中运行。只需：

1. 打开Android Studio
2. 导入项目
3. 连接设备
4. 点击运行

应用将在Android设备上完美运行！🚀

---

如有问题，请参考 [主README](./README.md) 或 [Android开发指南](./README_ANDROID.md)。 