const express = require('express');
const AuthUtils = require('../utils/auth');
const ValidationUtils = require('../utils/validation');
const ConstellationFortune = require('../models/ConstellationFortune');
const router = express.Router();

// 获取所有星座今日运势
router.get('/today', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const fortunes = await ConstellationFortune.getTodayFortunes();
    res.json(fortunes);
  } catch (error) {
    console.error('获取今日运势失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取今日运势失败'
      }
    });
  }
});

// 获取指定星座运势
router.get('/:type', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { period = 'today' } = req.query;

    // 验证星座类型
    if (!ValidationUtils.validateConstellationType(type)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的星座类型'
        }
      });
    }

    // 验证时间周期
    if (!ValidationUtils.validateConstellationPeriod(period)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的时间周期，支持：today, week, month'
        }
      });
    }

    const fortune = await ConstellationFortune.getConstellationFortune(type, period);
    res.json(fortune);
  } catch (error) {
    console.error('获取星座运势失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取星座运势失败'
      }
    });
  }
});

// 获取星座运势历史
router.get('/:type/history', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { page = 1, limit = 10 } = req.query;

    // 验证星座类型
    if (!ValidationUtils.validateConstellationType(type)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的星座类型'
        }
      });
    }

    const query = new AV.Query('ConstellationFortune');
    query.equalTo('type', type);
    query.descending('createdAt');
    query.skip((page - 1) * limit);
    query.limit(parseInt(limit));

    const fortunes = await query.find();
    const count = await query.count();

    res.json({
      fortunes: fortunes.map(fortune => ({
        id: fortune.id,
        type: fortune.get('type'),
        name: ConstellationFortune.NAMES[fortune.get('type')],
        period: fortune.get('period'),
        overallRating: fortune.get('overallRating'),
        summary: fortune.get('summary'),
        date: fortune.get('date'),
        createdAt: fortune.get('createdAt')
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(count / limit),
        totalRecords: count
      }
    });
  } catch (error) {
    console.error('获取星座运势历史失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取星座运势历史失败'
      }
    });
  }
});

// 获取所有星座信息
router.get('/', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const constellations = Object.entries(ConstellationFortune.NAMES).map(([type, name]) => ({
      type,
      name,
      dateRange: ConstellationFortune.DATE_RANGES[type]
    }));

    res.json(constellations);
  } catch (error) {
    console.error('获取星座信息失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取星座信息失败'
      }
    });
  }
});

// 根据生日获取星座
router.post('/detect', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { birthDate } = req.body;

    if (!birthDate) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '生日不能为空'
        }
      });
    }

    // 验证日期格式
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(birthDate)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '日期格式错误，应为YYYY-MM-DD'
        }
      });
    }

    const month = parseInt(birthDate.split('-')[1]);
    const day = parseInt(birthDate.split('-')[2]);

    // 验证月份和日期
    if (month < 1 || month > 12 || day < 1 || day > 31) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的日期'
        }
      });
    }

    // 星座判断逻辑
    const getConstellation = (month, day) => {
      const dates = [
        { name: '摩羯座', type: 'capricorn', start: [12, 22], end: [1, 19] },
        { name: '水瓶座', type: 'aquarius', start: [1, 20], end: [2, 18] },
        { name: '双鱼座', type: 'pisces', start: [2, 19], end: [3, 20] },
        { name: '白羊座', type: 'aries', start: [3, 21], end: [4, 19] },
        { name: '金牛座', type: 'taurus', start: [4, 20], end: [5, 20] },
        { name: '双子座', type: 'gemini', start: [5, 21], end: [6, 21] },
        { name: '巨蟹座', type: 'cancer', start: [6, 22], end: [7, 22] },
        { name: '狮子座', type: 'leo', start: [7, 23], end: [8, 22] },
        { name: '处女座', type: 'virgo', start: [8, 23], end: [9, 22] },
        { name: '天秤座', type: 'libra', start: [9, 23], end: [10, 23] },
        { name: '天蝎座', type: 'scorpio', start: [10, 24], end: [11, 22] },
        { name: '射手座', type: 'sagittarius', start: [11, 23], end: [12, 21] }
      ];

      for (const constellation of dates) {
        const [startMonth, startDay] = constellation.start;
        const [endMonth, endDay] = constellation.end;

        if (startMonth === endMonth) {
          if (month === startMonth && day >= startDay && day <= endDay) {
            return constellation;
          }
        } else {
          if ((month === startMonth && day >= startDay) || (month === endMonth && day <= endDay)) {
            return constellation;
          }
        }
      }

      return { name: '白羊座', type: 'aries' }; // 默认返回白羊座
    };

    const constellation = getConstellation(month, day);
    
    res.json({
      birthDate,
      constellation: {
        type: constellation.type,
        name: constellation.name,
        dateRange: ConstellationFortune.DATE_RANGES[constellation.type]
      }
    });
  } catch (error) {
    console.error('星座检测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '星座检测失败'
      }
    });
  }
});

// 星座配对分析
router.post('/compatibility', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { type1, type2 } = req.body;

    if (!type1 || !type2) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '星座类型不能为空'
        }
      });
    }

    // 验证星座类型
    if (!ValidationUtils.validateConstellationType(type1) || !ValidationUtils.validateConstellationType(type2)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的星座类型'
        }
      });
    }

    // 简化的配对分析
    const compatibility = {
      overall: Math.floor(Math.random() * 5) + 1,
      love: Math.floor(Math.random() * 5) + 1,
      friendship: Math.floor(Math.random() * 5) + 1,
      work: Math.floor(Math.random() * 5) + 1
    };

    const analysis = {
      summary: `${ConstellationFortune.NAMES[type1]}和${ConstellationFortune.NAMES[type2]}的配对分析`,
      strengths: ['性格互补', '共同目标', '相互理解'],
      challenges: ['沟通方式不同', '价值观差异', '生活节奏不同'],
      advice: '建议双方多沟通理解，发挥各自优势，共同成长。'
    };

    res.json({
      constellation1: {
        type: type1,
        name: ConstellationFortune.NAMES[type1]
      },
      constellation2: {
        type: type2,
        name: ConstellationFortune.NAMES[type2]
      },
      compatibility,
      analysis
    });
  } catch (error) {
    console.error('星座配对分析失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '星座配对分析失败'
      }
    });
  }
});

// 新增: 使用AI获取星座运势
router.get('/:type/ai-fortune', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { type } = req.params;

    // 验证星座类型
    if (!ValidationUtils.validateConstellationType(type)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的星座类型'
        }
      });
    }

    console.log(`👉 请求AI星座运势: ${type}`);
    const fortune = await ConstellationFortune.getAIConstellationFortune(type);
    console.log(`✅ AI星座运势请求成功: ${type}`);

    res.json(fortune);
  } catch (error) {
    console.error('AI星座运势解析失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: 'AI星座运势解析失败'
      }
    });
  }
});

// 新增: 手动刷新所有星座运势（管理员功能）
router.post('/refresh-all', AuthUtils.adminAuthenticate, async (req, res) => {
  try {
    console.log('👤 管理员请求：手动刷新所有星座运势');
    
    // 检查是否需要强制刷新
    const forceRefresh = req.query.force === 'true' || req.body.force === true;
    
    const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
    if (isUpdated && !forceRefresh) {
      return res.json({
        success: true,
        message: '今日星座运势已更新，无需重复更新',
        alreadyUpdated: true
      });
    }
    
    console.log(`🔄 开始${forceRefresh ? '强制' : ''}手动刷新所有星座运势...`);
    const results = await ConstellationFortune.updateAllDailyFortunes();
    console.log('✅ 所有星座运势刷新成功');
    
    res.json({
      success: true,
      message: '所有星座运势刷新成功',
      count: results.length,
      updated: new Date(),
      forced: forceRefresh
    });
  } catch (error) {
    console.error('❌ 刷新星座运势失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '刷新星座运势失败'
      }
    });
  }
});

// 新增: 使用AI获取星座+紫微组合分析
router.post('/:type/ai-fortune', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { birthDateTime, constellation, analysisType, includeZiwei } = req.body;

    // 验证星座类型
    if (!ValidationUtils.validateConstellationType(type)) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '无效的星座类型'
        }
      });
    }

    // 检查是否为组合分析请求
    const isComboAnalysis = analysisType === 'combo' && includeZiwei === true;
    
    console.log(`👉 请求${isComboAnalysis ? '星座+紫微组合' : 'AI星座'}运势: ${type}`);
    console.log(`📅 出生日期时间: ${birthDateTime}`);
    console.log(`🔮 分析类型: ${analysisType || '标准'}`);

    // 导入AIService
    const AIService = require('../services/AIService');
    const aiService = new AIService();

    let result;
    if (isComboAnalysis && birthDateTime && constellation) {
      // 使用专门的星座+紫微组合分析方法
      try {
        console.log('🚀 开始调用火山方舟AI进行星座+紫微组合分析...');
        result = await aiService.getConstellationZiweiAnalysis(birthDateTime, constellation);
        console.log('✅ AI分析完成');
      } catch (aiError) {
        console.error('AI调用失败:', aiError);
        throw new Error(`调用AI服务失败: ${aiError.message}`);
      }
    } else {
      // 原来的星座AI运势逻辑
      result = await ConstellationFortune.getAIConstellationFortune(type);
    }

    console.log(`✅ ${isComboAnalysis ? '星座+紫微组合' : 'AI星座'}运势请求成功: ${type}`);
    res.json(result);
  } catch (error) {
    console.error('AI星座运势分析失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: 'AI星座运势分析失败: ' + error.message
      }
    });
  }
});

module.exports = router; 