import 'package:flutter/foundation.dart';
import '../models/prediction_record.dart';
import '../services/api_service.dart';

class PredictionProvider with ChangeNotifier {
  List<PredictionRecord> _records = [];
  bool _isLoading = false;
  PredictionType? _selectedFilter;

  List<PredictionRecord> get records => _records;
  bool get isLoading => _isLoading;
  PredictionType? get selectedFilter => _selectedFilter;

  // 获取过滤后的记录
  List<PredictionRecord> get filteredRecords {
    if (_selectedFilter == null) {
      return _records;
    }
    return _records.where((record) => record.type == _selectedFilter).toList();
  }

  // 按时间分组的记录
  Map<String, List<PredictionRecord>> get groupedRecords {
    final Map<String, List<PredictionRecord>> grouped = {};
    final now = DateTime.now();
    
    for (final record in filteredRecords) {
      final difference = now.difference(record.createdAt);
      String key;
      
      if (difference.inDays == 0) {
        key = '今天';
      } else if (difference.inDays == 1) {
        key = '昨天';
      } else {
        key = '更早';
      }
      
      if (!grouped.containsKey(key)) {
        grouped[key] = [];
      }
      grouped[key]!.add(record);
    }
    
    // 按时间排序
    for (final key in grouped.keys) {
      grouped[key]!.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    }
    
    return grouped;
  }

  // 获取收藏的记录
  List<PredictionRecord> get favoriteRecords {
    return _records.where((record) => record.isFavorite).toList();
  }

  // 按类型统计记录数量
  Map<PredictionType, int> get recordCountByType {
    final Map<PredictionType, int> count = {};
    for (final type in PredictionType.values) {
      count[type] = _records.where((record) => record.type == type).length;
    }
    return count;
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setFilter(PredictionType? filter) {
    _selectedFilter = filter;
    notifyListeners();
  }

  void addRecord(PredictionRecord record) {
    _records.insert(0, record);
    notifyListeners();
  }

  Future<void> removeRecord(String id) async {
    try {
      final apiService = ApiService();
      await apiService.deletePredictionRecord(id);
      
      _records.removeWhere((record) => record.id == id);
      notifyListeners();
    } catch (e) {
      // 如果API失败，只更新本地状态
      _records.removeWhere((record) => record.id == id);
      notifyListeners();
      print('Remove record failed: $e');
    }
  }

  void updateRecord(PredictionRecord updatedRecord) {
    final index = _records.indexWhere((record) => record.id == updatedRecord.id);
    if (index != -1) {
      _records[index] = updatedRecord;
      notifyListeners();
    }
  }

  Future<void> toggleFavorite(String id) async {
    try {
      final apiService = ApiService();
      final result = await apiService.togglePredictionFavorite(id);
      
      final index = _records.indexWhere((record) => record.id == id);
      if (index != -1) {
        _records[index] = PredictionRecord.fromJson(result);
        notifyListeners();
      }
    } catch (e) {
      // 如果API失败，只更新本地状态
      final index = _records.indexWhere((record) => record.id == id);
      if (index != -1) {
        final record = _records[index];
        _records[index] = record.copyWith(isFavorite: !record.isFavorite);
        notifyListeners();
      }
      print('Toggle favorite failed: $e');
    }
  }

  void clearAllRecords() {
    _records.clear();
    notifyListeners();
  }

  void clearRecordsByType(PredictionType type) {
    _records.removeWhere((record) => record.type == type);
    notifyListeners();
  }

  // 加载历史记录
  Future<void> loadRecords() async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final recordsData = await apiService.getPredictionHistory();
      
      _records = recordsData
          .map((data) => PredictionRecord.fromJson(data))
          .toList();
      
    } catch (e) {
      // 如果API失败，使用本地示例数据
      _records = _generateSampleRecords();
      print('Load prediction records failed: $e');
    } finally {
      setLoading(false);
    }
  }

  // 生成示例记录数据
  List<PredictionRecord> _generateSampleRecords() {
    return [
      PredictionRecord(
        id: '1',
        type: PredictionType.bazi,
        title: '八字预测',
        description: '1995年8月15日 女 - 木火土金组合，事业线佳',
        inputData: {'birthDate': '1995-08-15', 'gender': 'female'},
        resultData: {'wuxing': '木火土金', 'analysis': '事业线佳'},
        createdAt: DateTime.now(),
      ),
      PredictionRecord(
        id: '2',
        type: PredictionType.ziwei,
        title: '紫微斗数',
        description: '1995年8月15日 女 - 紫微天府同宫，财运亨通',
        inputData: {'birthDate': '1995-08-15', 'gender': 'female'},
        resultData: {'stars': '紫微天府', 'analysis': '财运亨通'},
        createdAt: DateTime.now(),
      ),
      PredictionRecord(
        id: '3',
        type: PredictionType.ai,
        title: 'AI咨询',
        description: '关于最近工作变动的咨询 - 聊天记录 (12条)',
        inputData: {'question': '最近工作变动'},
        resultData: {'messageCount': 12, 'summary': '工作变动咨询'},
        createdAt: DateTime.now(),
      ),
      PredictionRecord(
        id: '4',
        type: PredictionType.liuyao,
        title: '六爻预测',
        description: '事业发展 - 泽天夬卦，上进有为，事业有成',
        inputData: {'question': '事业发展'},
        resultData: {'hexagram': '泽天夬', 'analysis': '上进有为，事业有成'},
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PredictionRecord(
        id: '5',
        type: PredictionType.constellation,
        title: '星座预测',
        description: '狮子座 - 本周运势分析，感情有新发展',
        inputData: {'constellation': '狮子座'},
        resultData: {'period': '本周', 'analysis': '感情有新发展'},
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PredictionRecord(
        id: '6',
        type: PredictionType.combo,
        title: '星座组合',
        description: '狮子座与紫微 - 年月日运势分析',
        inputData: {'constellation': '狮子座', 'type': 'constellation+ziwei'},
        resultData: {'period': '年月日', 'analysis': '运势分析'},
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
      PredictionRecord(
        id: '7',
        type: PredictionType.combo,
        title: '八字组合',
        description: '八字与紫微整体运势组合 - 2024年运势分析',
        inputData: {'type': 'bazi+ziwei'},
        resultData: {'year': '2024', 'analysis': '运势分析'},
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    ];
  }

  // 创建八字预测记录 - 支持AI解读和游客模式
  Future<Map<String, dynamic>> createBaziRecord({
    required String birthDate,
    required String gender,
    String? birthTime,
    Map<String, dynamic>? inputData,
  }) async {
    try {
      print('🔮 开始创建八字预测记录...');
      print('📥 输入数据: birthDate=$birthDate, gender=$gender, birthTime=$birthTime');
      
      final apiService = ApiService();
      final result = await apiService.createBaziPrediction({
        'birthDate': birthDate,
        'gender': gender,
        'birthTime': birthTime ?? '12:00',
        if (inputData != null) 'inputData': inputData,
      });
      
      print('📦 后端响应: $result');
      print('🔍 响应结构检查:');
      print('   - 包含record: ${result.containsKey('record')}');
      print('   - 包含result: ${result.containsKey('result')}');
      
      // 检查是否为游客模式响应（只有result，没有record）
      if (result.containsKey('result') && !result.containsKey('record')) {
        print('🎭 检测到游客模式响应，创建本地记录结构');
        final actualResult = result['result'] as Map<String, dynamic>;
        
        // 为游客模式创建记录结构
        final record = PredictionRecord(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: PredictionType.bazi,
          title: '八字预测',
          description: '$birthDate $gender - ${actualResult['description'] ?? '八字AI解读'}',
          inputData: {'birthDate': birthDate, 'gender': gender, 'birthTime': birthTime ?? '12:00', ...?inputData},
          resultData: actualResult,
          createdAt: DateTime.now(),
        );
        
        addRecord(record);
        print('✅ 游客模式记录创建成功');
        return result;
      } else if (result.containsKey('record')) {
        // 登录用户模式，使用后端返回的record
        print('👤 登录用户模式，解析后端record');
        final record = PredictionRecord.fromJson(result['record']);
        addRecord(record);
        print('✅ 登录用户记录创建成功');
        return result;
      } else {
        throw Exception('后端响应格式异常');
      }
    } catch (e) {
      print('❌ 八字预测API调用失败: $e');
      // 如果API失败，创建本地记录
      final record = PredictionRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PredictionType.bazi,
        title: '八字预测',
        description: '$birthDate $gender - 八字预测结果',
        inputData: {'birthDate': birthDate, 'gender': gender, 'birthTime': birthTime ?? '12:00', ...?inputData},
        resultData: {'summary': '八字预测结果', 'analysis': '本地生成的八字预测结果，网络错误时的备用数据'},
        createdAt: DateTime.now(),
      );
      
      addRecord(record);
      print('🔄 使用本地预设数据创建记录');
      
      // 返回本地数据格式
      return {
        'result': record.resultData,
      };
    }
  }

  // 创建六爻预测记录
  Future<PredictionRecord> createLiuyaoRecord({
    required String question,
    required Map<String, dynamic> inputData,
  }) async {
    try {
      print('🔮 开始创建六爻预测记录...');
      print('📥 输入数据: $inputData');
      
      final apiService = ApiService();
      // 直接传递inputData，因为它已经包含了question
      final result = await apiService.createLiuyaoPrediction(inputData);
      
      print('📦 后端响应: $result');
      print('🔍 响应结构检查:');
      print('   - 包含record: ${result.containsKey('record')}');
      print('   - 包含result: ${result.containsKey('result')}');
      
      // 检查是否为游客模式响应（只有result，没有record）
      if (result.containsKey('result') && !result.containsKey('record')) {
        print('🎭 检测到游客模式响应，创建本地记录结构');
        final actualResult = result['result'] as Map<String, dynamic>;
        
        // 为游客模式创建记录结构
        final record = PredictionRecord(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: PredictionType.liuyao,
          title: '六爻预测',
          description: '$question - ${actualResult['hexagramName'] ?? '六爻卦象预测'}',
          inputData: {'question': question, ...inputData},
          resultData: actualResult,
          createdAt: DateTime.now(),
        );
        
        addRecord(record);
        print('✅ 游客模式记录创建成功');
        return record;
      } else if (result.containsKey('record')) {
        // 登录用户模式，使用后端返回的record
        print('👤 登录用户模式，解析后端record');
        final record = PredictionRecord.fromJson(result['record']);
        addRecord(record);
        print('✅ 登录用户记录创建成功');
        return record;
      } else {
        throw Exception('后端响应格式异常');
      }
    } catch (e) {
      print('❌ 六爻预测API调用失败: $e');
      // 如果API失败，创建本地记录
      final record = PredictionRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PredictionType.liuyao,
        title: '六爻预测',
        description: '$question - 六爻卦象预测',
        inputData: {'question': question, ...inputData},
        resultData: {'hexagram': '泽天夬', 'analysis': '本地生成的六爻预测结果'},
        createdAt: DateTime.now(),
      );
      
      addRecord(record);
      print('🔄 使用本地预设数据创建记录');
      return record;
    }
  }

  // 创建紫微斗数记录 - 支持AI解读
  Future<Map<String, dynamic>> createZiweiRecord({
    required String birthDate,
    required String gender,
    required Map<String, dynamic> inputData,
  }) async {
    try {
      print('🔮 开始创建紫微斗数预测记录...');
      print('📥 输入数据: birthDate=$birthDate, gender=$gender, inputData=$inputData');
      
      final apiService = ApiService();
      final result = await apiService.createZiweiPrediction({
        'birthDate': birthDate,
        'gender': gender,
        'birthTime': inputData['birthTime'] ?? '12:00',
      });
      
      print('📦 后端响应: $result');
      print('🔍 响应结构检查:');
      print('   - 包含record: ${result.containsKey('record')}');
      print('   - 包含result: ${result.containsKey('result')}');
      
      if (result.containsKey('result')) {
        final resultData = result['result'];
        print('   - result包含palaces: ${resultData.containsKey('palaces')}');
        
        // 如果API返回的数据中没有palaces字段，添加备用的宫位数据
        if (!resultData.containsKey('palaces') || resultData['palaces'] == null) {
          print('⚠️ API返回数据中缺少palaces字段，添加备用数据');
          resultData['palaces'] = _generateFakePalaces();
        }
      }
      
      // 检查是否为游客模式响应（只有result，没有record）
      if (result.containsKey('result') && !result.containsKey('record')) {
        print('🎭 检测到游客模式响应，创建本地记录结构');
        final actualResult = result['result'] as Map<String, dynamic>;
        
        // 为游客模式创建记录结构
        final record = PredictionRecord(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: PredictionType.ziwei,
          title: '紫微斗数',
          description: '$birthDate $gender - ${actualResult['description'] ?? '紫微斗数AI解读'}',
          inputData: {'birthDate': birthDate, 'gender': gender, ...inputData},
          resultData: actualResult,
          createdAt: DateTime.now(),
        );
        
        addRecord(record);
        print('✅ 游客模式记录创建成功');
        return result;
      } else if (result.containsKey('record')) {
        // 登录用户模式，使用后端返回的record
        print('👤 登录用户模式，解析后端record');
        final record = PredictionRecord.fromJson(result['record']);
        addRecord(record);
        print('✅ 登录用户记录创建成功');
        return result;
      } else {
        throw Exception('后端响应格式异常');
      }
    } catch (e) {
      print('❌ 紫微斗数预测API调用失败: $e');
      // 如果API失败，创建本地记录，并提供备用的命盘数据
      
      // 创建备用命盘数据
      final fakePalaces = _generateFakePalaces();
      
      final record = PredictionRecord(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: PredictionType.ziwei,
        title: '紫微斗数',
        description: '$birthDate $gender - 紫微斗数分析',
        inputData: {'birthDate': birthDate, 'gender': gender, ...inputData},
        resultData: {
          'palaces': fakePalaces, // 添加备用命盘数据
          'analysis': {
            'life': '本地生成的命宫解析，仅用于演示。真实数据需要连接后端服务获取。',
            'wealth': '本地生成的财运分析，仅用于演示。真实数据需要连接后端服务获取。',
            'career': '本地生成的事业运势，仅用于演示。真实数据需要连接后端服务获取。',
            'relationships': '本地生成的情感婚姻，仅用于演示。真实数据需要连接后端服务获取。',
            'health': '本地生成的健康建议，仅用于演示。真实数据需要连接后端服务获取。'
          }
        },
        createdAt: DateTime.now(),
      );
      
      addRecord(record);
      print('🔄 使用本地预设数据创建记录');
      
      // 返回本地数据格式
      return {
        'result': record.resultData,
      };
    }
  }
  
  // 生成备用的紫微命盘宫位数据
  List<Map<String, dynamic>> _generateFakePalaces() {
    print('🏯 生成备用紫微命盘宫位数据');
    return [
      {'position': '命宫', 'stars': ['紫微', '天相']},
      {'position': '兄弟宫', 'stars': ['武曲', '天梁']},
      {'position': '夫妻宫', 'stars': ['天机', '巨门']},
      {'position': '子女宫', 'stars': ['太阳', '天同']},
      {'position': '财帛宫', 'stars': ['廉贞', '天府']},
      {'position': '疾厄宫', 'stars': ['太阴', '贪狼']},
      {'position': '迁移宫', 'stars': ['破军', '七杀']},
      {'position': '奴仆宫', 'stars': ['左辅', '右弼']},
      {'position': '官禄宫', 'stars': ['文曲', '文昌']},
      {'position': '田宅宫', 'stars': ['擎羊', '陀罗']},
      {'position': '福德宫', 'stars': ['火星', '铃星']},
      {'position': '父母宫', 'stars': ['禄存', '化禄']},
    ];
  }

  // 创建星座运势记录
  PredictionRecord createConstellationRecord({
    required String constellation,
    required Map<String, dynamic> resultData,
  }) {
    final record = PredictionRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: PredictionType.constellation,
      title: '星座预测',
      description: '$constellation - ${resultData['summary'] ?? '星座运势分析'}',
      inputData: {'constellation': constellation},
      resultData: resultData,
      createdAt: DateTime.now(),
    );
    
    addRecord(record);
    return record;
  }

  // 创建AI咨询记录
  PredictionRecord createAIRecord({
    required String question,
    required int messageCount,
    required String summary,
  }) {
    final record = PredictionRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: PredictionType.ai,
      title: 'AI咨询',
      description: '$question - 聊天记录 ($messageCount条)',
      inputData: {'question': question},
      resultData: {'messageCount': messageCount, 'summary': summary},
      createdAt: DateTime.now(),
    );
    
    addRecord(record);
    return record;
  }

  // 创建预测组合记录
  PredictionRecord createComboRecord({
    required String comboType,
    required Map<String, dynamic> inputData,
    required Map<String, dynamic> resultData,
  }) {
    final record = PredictionRecord(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: PredictionType.combo,
      title: '预测组合',
      description: '$comboType - ${resultData['summary'] ?? '组合预测分析'}',
      inputData: {'comboType': comboType, ...inputData},
      resultData: resultData,
      createdAt: DateTime.now(),
    );
    
    addRecord(record);
    return record;
  }
} 