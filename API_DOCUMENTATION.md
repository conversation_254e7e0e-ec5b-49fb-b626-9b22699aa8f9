# 银发-满天神佛 API接口文档

版本: 1.0.0
基础URL: `/api/v1`

---

## 目录
1.  [简介](#简介)
2.  [认证机制](#认证机制)
3.  [通用约定](#通用约定)
    - [数据格式](#数据格式)
    - [响应结构](#响应结构)
    - [错误码](#错误码)
4.  [接口列表](#接口列表)
    - [认证接口 (`/auth`)](#认证接口)
    - [用户接口 (`/user`)](#用户接口)
    - [预测接口 (`/predictions`)](#预测接口)
    - [星座运势接口 (`/constellations`)](#星座运势接口)
    - [AI咨询接口 (`/chat`)](#ai咨询接口)

---

## 简介
本文档旨在为“银发-满天神佛”应用的前后端分离开发提供统一的接口规范。所有接口均基于RESTful架构设计。

## 认证机制
所有需要用户登录才能访问的接口，都需要在HTTP请求头中携带`Authorization`字段。

- **Header**: `Authorization: Bearer <jwt_token>`

`jwt_token`在用户登录或注册成功后由后端返回。

## 通用约定

### 数据格式
所有请求和响应的数据格式均为`application/json`。

### 响应结构
成功的响应将直接返回数据。失败的响应将遵循统一的结构：

```json
{
  "error": {
    "code": 400,
    "message": "请求参数错误: 'nickname'不能为空"
  }
}
```

### 错误码
- `200 OK`: 请求成功。
- `201 Created`: 资源创建成功。
- `400 Bad Request`: 请求无效，如参数错误、格式错误等。
- `401 Unauthorized`: 用户未认证或认证失败。
- `403 Forbidden`: 用户无权限访问该资源。
- `404 Not Found`: 请求的资源不存在。
- `500 Internal Server Error`: 服务器内部错误。

---

## 接口列表

### 认证接口 (`/auth`)

#### 1. 用户注册
- **Endpoint**: `POST /auth/register`
- **描述**: 创建一个新用户账号。
- **请求体**:
  ```json
  {
    "nickname": "新用户",
    "password": "password123"
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "user": {
      "id": "user-id-123",
      "nickname": "新用户",
      "avatar": "assets/images/default_avatar.png",
      "memberLevel": "普通会员",
      "predictionCount": 0,
      "favoriteCount": 0,
      "consultationCount": 0,
      "createdAt": "2023-10-27T10:00:00Z",
      "lastActiveAt": "2023-10-27T10:00:00Z"
    },
    "token": "your_jwt_token_here"
  }
  ```

#### 2. 用户登录
- **Endpoint**: `POST /auth/login`
- **描述**: 用户使用昵称和密码登录。
- **请求体**:
  ```json
  {
    "nickname": "新用户",
    "password": "password123"
  }
  ```
- **成功响应 (200 OK)**:
  ```json
  {
    "user": {
       "id": "user-id-123",
       "nickname": "新用户",
       "avatar": "assets/images/default_avatar.png",
       "memberLevel": "普通会员",
       "predictionCount": 10,
       "favoriteCount": 2,
       "consultationCount": 5,
       "createdAt": "2023-10-27T10:00:00Z",
       "lastActiveAt": "2023-10-28T12:00:00Z"
    },
    "token": "your_jwt_token_here"
  }
  ```

---

### 用户接口 (`/user`)
**需要认证**

#### 1. 获取当前用户信息
- **Endpoint**: `GET /user/profile`
- **描述**: 获取当前登录用户的详细信息。
- **成功响应 (200 OK)**:
  ```json
  {
    "id": "user-id-123",
    "nickname": "新用户",
    "avatar": "assets/images/default_avatar.png",
    "memberLevel": "普通会员",
    "predictionCount": 10,
    "favoriteCount": 2,
    "consultationCount": 5,
    "createdAt": "2023-10-27T10:00:00Z",
    "lastActiveAt": "2023-10-28T12:00:00Z"
  }
  ```

#### 2. 获取预测历史记录
- **Endpoint**: `GET /user/history`
- **描述**: 获取用户的预测历史记录，支持分页和筛选。
- **查询参数**:
  - `page` (number, optional, default: 1): 页码。
  - `limit` (number, optional, default: 10): 每页数量。
  - `type` (string, optional): 筛选类型 (`bazi`, `ziwei`, `liuyao`, `constellation`, `combo`, `ai`)。
- **成功响应 (200 OK)**:
  ```json
  {
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalRecords": 48
    },
    "records": [
      {
        "id": "record-id-1",
        "type": "bazi",
        "title": "八字预测",
        "description": "1995年8月15日 女 - ...",
        "createdAt": "2023-10-28T11:00:00Z",
        "isFavorite": true
      }
    ]
  }
  ```

#### 3. 收藏/取消收藏预测记录
- **Endpoint**: `POST /user/history/{recordId}/favorite`
- **描述**: 切换指定记录的收藏状态。
- **成功响应 (200 OK)**:
  ```json
  {
    "id": "record-id-1",
    "isFavorite": true
  }
  ```
---

### 预测接口 (`/predictions`)
**需要认证**

#### 1. 八字预测
- **Endpoint**: `POST /predictions/bazi`
- **描述**: 提交八字预测请求。
- **请求体**:
  ```json
  {
    "gender": "female",
    "birthDate": "1995-08-15",
    "birthTime": "14:30"
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "record": {
      "id": "new-record-id",
      "type": "bazi",
      "title": "八字预测",
      "description": "...",
      "createdAt": "2023-10-28T13:00:00Z",
      "isFavorite": false
    },
    "result": {
      "yearColumn": "乙亥",
      "monthColumn": "甲申",
      "dayColumn": "丁酉",
      "hourColumn": "丁未",
      "wuxingCount": { "jin": 2, "mu": 2, "shui": 1, "huo": 2, "tu": 1 },
      "analysis": "此命五行平衡，事业线佳..."
    }
  }
  ```

#### 2. 六爻预测
- **Endpoint**: `POST /predictions/liuyao`
- **描述**: 提交六爻预测请求。
- **请求体**:
  ```json
  {
    "question": "我最近的事业运如何？"
  }
  ```
- **成功响应 (201 Created)**: (后端完成摇卦过程)
  ```json
  {
    "record": {
      "id": "new-record-id-2",
      "type": "liuyao",
      "title": "六爻预测",
      "description": "...",
      "createdAt": "2023-10-28T13:05:00Z",
      "isFavorite": false
    },
    "result": {
      "hexagramName": "泽天夬",
      "changingHexagramName": "乾为天",
      "lines": [
        {"type": "yangLine", "isChanging": false},
        {"type": "yinLine", "isChanging": true},
        ...
      ],
      "analysis": "此卦象预示着..."
    }
  }
  ```

#### 3. 紫微斗数预测
- **Endpoint**: `POST /predictions/ziwei`
- **描述**: 提交紫微斗数预测请求。
- **请求体**:
  ```json
  {
    "gender": "male",
    "birthDate": "1990-05-20",
    "birthTime": "08:15"
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "record": {
      "id": "new-record-id-3",
      "type": "ziwei",
      "title": "紫微斗数",
      "description": "...",
      "createdAt": "2023-10-28T13:10:00Z",
      "isFavorite": false
    },
    "result": {
      "palaces": [
        { "name": "命宫", "stars": ["紫微", "天府"], "position": 0 },
        { "name": "兄弟宫", "stars": ["天机"], "position": 1 },
        ...
      ],
      "analysis": {
        "life": "...",
        "wealth": "...",
        "career": "...",
        "relationships": "...",
        "health": "..."
      }
    }
  }
  ```

#### 4. 组合预测
- **Endpoint**: `POST /predictions/combo`
- **描述**: 提交组合预测请求。
- **请求体**:
  ```json
  {
    "comboType": "bazi_ziwei",
    "inputData": {
      "gender": "male",
      "birthDate": "1990-05-20",
      "birthTime": "08:15"
    }
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "record": {
      "id": "new-record-id-4",
      "type": "combo",
      "title": "八字+紫微组合预测",
      "description": "...",
      "createdAt": "2023-10-28T13:15:00Z",
      "isFavorite": false
    },
    "result": {
      "summary": "...",
      "comparison": {
        "yearly": [
           { "item": "预测周期", "bazi": "...", "ziwei": "..." }
        ],
        "monthly": [],
        "daily": []
      },
      "advice": "..."
    }
  }
  ```
---

### 星座运势接口 (`/constellations`)

#### 1. 获取所有星座今日运势
- **Endpoint**: `GET /constellations/today`
- **描述**: 获取12星座当天的运势概览。
- **成功响应 (200 OK)**:
  ```json
  [
    {
      "type": "aries",
      "name": "白羊座",
      "dateRange": "3月21日 - 4月19日",
      "overallRating": 4
    },
    ...
  ]
  ```

#### 2. 获取指定星座运势
- **Endpoint**: `GET /constellations/{type}`
- **描述**: 获取指定星座的详细运势。
- **路径参数**: `type` (string) - 星座类型, e.g., `aries`
- **查询参数**: `period` (string, optional, default: 'today') - 周期 (`today`, `week`, `month`)
- **成功响应 (200 OK)**:
  ```json
  {
    "type": "aries",
    "name": "白羊座",
    "dateRange": "3月21日 - 4月19日",
    "overallRating": 4,
    "loveRating": 5,
    "moneyRating": 3,
    "careerRating": 4,
    "healthRating": 4,
    "luckyColor": "红色",
    "luckyNumber": "7",
    "summary": "...",
    "loveAdvice": "...",
    "moneyAdvice": "...",
    "careerAdvice": "...",
    "healthAdvice": "..."
  }
  ```
---

### AI咨询接口 (`/chat`)
**需要认证**

#### 1. 获取聊天记录
- **Endpoint**: `GET /chat/history`
- **描述**: 获取当前用户的聊天历史记录。
- **成功响应 (200 OK)**:
  ```json
  [
    {
      "id": "msg-1",
      "type": "user",
      "content": "我最近的运势如何？",
      "timestamp": "2023-10-28T10:00:00Z"
    },
    {
      "id": "msg-2",
      "type": "ai",
      "content": "您的运势正在逐步提升...",
      "timestamp": "2023-10-28T10:00:05Z"
    }
  ]
  ```

#### 2. 发送消息
- **Endpoint**: `POST /chat/message`
- **描述**: 用户发送一条消息，并获取AI的回复。
- **请求体**:
  ```json
  {
    "content": "事业方面有什么建议？"
  }
  ```
- **成功响应 (201 Created)**:
  ```json
  {
    "userMessage": {
      "id": "msg-3",
      "type": "user",
      "content": "事业方面有什么建议？",
      "timestamp": "2023-10-28T14:00:00Z"
    },
    "aiResponse": {
      "id": "msg-4",
      "type": "ai",
      "content": "事业方面，您需要耐心等待时机...",
      "timestamp": "2023-10-28T14:00:03Z"
    }
  }
  ``` 