enum PredictionType {
  bazi,
  ziwei,
  liuyao,
  constellation,
  ai,
  combo,
}

class PredictionRecord {
  final String id;
  final PredictionType type;
  final String title;
  final String description;
  final Map<String, dynamic> inputData;
  final Map<String, dynamic> resultData;
  final DateTime createdAt;
  final bool isFavorite;

  PredictionRecord({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.inputData,
    required this.resultData,
    required this.createdAt,
    this.isFavorite = false,
  });

  factory PredictionRecord.fromJson(Map<String, dynamic> json) {
    return PredictionRecord(
      id: json['id'],
      type: PredictionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PredictionType.bazi,
      ),
      title: json['title'],
      description: json['description'],
      inputData: json['inputData'],
      resultData: json['resultData'],
      createdAt: DateTime.parse(json['createdAt']),
      isFavorite: json['isFavorite'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'description': description,
      'inputData': inputData,
      'resultData': resultData,
      'createdAt': createdAt.toIso8601String(),
      'isFavorite': isFavorite,
    };
  }

  PredictionRecord copyWith({
    String? id,
    PredictionType? type,
    String? title,
    String? description,
    Map<String, dynamic>? inputData,
    Map<String, dynamic>? resultData,
    DateTime? createdAt,
    bool? isFavorite,
  }) {
    return PredictionRecord(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      inputData: inputData ?? this.inputData,
      resultData: resultData ?? this.resultData,
      createdAt: createdAt ?? this.createdAt,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  String get typeDisplayName {
    switch (type) {
      case PredictionType.bazi:
        return '八字预测';
      case PredictionType.ziwei:
        return '紫微斗数';
      case PredictionType.liuyao:
        return '六爻预测';
      case PredictionType.constellation:
        return '星座运势';
      case PredictionType.ai:
        return 'AI咨询';
      case PredictionType.combo:
        return '预测组合';
    }
  }

  String get routeName {
    switch (type) {
      case PredictionType.bazi:
        return '/bazi';
      case PredictionType.ziwei:
        return '/ziwei';
      case PredictionType.liuyao:
        return '/liuyao';
      case PredictionType.constellation:
        return '/constellation';
      case PredictionType.ai:
        return '/ai-chat';
      case PredictionType.combo:
        return '/prediction-combo';
    }
  }
} 