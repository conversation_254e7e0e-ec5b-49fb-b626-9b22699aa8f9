# 端口变更说明

## 问题描述
在启动后端服务时遇到了端口占用错误：
```
Error: listen EADDRINUSE: address already in use :::3001
```

## 解决方案
为了解决端口冲突问题，我们已经将后端服务端口从 **3001** 更改为 **3002**。

## 修改的文件
1. **backend/server.js** - 第109行，将默认端口从3001改为3002
2. **lib/services/api_service.dart** - 第7行，将API基础URL从3001改为3002

## 新的服务地址
- 后端服务：http://localhost:3002
- API接口：http://localhost:3002/api/v1/
- 健康检查：http://localhost:3002/health

## 启动方式
使用以下任一方式启动后端服务：

### 方式1：使用新的启动脚本
```bash
start_backend_3002.bat
```

### 方式2：手动启动
```bash
cd backend
node server.js
```

### 方式3：使用原有脚本（会使用3002端口）
```bash
cd backend
start.bat
```

## 验证服务状态
启动后，可以通过以下方式验证服务是否正常：
1. 浏览器访问：http://localhost:3002/health
2. 前端应用的连接状态显示

## 注意事项
- 如果需要改回3001端口，请同时修改上述两个文件
- 确保防火墙允许3002端口的访问
- 如果仍有问题，请检查是否有其他进程占用3002端口

## 后续步骤
1. 启动后端服务（使用3002端口）
2. 启动前端应用
3. 测试六爻功能和AI分析
4. 验证连接状态显示功能 