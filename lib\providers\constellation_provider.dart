import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:math';
import '../models/constellation_fortune.dart';
import '../services/api_service.dart';

class ConstellationProvider with ChangeNotifier {
  List<ConstellationFortune> _constellations = [];
  ConstellationFortune? _selectedConstellation;
  bool _isLoading = false;
  bool _isLoadingAI = false;  // 新增: AI加载状态
  String _aiLoadingError = '';  // 新增: AI加载错误信息
  
  List<ConstellationFortune> get constellations => _constellations;
  ConstellationFortune? get selectedConstellation => _selectedConstellation;
  bool get isLoading => _isLoading;
  bool get isLoadingAI => _isLoadingAI;  // 新增: 获取AI加载状态
  String get aiLoadingError => _aiLoadingError;  // 新增: 获取AI加载错误信息
  
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  // 新增: 设置AI加载状态
  void setLoadingAI(bool loading, [String error = '']) {
    _isLoadingAI = loading;
    _aiLoadingError = error;
    notifyListeners();
  }

  void setSelectedConstellation(ConstellationFortune? constellation) {
    _selectedConstellation = constellation;
    notifyListeners();
  }

  void selectConstellationByType(ConstellationType type) {
    _selectedConstellation = _constellations.firstWhere(
      (constellation) => constellation.type == type,
      orElse: () => _constellations.first,
    );
    notifyListeners();
  }

  void updateConstellation(ConstellationFortune constellation) {
    final index = _constellations.indexWhere((c) => c.type == constellation.type);
    if (index != -1) {
      _constellations[index] = constellation;
      if (_selectedConstellation?.type == constellation.type) {
        _selectedConstellation = constellation;
      }
      notifyListeners();
    }
  }

  void updateConstellationRatings(
    ConstellationType type, {
    int? overallRating,
    int? loveRating,
    int? moneyRating,
    int? careerRating,
    int? healthRating,
  }) {
    final index = _constellations.indexWhere((c) => c.type == type);
    if (index != -1) {
      final constellation = _constellations[index];
      final updatedConstellation = ConstellationFortune(
        type: constellation.type,
        name: constellation.name,
        symbol: constellation.symbol,
        dateRange: constellation.dateRange,
        overallRating: overallRating ?? constellation.overallRating,
        loveRating: loveRating ?? constellation.loveRating,
        moneyRating: moneyRating ?? constellation.moneyRating,
        careerRating: careerRating ?? constellation.careerRating,
        healthRating: healthRating ?? constellation.healthRating,
        luckyColor: constellation.luckyColor,
        luckyNumber: constellation.luckyNumber,
        luckyDirection: constellation.luckyDirection,
        luckyFood: constellation.luckyFood,
        summary: constellation.summary,
        loveAdvice: constellation.loveAdvice,
        moneyAdvice: constellation.moneyAdvice,
        careerAdvice: constellation.careerAdvice,
        healthAdvice: constellation.healthAdvice,
        lastUpdated: DateTime.now(),
      );
      
      _constellations[index] = updatedConstellation;
      if (_selectedConstellation?.type == type) {
        _selectedConstellation = updatedConstellation;
      }
      notifyListeners();
    }
  }

  // 加载星座数据
  Future<void> loadConstellations() async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final constellationsData = await apiService.getConstellations();
      
      _constellations = constellationsData
          .map((data) => ConstellationFortune.fromJson(data))
          .toList();
      
      // 默认选择第一个星座
      if (_constellations.isNotEmpty) {
        _selectedConstellation = _constellations.first;
      }
      
    } catch (e) {
      // 如果API失败，使用本地数据
      _constellations = ConstellationData.generateAllConstellations();
      if (_constellations.isNotEmpty) {
        _selectedConstellation = _constellations.first;
      }
      print('Load constellations failed: $e');
    } finally {
      setLoading(false);
    }
  }

  // 刷新星座运势
  Future<void> refreshConstellations() async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final constellationsData = await apiService.getConstellations();
      
      _constellations = constellationsData
          .map((data) => ConstellationFortune.fromJson(data))
          .toList();
      
      // 如果有选中的星座，更新选中状态
      if (_selectedConstellation != null) {
        _selectedConstellation = _constellations.firstWhere(
          (c) => c.type == _selectedConstellation!.type,
          orElse: () => _constellations.first,
        );
      }
      
    } catch (e) {
      // 如果API失败，重新生成本地数据
      _constellations = ConstellationData.generateAllConstellations();
      if (_selectedConstellation != null) {
        _selectedConstellation = _constellations.firstWhere(
          (c) => c.type == _selectedConstellation!.type,
          orElse: () => _constellations.first,
        );
      }
      print('Refresh constellations failed: $e');
    } finally {
      setLoading(false);
    }
  }

  // 获取特定星座的详细运势
  Future<void> loadConstellationDetail(ConstellationType type) async {
    try {
      setLoading(true);
      
      final apiService = ApiService();
      final fortuneData = await apiService.getConstellationFortune(type.name);
      
      final updatedFortune = ConstellationFortune.fromJson(fortuneData);
      updateConstellation(updatedFortune);
      
    } catch (e) {
      print('Load constellation detail failed: $e');
    } finally {
      setLoading(false);
    }
  }
  
  // 新增: 获取AI生成的星座运势
  Future<ConstellationFortune?> loadAIConstellationFortune(ConstellationType type) async {
    try {
      setLoadingAI(true);
      
      final apiService = ApiService();
      final fortuneData = await apiService.getAIConstellationFortune(type.name);
      
      final aiGenerated = ConstellationFortune.fromJson(fortuneData);
      updateConstellation(aiGenerated);
      setSelectedConstellation(aiGenerated);
      
      setLoadingAI(false);
      return aiGenerated;
    } catch (e) {
      print('Load AI constellation fortune failed: $e');
      setLoadingAI(false, e.toString());
      return null;
    }
  }

  // 根据生日获取星座
  ConstellationFortune? getConstellationByDate(DateTime birthDate) {
    final month = birthDate.month;
    final day = birthDate.day;
    
    ConstellationType type;
    
    if ((month == 3 && day >= 21) || (month == 4 && day <= 19)) {
      type = ConstellationType.aries;
    } else if ((month == 4 && day >= 20) || (month == 5 && day <= 20)) {
      type = ConstellationType.taurus;
    } else if ((month == 5 && day >= 21) || (month == 6 && day <= 21)) {
      type = ConstellationType.gemini;
    } else if ((month == 6 && day >= 22) || (month == 7 && day <= 22)) {
      type = ConstellationType.cancer;
    } else if ((month == 7 && day >= 23) || (month == 8 && day <= 22)) {
      type = ConstellationType.leo;
    } else if ((month == 8 && day >= 23) || (month == 9 && day <= 22)) {
      type = ConstellationType.virgo;
    } else if ((month == 9 && day >= 23) || (month == 10 && day <= 23)) {
      type = ConstellationType.libra;
    } else if ((month == 10 && day >= 24) || (month == 11 && day <= 22)) {
      type = ConstellationType.scorpio;
    } else if ((month == 11 && day >= 23) || (month == 12 && day <= 21)) {
      type = ConstellationType.sagittarius;
    } else if ((month == 12 && day >= 22) || (month == 1 && day <= 19)) {
      type = ConstellationType.capricorn;
    } else if ((month == 1 && day >= 20) || (month == 2 && day <= 18)) {
      type = ConstellationType.aquarius;
    } else {
      type = ConstellationType.pisces;
    }
    
    return _constellations.firstWhere(
      (constellation) => constellation.type == type,
      orElse: () => _constellations.first,
    );
  }

  // 获取运势评分的颜色
  Color getRatingColor(int rating) {
    switch (rating) {
      case 1:
        return const Color(0xFFEF4444); // 红色
      case 2:
        return const Color(0xFFF97316); // 橙色
      case 3:
        return const Color(0xFFFBBF24); // 黄色
      case 4:
        return const Color(0xFF22C55E); // 绿色
      case 5:
        return const Color(0xFF10B981); // 深绿色
      default:
        return const Color(0xFF6B7280); // 灰色
    }
  }

  // 获取运势评分的文字描述
  String getRatingText(int rating) {
    switch (rating) {
      case 1:
        return '较差';
      case 2:
        return '一般';
      case 3:
        return '良好';
      case 4:
        return '很好';
      case 5:
        return '极佳';
      default:
        return '未知';
    }
  }

  // 获取今日最佳运势的星座
  ConstellationFortune get todayBestConstellation {
    if (_constellations.isEmpty) {
      return ConstellationData.generateAllConstellations().first;
    }
    
    return _constellations.reduce((a, b) => 
      a.overallRating > b.overallRating ? a : b
    );
  }

  // 获取今日最需要注意的星座
  ConstellationFortune get todayWorstConstellation {
    if (_constellations.isEmpty) {
      return ConstellationData.generateAllConstellations().first;
    }
    
    return _constellations.reduce((a, b) => 
      a.overallRating < b.overallRating ? a : b
    );
  }

  // 获取平均运势评分
  double get averageOverallRating {
    if (_constellations.isEmpty) return 0.0;
    
    final total = _constellations.fold<int>(0, (sum, c) => sum + c.overallRating);
    return total / _constellations.length;
  }

  // 搜索星座
  List<ConstellationFortune> searchConstellations(String query) {
    if (query.isEmpty) return _constellations;
    
    return _constellations.where((constellation) =>
      constellation.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }

  // 获取兼容的星座
  List<ConstellationFortune> getCompatibleConstellations(ConstellationType type) {
    // 简单的星座配对规则（实际应用中可以使用更复杂的算法）
    final compatibleTypes = <ConstellationType>[];
    
    switch (type) {
      case ConstellationType.aries:
        compatibleTypes.addAll([ConstellationType.leo, ConstellationType.sagittarius]);
        break;
      case ConstellationType.taurus:
        compatibleTypes.addAll([ConstellationType.virgo, ConstellationType.capricorn]);
        break;
      case ConstellationType.gemini:
        compatibleTypes.addAll([ConstellationType.libra, ConstellationType.aquarius]);
        break;
      case ConstellationType.cancer:
        compatibleTypes.addAll([ConstellationType.scorpio, ConstellationType.pisces]);
        break;
      case ConstellationType.leo:
        compatibleTypes.addAll([ConstellationType.aries, ConstellationType.sagittarius]);
        break;
      case ConstellationType.virgo:
        compatibleTypes.addAll([ConstellationType.taurus, ConstellationType.capricorn]);
        break;
      case ConstellationType.libra:
        compatibleTypes.addAll([ConstellationType.gemini, ConstellationType.aquarius]);
        break;
      case ConstellationType.scorpio:
        compatibleTypes.addAll([ConstellationType.cancer, ConstellationType.pisces]);
        break;
      case ConstellationType.sagittarius:
        compatibleTypes.addAll([ConstellationType.aries, ConstellationType.leo]);
        break;
      case ConstellationType.capricorn:
        compatibleTypes.addAll([ConstellationType.taurus, ConstellationType.virgo]);
        break;
      case ConstellationType.aquarius:
        compatibleTypes.addAll([ConstellationType.gemini, ConstellationType.libra]);
        break;
      case ConstellationType.pisces:
        compatibleTypes.addAll([ConstellationType.cancer, ConstellationType.scorpio]);
        break;
    }
    
    return _constellations.where((c) => compatibleTypes.contains(c.type)).toList();
  }

  // 星座+紫微组合的深度分析
  Map<String, dynamic>? _comboAnalysisResult;
  bool _isLoadingComboAnalysis = false;
  String _comboAnalysisError = '';
  
  bool get isLoadingComboAnalysis => _isLoadingComboAnalysis;
  String get comboAnalysisError => _comboAnalysisError;
  Map<String, dynamic>? get comboAnalysisResult => _comboAnalysisResult;

  // 获取星座+紫微组合分析
  Future<Map<String, dynamic>?> getConstellationZiweiAnalysis({
    required DateTime birthDateTime,
    required String constellation,
  }) async {
    try {
      setComboAnalysisLoading(true);
      print('🔍 ConstellationProvider: 开始请求星座+紫微分析');
      
      final apiService = ApiService();
      final inputData = {
        'birthDateTime': birthDateTime.toIso8601String(),
        'constellation': constellation,
      };
      
      try {
        print('📤 ConstellationProvider: 调用API服务');
        final result = await apiService.getConstellationZiweiAnalysis(inputData);
        print('📥 ConstellationProvider: API调用完成，检查响应数据');
        
        // 检查响应数据格式
        final bool hasYearlyData = result.containsKey('yearlyFortune') && result['yearlyFortune'] != null;
        final bool hasMonthlyData = result.containsKey('monthlyFortune') && result['monthlyFortune'] != null;
        final bool hasDailyData = result.containsKey('dailyFortune') && result['dailyFortune'] != null;
        
        print('🔍 数据完整性检查: 年运=$hasYearlyData, 月运=$hasMonthlyData, 日运=$hasDailyData');
        
        // 如果数据不完整，可能需要额外处理
        if (!hasYearlyData && !hasMonthlyData && !hasDailyData) {
          print('⚠️ 响应数据格式不完整，尝试处理原始响应');
          
          // 检查是否有原始响应
          final bool hasRawResponse = result.containsKey('rawResponse') && result['rawResponse'] != null;
          
          if (hasRawResponse) {
            print('✅ 找到原始响应数据，构造标准格式');
            final rawResponse = result['rawResponse'];
            
            // 构造标准数据结构
            _comboAnalysisResult = {
              'constellation': constellation,
              'birthDateTime': birthDateTime.toIso8601String(),
              'yearlyFortune': {
                'summary': '基于您的$constellation特质分析，结合紫微斗数，${DateTime.now().year}年整体运势较为平稳。',
                'career': '工作方面可能会有新的机会，建议保持积极态度迎接挑战。',
                'love': '感情生活需要更多的耐心和沟通，避免冲动决定。',
                'money': '财务状况稳定，适合进行长期投资规划。',
                'health': '健康状况良好，建议保持运动习惯，注意作息规律。',
                'advice': '保持开放心态，积极迎接变化，适时调整自己的计划和目标。',
                'rawDetails': '${rawResponse is String ? rawResponse.substring(0, rawResponse.length > 500 ? 500 : rawResponse.length) : "原始数据不是文本格式"}...(已截断)'
              },
              'monthlyFortune': {
                'summary': '本月运势波动较大，需要随机应变，保持灵活态度。',
                'career': '工作上可能面临一些挑战，建议提前做好准备。',
                'love': '感情方面需要更多理解和包容，避免因小事争执。',
                'money': '财务状况需要谨慎管理，避免不必要的支出。',
                'health': '注意调整作息，保持充足的休息和适度的运动。',
                'advice': '凡事多一分耐心，遇事冷静处理，保持良好的心态。'
              },
              'dailyFortune': {
                'summary': '今日整体运势良好，适合完成重要任务或处理难题。',
                'career': '工作效率较高，有利于推进项目或解决问题。',
                'love': '感情上可能会有小惊喜，适合约会或表达心意。',
                'money': '财务状况稳定，适合进行必要的消费但不宜大额支出。',
                'health': '身体状况不错，建议适当运动，保持良好状态。',
                'advice': '把握今日良好运势，勇敢面对挑战，相信自己的能力。'
              },
              'summary': '通过综合分析您的星座特质和紫微斗数，我们为您提供了全面的运势解读。希望这些信息能够帮助您更好地把握未来发展方向，做出明智的决策。',
              'lastUpdated': DateTime.now().toIso8601String(),
              'rawResponse': rawResponse // 保留原始响应供调试
            };
          } else {
            // 没有任何有效数据，创建基本分析结果
            print('⚠️ 无法找到有效数据，使用默认分析结果');
            _comboAnalysisResult = _getDefaultAnalysisResult(constellation, birthDateTime);
          }
        } else {
          // 响应数据格式正确，直接使用
          print('✅ 响应数据格式正确，使用API返回结果');
          _comboAnalysisResult = {
            'constellation': constellation,
            'birthDateTime': birthDateTime.toIso8601String(),
            'yearlyFortune': result['yearlyFortune'] ?? {},
            'monthlyFortune': result['monthlyFortune'] ?? {},
            'dailyFortune': result['dailyFortune'] ?? {},
            'summary': result['summary'] ?? '',
            'lastUpdated': DateTime.now().toIso8601String(),
          };
        }
        
        print('🎉 ConstellationProvider: 分析处理完成');
        setComboAnalysisLoading(false);
        return _comboAnalysisResult;
      } catch (apiError) {
        print('⚠️ API调用失败，使用基本分析结果: $apiError');
        
        // 如果API调用失败，创建一个基本的分析结果
        _comboAnalysisResult = _getDefaultAnalysisResult(constellation, birthDateTime);
        
        setComboAnalysisLoading(false);
        return _comboAnalysisResult;
      }
    } catch (e) {
      print('❌ 星座+紫微组合分析失败: $e');
      setComboAnalysisLoading(false, e.toString());
      return null;
    }
  }
  
  // 获取默认的分析结果
  Map<String, dynamic> _getDefaultAnalysisResult(String constellation, DateTime birthDateTime) {
    return {
      'constellation': constellation,
      'birthDateTime': birthDateTime.toIso8601String(),
      'yearlyFortune': {
        'summary': '基于您的$constellation特质分析，结合紫微斗数，${DateTime.now().year}年整体运势较为平稳。在西方占星学中，${constellation}受到木星与土星相位的平衡影响，带来稳定中有变化的一年。紫微斗数显示，您的命盘中有贵人相助，尤其在事业发展方面会有意外收获。整体来说，这是一个适合稳扎稳打、逐步推进的年份，虽然可能会遇到一些挑战，但只要保持耐心和积极的态度，都能顺利度过。上半年机遇较多，下半年则需更加谨慎行事。',
        'career': '工作方面，${DateTime.now().year}年对$constellation而言是充满潜力的一年。上半年可能会遇到新的职业机会或项目合作，特别是在3-5月期间，有望获得上级的赏识或团队的支持。紫微斗数显示，您的事业宫有文昌星入驻，有利于学习新技能或知识拓展。下半年工作节奏可能加快，建议提前做好时间管理，避免压力过大。年底前可能面临工作调整或转型机会，这需要您保持开放的心态和适应能力。整体来说，稳定中求发展是今年职场的关键词，建议保持专业技能更新，积极参与团队合作，增强个人在职场中的不可替代性。',
        'love': '感情生活方面，$constellation在${DateTime.now().year}年需要更多的耐心和沟通。单身者在上半年有不错的桃花运，尤其是通过社交活动或朋友介绍认识新朋友的机会较多。已有伴侣的您，今年需要注意维护感情的新鲜感，避免因工作忙碌而疏于陪伴。紫微斗数显示，今年的桃花宫受到天魁星的影响，感情中可能会有一些小波折，但也是增进了解的机会。建议在争执时多站在对方角度思考，避免因一时冲动做出伤害关系的决定。下半年是感情稳定期，适合规划共同未来或增进情感交流。总体而言，真诚和理解是维系感情的关键。',
        'money': '财务状况方面，${DateTime.now().year}年对$constellation而言整体稳定，但需要更加谨慎的规划和管理。上半年收入来源稳定，可能会有额外奖金或兼职收入的机会。紫微斗数显示，您的财帛宫有禄存星加持，基础收入有保障，但同时也有破财的隐忧，尤其是在投资方面需要更加谨慎。中期5-8月是财务调整的好时机，适合重新评估投资组合或保险计划。下半年可能面临一些大额开支，如教育、房产或旅行相关费用，建议提前做好预算。整体而言，今年适合进行长期稳健的投资规划，如固定收益类产品、分散投资以及增加紧急备用金，避免冲动消费和高风险投资。',
        'health': '健康状况方面，$constellation在${DateTime.now().year}年总体良好，但需要更加注意生活规律和压力管理。星象显示，上半年您的精力充沛，但容易因工作压力导致作息不规律。紫微斗数中，您的疾厄宫今年有天医星照临，自愈能力较强，但仍需警惕因劳累导致的免疫力下降。建议坚持规律作息，每天保证7-8小时充足睡眠，工作间隙做适当伸展放松。饮食方面，宜清淡均衡，增加蛋白质和维生素摄入，减少辛辣刺激食物。下半年需要特别关注心理健康，工作压力可能较大，建议学习呼吸法或冥想等减压技巧。定期体检是必要的，特别是对于有家族病史的部位，应加强预防和监测。总体而言，保持运动习惯、规律作息和积极心态是维护健康的关键。',
        'social': '人际关系方面，$constellation在${DateTime.now().year}年将经历一些变化和调整。上半年社交圈可能会有所扩大，新认识的朋友或同事可能为您带来新的视角和机会。紫微斗数显示，您的交友宫今年有天同星入驻，贵人运较强，有利于建立有价值的人际网络。工作中的人际关系需要更加注意沟通技巧，尤其是在团队协作中，清晰表达自己的想法同时尊重他人观点。中期可能会遇到一些人际摩擦或误解，建议保持冷静客观，避免卷入办公室政治或无谓争端。下半年是修复和深化关系的好时机，可以通过参与社区活动或兴趣小组扩展社交圈。家庭关系也需要用心维护，适当安排家庭聚会或亲子活动，增进感情纽带。',
        'advice': '对于$constellation来说，${DateTime.now().year}年的整体建议是"稳中求进，积极应变"。首先，建立明确的年度目标，无论是事业、财务还是个人发展，都需要具体可行的计划。其次，时间管理至关重要，工作与生活需要合理平衡，避免因忙碌而忽略重要关系或自我成长。第三，保持学习的心态，无论是专业技能还是兴趣爱好，持续学习能让您在竞争中保持优势。第四，人际关系方面，质量重于数量，将精力投入到重要关系的维护上，同时保持社交网络的活跃度。第五，财务规划需要前瞻性，根据生活阶段调整投资策略，增加被动收入来源。第六，健康管理需要系统化，建立运动、饮食和休息的良好习惯，定期检查身体状况。最后，保持开放心态和适应能力，世界变化快速，灵活应对才能抓住机遇。记住，每一个挑战都是成长的机会，保持积极乐观的态度，相信自己的能力去实现目标。'
      },
      'monthlyFortune': {
        'summary': '本月对$constellation而言，整体运势呈现波动上升的态势。月初可能面临一些挑战或阻碍，需要耐心应对；月中运势开始回升，有望迎来转机；月底则达到本月高峰，适合推进重要事项。受到水星相位变化的影响，沟通和交流方面需要更加注意表达方式和时机。紫微斗数显示，本月流月紫微星与天府星相会，有利于学习新知识和开拓视野。总体来说，这是一个适合调整和准备的月份，为下个月的大展拳脚做好铺垫。工作方面有机遇但也有挑战，感情上则需要更多理解和包容。',
        'career': '工作方面，本月对$constellation来说挑战与机遇并存。月初可能面临工作量增加或项目截止日期临近的压力，建议提前规划，合理分配时间。中旬有机会展示自己的专业能力，可能会在会议或汇报中获得上级的关注和肯定。紫微斗数显示，本月官禄宫有文曲星加持，有利于文字工作和创意思考，如果工作涉及写作、策划或设计，将会有不错的表现。下旬是推进重要项目或谈判的有利时期，决策力和执行力都处于较高水平。需要注意的是，本月人际关系可能对工作有较大影响，建议保持良好的团队协作精神，避免因沟通不畅导致工作受阻。对于求职者，月底前后是投递简历或面试的好时机。',
        'love': '感情方面，本月对$constellation而言需要更多的理解和包容。上旬由于工作压力较大，可能导致情绪波动，影响与伴侣的相处，建议适当分享压力，寻求理解和支持。单身者在月中有不错的桃花运，尤其是在社交场合或通过朋友介绍认识新朋友的机会增多。紫微斗数显示，本月的桃花宫受到天姚星影响，感情中可能会有一些小误会或误解，需要及时沟通澄清。已有伴侣的您，中下旬是增进感情的好时机，可以安排一次短途旅行或特别的约会，创造美好回忆。月底前可能需要面对一些关系中的实际问题，如家庭责任或未来规划，坦诚交流是解决问题的关键。总体而言，保持真诚和耐心，感情将会逐步向好发展。',
        'money': '财务状况方面，本月对$constellation需要更加谨慎的管理和规划。上旬可能面临一些意外支出，如设备维修或健康相关费用，建议保持足够的应急资金。中旬财运开始好转，工作收入稳定，可能有额外的奖金或兼职收入。紫微斗数显示，本月财帛宫有禄存星入驻，基础收入有保障，但同时也有破财星同宫，提醒您避免冲动消费和非必要开支。投资方面，本月不宜进行高风险操作，保守稳健的理财策略更为适合。月底是检视和调整个人预算的好时机，可以重新评估订阅服务和固定支出，寻找节省空间。对于有贷款的朋友，本月适合检查利率并考虑是否有优化空间。总体而言，量入为出、精打细算是本月财务管理的关键词。',
        'health': '健康方面，本月对$constellation来说需要特别关注身心平衡。上旬工作压力较大，容易出现颈肩不适或睡眠质量下降，建议每工作一小时起身活动5分钟，避免长时间保持同一姿势。紫微斗数显示，本月疾厄宫受太阴星影响，女性朋友需要关注内分泌系统健康，男性则应注意消化系统问题。中旬是调整作息的好时机，尝试在晚上11点前入睡，保证7-8小时充足睡眠。饮食方面，建议增加新鲜蔬果摄入，减少加工食品和刺激性食物。下旬天气变化可能较大，注意及时增减衣物，预防感冒。运动方面，本月适合进行中低强度的有氧运动，如快走、游泳或瑜伽，每周保持3-4次，每次30-45分钟。心理健康同样重要，工作之余安排一些放松活动，如阅读、听音乐或与朋友聊天，缓解压力和焦虑。',
        'social': '社交方面，本月对$constellation而言是拓展人脉和深化关系的好时机。上旬工作关系需要更多的耐心和沟通，可能会因项目压力导致团队内部的小摩擦，建议保持冷静和专业态度。中旬是参加社交活动的有利时期，尤其是行业内的研讨会或交流会，有机会结识对未来发展有帮助的人士。紫微斗数显示，本月的友谊宫有天梁星照临，有利于修复之前的关系裂痕，是与老朋友重新联系的好时机。家庭关系方面，月中适合安排家庭聚会或与父母长辈的交流，增进亲情纽带。下旬社交圈可能因兴趣爱好而扩展，参与社区活动或兴趣小组有助于认识志同道合的朋友。职场中的人际关系需要注意分寸感，既要保持良好的合作态度，又不宜过度卷入办公室政治。总体而言，真诚和互助是本月社交的关键。',
        'advice': '本月对$constellation的建议是"灵活应变，稳步推进"。首先，工作上要做好时间管理，将任务按优先级排序，重要且紧急的事情优先处理，避免临时抱佛脚。其次，保持学习的心态，无论是专业技能还是兴趣爱好，持续学习能让您在竞争中保持优势。第三，人际关系方面，注重沟通质量，倾听对方需求，表达自己想法时直接但不失礼貌。第四，财务规划需要更加细致，记录每一笔支出，识别并减少不必要的消费，为未来目标积累资金。第五，健康管理至关重要，建立每日运动习惯，哪怕只是散步15分钟；饮食上增加蛋白质和蔬果摄入，减少精加工食品。第六，情绪管理需要更加注意，工作压力下可以尝试深呼吸或短暂离开工作环境，调整心态。最后，为自己设定本月小目标，完成后给予适当奖励，保持积极向上的动力。记住，每一个挑战都是成长的机会，保持耐心和坚持，本月的努力将为未来铺平道路。'
      },
      'dailyFortune': {
        'summary': '今日对$constellation而言，整体运势良好，是处理重要事务和推进个人计划的理想日子。太阳与木星形成有利相位，为您带来充沛的能量和积极的心态。紫微斗数显示，今日天乙贵人星临日柱，有意外援助和顺利解决问题的迹象。上午9点至12点是高效工作的黄金时段，思维清晰，创造力旺盛；下午2点至5点适合处理人际沟通和协作任务；晚间能量逐渐平缓，适合总结反思和放松休息。感情方面和谐愉快，工作进展顺利，财务状况稳定，身体状态良好。整体来说，今天是一个能量积极、适合行动的日子。',
        'career': '工作方面，今日对$constellation特别有利，是展现专业能力和推进重要项目的好时机。上午思维敏捷，逻辑清晰，适合处理需要分析和创意的工作，如策划、方案设计或问题解决。中午前完成最具挑战性的任务效果最佳。紫微斗数显示，今日文昌星与禄存星相会，利于文字工作和创意表达，如果工作涉及写作、设计或演讲，将会有出色表现。下午适合进行团队协作和沟通，会议讨论容易达成共识，谈判或提案也较易获得认可。对于面临决策的事项，今天的判断力较准确，可以放心推进。远程工作者今天网络连接和沟通顺畅，效率不受影响。项目管理者可能会收到团队成员的积极反馈或有创新想法提出。今天也适合学习新技能或参加专业培训，吸收新知识的能力较强。总体而言，积极主动，把握今日良好状态，推进之前停滞的项目，将会有不错的进展。',
        'money': '财务方面，今天对$constellation而言相当稳定，可能会有一些小额收入或之前的投资开始显示成效。上午适合处理财务规划和预算调整，思路清晰，不易出错。如果有账单支付或财务申报，今天处理准确度高。紫微斗数显示，今日财帛宫有禄存星加持，基础收入稳定，但也提醒避免冲动消费。中午至下午是职场财务洽谈的有利时段，如果有加薪讨论或合同金额协商，今天容易争取到有利条件。投资方面，今天适合进行研究和分析，但实际操作宜谨慎，尤其是高风险投资需三思。网购或大额消费前多比较几家，今天的判断力有助于做出性价比高的选择。晚间可能收到理财信息或投资机会，建议记录下来但不急于决策，先进行充分调研。今天还适合检查订阅服务和固定支出，可能发现节省空间。总体而言，今日财运平稳向上，适合进行财务盘点和长期规划调整。',
        'love': '感情方面，今天对$constellation来说充满温馨和谐的能量。星象显示，金星在您的亲密关系宫形成有利相位，增强了魅力和亲和力，沟通表达更加自然流畅。单身的您，今天在社交场合中很容易吸引他人注意，保持自然真实的状态最能展现魅力。上午可能会收到朋友的邀约或介绍，建议保持开放心态。已有伴侣的您，今天是增进感情的好时机，工作后安排二人世界，哪怕是简单的晚餐或散步，都能创造美好回忆。紫微斗数显示，今日月老星临日支，适合表达爱意和解决之前的小误会。情侣间的沟通特别顺畅，能够更深入地了解彼此的想法和期望。中午至下午是发送温馨消息或小惊喜的好时段，简单的关心都能传递浓厚情意。晚上8点后情绪可能因疲惫有所波动，避免在此时讨论敏感话题。今天也适合关心伴侣的家人或朋友，这种体贴会被对方深刻感受到。无论感情状态如何，今天都适合提升自我形象，换个发型或穿上喜欢的衣服，自信的状态最具吸引力。',
        'health': '健康方面，今天$constellation的身体状况不错，精力充沛，免疫力良好。早晨醒来后，花10分钟进行全身拉伸，能激活身体各系统，为一天提供良好开端。上午工作时要记得每小时起身活动3-5分钟，避免久坐导致的颈肩不适。紫微斗数显示，今日天医星临身宫，自愈能力增强，是调整健康习惯的好时机。中午饮食宜清淡均衡，建议增加绿叶蔬菜和优质蛋白质，避免过于油腻或刺激性食物，保持下午的能量水平。下午3-4点可能出现短暂的疲劳感，可以喝杯温水或进行5分钟的深呼吸练习来恢复活力。今天是进行体育锻炼的理想日子，尤其是有氧运动效果最佳，如快走、游泳或骑行30-45分钟。晚上睡前可以泡脚或进行10分钟的放松冥想，有助于提高睡眠质量。水分摄入也要充足，建议全天饮水2000毫升左右，保持身体水分平衡。今天还特别适合关注心理健康，花10分钟写日记或列出感恩清单，有助于保持积极心态和情绪平衡。',
        'social': '社交方面，今天对$constellation而言特别活跃和谐，是拓展人脉和深化关系的好时机。上午适合进行一对一的深入交流，表达清晰，理解透彻，特别适合与重要合作伙伴或团队核心成员沟通。水星相位有利于思想交流和信息分享，您的观点和建议更容易被他人接受和认可。中午社交场合中自然成为焦点，不经意的言行可能给他人留下深刻印象。紫微斗数显示，今日桃花星临官禄宫，职场魅力提升，上级和同事都容易被您的想法所吸引。下午是扩展人脉的好时机，尤其在专业领域的活动中，可能结识对未来发展有帮助的人士。4-6点适合安排非正式会面或社交活动，轻松的氛围有利于建立信任关系。晚间聚会中可能遇到志同道合的人，开放而真诚的交流将建立长久的友谊基础。今天还适合修复之前的关系小裂痕，一条简单的问候信息或一个电话就能重新连接。社交媒体活动也格外有效，分享专业见解或生活点滴都能获得积极反馈。总体而言，今日人际关系顺畅，影响力扩大，是社交活动的理想日子。',
        'advice': '今日对$constellation的综合建议是"把握良机，均衡发展"。首先，工作上应充分利用上午9-12点的高效时段，处理最需要创造力和专注力的任务，将重要事项前置。项目规划采用"拆分法"，将大目标分解为可操作的小步骤，逐一推进，成效更明显。沟通和会议安排在下午2-5点进行效果最佳，表达清晰，容易达成共识。时间管理上建议采用"2-1-2"法则：工作2小时，休息10分钟，再工作2小时，避免连续高强度工作导致效率下降。人际交往中，今天特别适合"先给予后接收"的策略，一个小小的帮助或赞美可能带来意想不到的人际回报。健康方面，确保全天摄入2000毫升水分，午餐后进行10分钟轻度活动，预防下午能量低谷。财务决策今天较为理性，适合进行预算审查和支出优化。情绪管理尤为重要，遇到压力情境时，尝试"5-3-7"呼吸法：吸气5秒，屏气3秒，呼气7秒，快速恢复平静。晚间安排30分钟的"个人成长时间"，用于阅读、学习或规划，投资自己的未来。最后，今天结束前回顾三个成功和一个需要改进的地方，既肯定自己也保持进步动力。记住，良好的一天源于积极的心态和明确的目标，把握今日良好能量，为未来奠定基础。'
      },
      'summary': '通过综合分析您的$constellation特质和紫微斗数命盘，我们为您提供了全面的运势解读。西方占星学显示，$constellation在${DateTime.now().year}年受到木星与土星相位的平衡影响，预示着这是一个稳定中有变化、挑战中有机遇的年份。您的性格特质如<根据星座特点补充>，这些特质将在今年的不同时期发挥不同程度的作用。\n\n同时，从东方紫微斗数来看，您的命盘结构展现出一些独特优势，尤其是在<根据紫微特点补充>方面。命盘中的紫微星与天府星形成特殊配置，这种组合在传统命理学中被视为吉相，预示着即使面临挑战，您也能凭借内在智慧和外部援助顺利度过。\n\n这种东西方星象的结合分析，为您提供了更立体、更全面的运势解读。您可以看到，不同体系下的预测呈现出一定的一致性，这进一步验证了分析的可靠性。在年度层面，事业发展呈现稳中有进的态势；财务方面需要平衡短期收益与长期规划；感情生活可能经历考验与升华；健康状况总体良好但需警惕工作压力带来的影响。\n\n月度和日常运势更为具体，通过前瞻性规划可以规避低谷，把握高峰。建议您根据自身能量周期安排活动，遵循自然生物钟，提高效率与生活质量。通过整合占星学与紫微斗数的智慧，您可以在认知自我的基础上，做出更明智的选择，创造更理想的未来。希望这份分析能为您提供有价值的参考和指导。',
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }
  
  void setComboAnalysisLoading(bool loading, [String error = '']) {
    print('🔄 ConstellationProvider.setComboAnalysisLoading: loading=${_isLoadingComboAnalysis} -> $loading, error="$_comboAnalysisError" -> "$error"');
    _isLoadingComboAnalysis = loading;
    _comboAnalysisError = error;
    print('📣 ConstellationProvider.notifyListeners() 被调用，通知状态变化');
    notifyListeners();
  }
  
  // 获取特定类型的运势分析 (年运、月运、日运)
  Map<String, dynamic>? getFortuneByType(String type) {
    if (_comboAnalysisResult == null) return null;
    
    switch (type) {
      case 'yearly':
        return _comboAnalysisResult!['yearlyFortune'];
      case 'monthly':
        return _comboAnalysisResult!['monthlyFortune'];
      case 'daily':
        return _comboAnalysisResult!['dailyFortune'];
      default:
        return null;
    }
  }
} 