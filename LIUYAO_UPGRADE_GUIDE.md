# 六爻预测功能升级指南

## 功能改进概述

本次升级完善了六爻预测功能，主要包含以下三个方面的改进：

### 1. 摇卦前问题检测 ✅
- **功能描述**: 在用户点击摇卦前，检测是否输入了问题
- **实现位置**: `lib/screens/liuyao_screen.dart` 中的 `_shakeCoins()` 方法
- **用户体验**: 如果未输入问题，会弹出友好的提示对话框，引导用户输入具体问题

### 2. 火山方舟AI卦象解析 ✅
- **功能描述**: 摇卦完成后，获取用户时间信息，结合摇卦结果向火山方舟AI发起卦象解析请求
- **实现位置**: 
  - 前端: `lib/screens/liuyao_screen.dart` 中的 `_generateGuaResult()` 方法
  - 后端: `backend/services/PredictionService.js` 中的 `getAILiuyaoAnalysis()` 方法
- **API集成**: 调用火山方舟API进行智能卦象分析
- **降级处理**: 当AI服务不可用时，自动回退到传统卦象解析

### 3. 保存结果和AI咨询功能 ✅
- **保存结果接口**: `POST /api/v1/predictions/liuyao/:recordId/save`
- **AI咨询接口**: `POST /api/v1/predictions/liuyao/:recordId/ai-consult`
- **前端UI**: 在卦象解析下方新增AI深度咨询区域
- **功能特点**: 基于已生成的卦象进行进一步的AI咨询

## 环境配置

### 火山方舟API配置
在后端环境变量中添加以下配置：

```bash
# 火山方舟API配置
ARK_API_KEY=your_ark_api_key_here
```

**注意**: 
1. 请将 `your_ark_api_key_here` 替换为实际的火山方舟API密钥
2. 在 `backend/services/PredictionService.js` 中，将模型ID `ep-20241201080456-k25qm` 替换为实际的模型ID

### 依赖安装
后端新增了 `axios` 依赖用于API调用：
```bash
cd backend
npm install axios
```

## 技术实现细节

### 前端改进
1. **状态管理扩展**: 新增咨询相关状态变量
2. **UI组件**: 新增 `_buildAIConsultSection()` 和 `_buildConsultResult()` 方法
3. **API调用**: 在 `lib/services/api_service.dart` 中新增相关接口方法

### 后端改进
1. **AI集成**: 新增火山方舟API调用逻辑
2. **数据验证**: 扩展验证规则支持新的输入字段
3. **错误处理**: 完善的降级和异常处理机制

### 数据流程
```
用户输入问题 -> 摇卦6次 -> 获取时间信息 -> 调用火山方舟API -> 
显示卦象解析 -> (可选)AI深度咨询 -> 保存结果
```

## 使用说明

### 用户操作流程
1. 打开六爻预测页面
2. 在问题输入框中输入具体问题
3. 点击"点击摇卦"按钮进行6次摇卦
4. 查看AI生成的卦象解析结果
5. (可选)在AI咨询区域输入进一步的问题
6. 获得基于卦象的深度AI咨询回答

### 开发者注意事项
1. 确保火山方舟API密钥正确配置
2. 监控API调用的成功率和响应时间
3. 定期检查降级逻辑是否正常工作
4. 考虑添加API调用的费用监控

## 测试建议

### 功能测试
- [ ] 测试未输入问题时的提示功能
- [ ] 测试完整的摇卦流程
- [ ] 测试AI卦象解析的响应
- [ ] 测试AI咨询功能
- [ ] 测试网络异常时的降级逻辑

### 性能测试
- [ ] 测试火山方舟API的响应时间
- [ ] 测试高并发下的系统稳定性
- [ ] 监控API调用的成功率

## 后续优化方向

1. **个性化推荐**: 基于用户历史记录提供个性化的卦象解释
2. **多模型支持**: 集成多个AI模型，提供不同风格的解析
3. **社区功能**: 允许用户分享和讨论卦象解析结果
4. **数据分析**: 分析用户问题类型和卦象分布，提供洞察报告

---

**更新时间**: 2024年12月1日  
**版本**: v2.0.0  
**状态**: 已完成 