class DailyFortune {
  final String date;
  final String lunarDate;
  final String content;
  final String timestamp;
  final bool isAIGenerated;
  final bool? cached;
  final DateTime? lastUpdated;
  final String? error;

  DailyFortune({
    required this.date,
    required this.lunarDate,
    required this.content,
    required this.timestamp,
    required this.isAIGenerated,
    this.cached,
    this.lastUpdated,
    this.error,
  });

  factory DailyFortune.fromJson(Map<String, dynamic> json) {
    return DailyFortune(
      date: json['date'] ?? '',
      lunarDate: json['lunarDate'] ?? '',
      content: json['content'] ?? '',
      timestamp: json['timestamp'] ?? '',
      isAIGenerated: json['isAIGenerated'] ?? false,
      cached: json['cached'],
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated']) 
          : null,
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'lunarDate': lunarDate,
      'content': content,
      'timestamp': timestamp,
      'isAIGenerated': isAIGenerated,
      'cached': cached,
      'lastUpdated': lastUpdated?.toIso8601String(),
      'error': error,
    };
  }

  // 获取格式化的日期
  String getFormattedDate() {
    try {
      final DateTime parsedDate = DateTime.parse(timestamp);
      return '${parsedDate.year}年${parsedDate.month}月${parsedDate.day}日';
    } catch (e) {
      return date;
    }
  }

  // 获取星期几
  String getWeekday() {
    try {
      final DateTime parsedDate = DateTime.parse(timestamp);
      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      return weekdays[parsedDate.weekday - 1];
    } catch (e) {
      return '';
    }
  }

  // 判断是否是今天的运势
  bool isToday() {
    try {
      final DateTime parsedDate = DateTime.parse(timestamp);
      final DateTime today = DateTime.now();
      return parsedDate.year == today.year &&
             parsedDate.month == today.month &&
             parsedDate.day == today.day;
    } catch (e) {
      return false;
    }
  }

  // 解析运势内容中的宜忌信息
  Map<String, List<String>> parseFortuneDetails() {
    print('\n========== 开始解析运势内容 ==========');
    print('原始内容长度: ${content.length}');
    print('原始内容:\n$content');
    print('=====================================\n');

    Map<String, List<String>> result = {
      'suitable': [],
      'avoid': [],
      'luckyColors': [],
      'luckyNumbers': [],
    };

    try {
      // 尝试多种格式解析
      _parseStandardFormat(result);
      
      // 如果标准格式解析失败，尝试备用格式
      if (result['suitable']!.isEmpty || result['avoid']!.isEmpty) {
        print('⚠️ 标准格式解析失败，尝试备用格式解析...');
        _parseAlternativeFormat(result);
      }
      
      // 如果备用格式也解析失败，尝试更宽松的格式
      if (result['suitable']!.isEmpty || result['avoid']!.isEmpty) {
        print('⚠️ 备用格式解析失败，尝试宽松格式解析...');
        _parseLooseFormat(result);
      }
      
      // 如果所有解析方法都失败，提供默认值
      if (result['suitable']!.isEmpty) {
        print('⚠️ 所有解析方法失败，使用默认值...');
        result['suitable'] = ['祈福求财', '学习充电', '整理环境', '与朋友聚会'];
      }
      if (result['avoid']!.isEmpty) {
        result['avoid'] = ['冲动投资', '争执口角', '熬夜劳累', '签订重要合同'];
      }
      if (result['luckyColors']!.isEmpty) {
        result['luckyColors'] = ['蓝色', '白色'];
      }
      if (result['luckyNumbers']!.isEmpty) {
        result['luckyNumbers'] = ['3', '7', '9'];
      }
      
    } catch (e) {
      print('❌ 解析运势内容异常: $e');
      print('运势内容: $content');
      
      // 解析失败时使用默认值
      result['suitable'] = ['祈福求财', '学习充电', '整理环境', '与朋友聚会'];
      result['avoid'] = ['冲动投资', '争执口角', '熬夜劳累', '签订重要合同'];
      result['luckyColors'] = ['蓝色', '白色'];
      result['luckyNumbers'] = ['3', '7', '9'];
    }

    print('\n========== 解析完成 ==========');
    print('最终结果:');
    print('今日宜: ${result['suitable']}');
    print('今日忌: ${result['avoid']}');
    print('幸运色彩: ${result['luckyColors']}');
    print('幸运数字: ${result['luckyNumbers']}');
    print('===============================\n');

    return result;
  }
  
  // 解析标准格式
  void _parseStandardFormat(Map<String, List<String>> result) {
    try {
      // 解析今日宜 - 尝试多种格式
      print('🔍 开始解析今日宜...');
      final suitableRegexList = [
        RegExp(r'【今日宜】\s*\n?((?:\d+\. .+(?:\n|$))+)', multiLine: true),
        RegExp(r'\*\*今日宜：?\*\*\s*\n?((?:• .+(?:\n|$))+)', multiLine: true),
        RegExp(r'今日宜：\s*\n?((?:• .+(?:\n|$))+)', multiLine: true),
        RegExp(r'今日宜[：:]\s*([^\n]+)', multiLine: true),
      ];
      
      bool suitableFound = false;
      for (var regex in suitableRegexList) {
        print('尝试正则表达式: ${regex.pattern}');
        final match = regex.firstMatch(content);
        if (match != null) {
          print('匹配成功: ${match.group(0)}');
          final matchedText = match.group(1) ?? '';
          
          // 处理不同格式
          if (matchedText.contains('•')) {
            // 处理项目符号格式
            final items = matchedText.split('\n')
                .where((line) => line.trim().contains('•'))
                .map((line) => line.trim().replaceFirst(RegExp(r'^• '), '').trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['suitable'] = items;
          } else if (matchedText.contains('1.') || matchedText.contains('1、')) {
            // 处理编号格式
            final items = matchedText.split('\n')
                .where((line) => line.trim().contains(RegExp(r'^\d+[\.\、]')))
                .map((line) => line.trim().replaceFirst(RegExp(r'^\d+[\.\、]\s*'), '').trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['suitable'] = items;
          } else {
            // 处理逗号分隔格式
            final items = matchedText.split(RegExp(r'[,，、]'))
                .map((item) => item.trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['suitable'] = items;
          }
          
          suitableFound = true;
          print('解析到的今日宜: ${result['suitable']}');
          break;
        }
      }
      
      if (!suitableFound) {
        print('所有正则表达式匹配失败');
      }

      // 解析今日忌 - 尝试多种格式
      print('\n🔍 开始解析今日忌...');
      final avoidRegexList = [
        RegExp(r'【今日忌】\s*\n?((?:\d+\. .+(?:\n|$))+)', multiLine: true),
        RegExp(r'\*\*今日忌：?\*\*\s*\n?((?:• .+(?:\n|$))+)', multiLine: true),
        RegExp(r'今日忌：\s*\n?((?:• .+(?:\n|$))+)', multiLine: true),
        RegExp(r'今日忌[：:]\s*([^\n]+)', multiLine: true),
      ];
      
      bool avoidFound = false;
      for (var regex in avoidRegexList) {
        print('尝试正则表达式: ${regex.pattern}');
        final match = regex.firstMatch(content);
        if (match != null) {
          print('匹配成功: ${match.group(0)}');
          final matchedText = match.group(1) ?? '';
          
          // 处理不同格式
          if (matchedText.contains('•')) {
            // 处理项目符号格式
            final items = matchedText.split('\n')
                .where((line) => line.trim().contains('•'))
                .map((line) => line.trim().replaceFirst(RegExp(r'^• '), '').trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['avoid'] = items;
          } else if (matchedText.contains('1.') || matchedText.contains('1、')) {
            // 处理编号格式
            final items = matchedText.split('\n')
                .where((line) => line.trim().contains(RegExp(r'^\d+[\.\、]')))
                .map((line) => line.trim().replaceFirst(RegExp(r'^\d+[\.\、]\s*'), '').trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['avoid'] = items;
          } else {
            // 处理逗号分隔格式
            final items = matchedText.split(RegExp(r'[,，、]'))
                .map((item) => item.trim())
                .where((item) => item.isNotEmpty)
                .toList();
            result['avoid'] = items;
          }
          
          avoidFound = true;
          print('解析到的今日忌: ${result['avoid']}');
          break;
        }
      }
      
      if (!avoidFound) {
        print('所有正则表达式匹配失败');
      }

      // 解析幸运色彩 - 尝试多种格式
      print('\n🔍 开始解析幸运色彩...');
      final colorRegexList = [
        RegExp(r'幸运色[：:]\s*([^\n（]+)', multiLine: true),
        RegExp(r'\*\*幸运色彩：?\*\*\s*([^\n]+)', multiLine: true),
        RegExp(r'幸运色彩[：:]\s*([^\n]+)', multiLine: true),
      ];
      
      bool colorFound = false;
      for (var regex in colorRegexList) {
        print('尝试正则表达式: ${regex.pattern}');
        final match = regex.firstMatch(content);
        if (match != null) {
          print('匹配成功: ${match.group(0)}');
          final matchedText = match.group(1) ?? '';
          
          final items = matchedText.split(RegExp(r'[,，、]'))
              .map((item) => item.trim())
              .where((item) => item.isNotEmpty)
              .toList();
          result['luckyColors'] = items;
          
          colorFound = true;
          print('解析到的幸运色彩: ${result['luckyColors']}');
          break;
        }
      }
      
      if (!colorFound) {
        print('所有正则表达式匹配失败');
      }

      // 解析幸运数字 - 尝试多种格式
      print('\n🔍 开始解析幸运数字...');
      final numberRegexList = [
        RegExp(r'幸运数[：:]\s*([^\n（]+)', multiLine: true),
        RegExp(r'\*\*幸运数字：?\*\*\s*([^\n]+)', multiLine: true),
        RegExp(r'幸运数字[：:]\s*([^\n]+)', multiLine: true),
      ];
      
      bool numberFound = false;
      for (var regex in numberRegexList) {
        print('尝试正则表达式: ${regex.pattern}');
        final match = regex.firstMatch(content);
        if (match != null) {
          print('匹配成功: ${match.group(0)}');
          final matchedText = match.group(1) ?? '';
          
          final items = matchedText.split(RegExp(r'[,，、]'))
              .map((item) => item.trim())
              .where((item) => item.isNotEmpty)
              .toList();
          result['luckyNumbers'] = items;
          
          numberFound = true;
          print('解析到的幸运数字: ${result['luckyNumbers']}');
          break;
        }
      }
      
      if (!numberFound) {
        print('所有正则表达式匹配失败');
      }
    } catch (e) {
      print('❌ 标准格式解析异常: $e');
    }
  }

  // 尝试解析备用格式
  void _parseAlternativeFormat(Map<String, List<String>> result) {
    try {
      // 查找包含"宜"的行
      final lines = content.split('\n');
      bool inSuitableSection = false;
      bool inAvoidSection = false;
      
      for (String line in lines) {
        final trimmedLine = line.trim();
        
        if (trimmedLine.contains('今日宜') || trimmedLine.contains('宜：')) {
          inSuitableSection = true;
          inAvoidSection = false;
          continue;
        }
        
        if (trimmedLine.contains('今日忌') || trimmedLine.contains('忌：')) {
          inAvoidSection = true;
          inSuitableSection = false;
          continue;
        }
        
        if (trimmedLine.contains('运势') || trimmedLine.contains('幸运') || trimmedLine.contains('财运') || trimmedLine.contains('温馨')) {
          inSuitableSection = false;
          inAvoidSection = false;
          continue;
        }
        
        if (inSuitableSection && trimmedLine.startsWith('•')) {
          result['suitable']!.add(trimmedLine.substring(1).trim());
        }
        
        if (inAvoidSection && trimmedLine.startsWith('•')) {
          result['avoid']!.add(trimmedLine.substring(1).trim());
        }
      }
      
      // 尝试解析幸运色彩和数字
      for (String line in lines) {
        final trimmedLine = line.trim();
        
        if (trimmedLine.contains('幸运色彩') || trimmedLine.contains('幸运色：')) {
          final colorMatch = RegExp(r'[：:]\s*(.+)$').firstMatch(trimmedLine);
          if (colorMatch != null) {
            final colorsText = colorMatch.group(1) ?? '';
            result['luckyColors'] = colorsText
                .split(RegExp(r'[、，,]'))
                .map((color) => color.trim())
                .where((color) => color.isNotEmpty)
                .toList();
          }
        }
        
        if (trimmedLine.contains('幸运数字') || trimmedLine.contains('幸运数：')) {
          final numberMatch = RegExp(r'[：:]\s*(.+)$').firstMatch(trimmedLine);
          if (numberMatch != null) {
            final numbersText = numberMatch.group(1) ?? '';
            result['luckyNumbers'] = numbersText
                .split(RegExp(r'[、，,]'))
                .map((number) => number.trim())
                .where((number) => number.isNotEmpty)
                .toList();
          }
        }
      }
    } catch (e) {
      print('解析备用格式失败: $e');
    }
  }
  
  // 尝试更宽松的格式解析
  void _parseLooseFormat(Map<String, List<String>> result) {
    try {
      final lines = content.split('\n');
      
      // 查找包含关键词的行
      for (String line in lines) {
        final trimmedLine = line.trim();
        
        // 尝试找到宜忌相关内容
        if (trimmedLine.contains('宜') && !trimmedLine.contains('忌') && 
            !trimmedLine.contains('不宜') && result['suitable']!.isEmpty) {
          // 排除标题行
          if (!trimmedLine.contains('今日宜') && !trimmedLine.contains('宜：')) {
            final items = trimmedLine.split(RegExp(r'[,，、]'))
                .map((item) => item.trim())
                .where((item) => item.isNotEmpty && item != '宜')
                .toList();
            if (items.isNotEmpty) {
              result['suitable'] = items;
            }
          }
        }
        
        if ((trimmedLine.contains('忌') || trimmedLine.contains('不宜')) && 
            !trimmedLine.contains('今日忌') && !trimmedLine.contains('忌：') && 
            result['avoid']!.isEmpty) {
          final items = trimmedLine.split(RegExp(r'[,，、]'))
              .map((item) => item.trim())
              .where((item) => item.isNotEmpty && item != '忌' && item != '不宜')
              .toList();
          if (items.isNotEmpty) {
            result['avoid'] = items;
          }
        }
        
        // 查找颜色相关内容
        if ((trimmedLine.contains('色') || trimmedLine.contains('颜色')) && 
            result['luckyColors']!.isEmpty) {
          final colorMatch = RegExp(r'[：:]\s*(.+)$').firstMatch(trimmedLine);
          if (colorMatch != null) {
            final colorsText = colorMatch.group(1) ?? '';
            final colors = colorsText
                .split(RegExp(r'[、，,]'))
                .map((color) => color.trim())
                .where((color) => color.isNotEmpty)
                .toList();
            if (colors.isNotEmpty) {
              result['luckyColors'] = colors;
            }
          }
        }
        
        // 查找数字相关内容
        if (trimmedLine.contains('数字') && result['luckyNumbers']!.isEmpty) {
          final numberMatch = RegExp(r'[：:]\s*(.+)$').firstMatch(trimmedLine);
          if (numberMatch != null) {
            final numbersText = numberMatch.group(1) ?? '';
            final numbers = numbersText
                .split(RegExp(r'[、，,]'))
                .map((number) => number.trim())
                .where((number) => number.isNotEmpty)
                .toList();
            if (numbers.isNotEmpty) {
              result['luckyNumbers'] = numbers;
            }
          }
        }
      }
    } catch (e) {
      print('解析宽松格式失败: $e');
    }
  }

  // 获取整体运势描述
  String getOverallFortune() {
    try {
      final overallMatch = RegExp(r'\*\*整体运势：?\*\*\s*([^\n]+)', multiLine: true).firstMatch(content);
      return overallMatch?.group(1)?.trim() ?? '';
    } catch (e) {
      return '';
    }
  }

  @override
  String toString() {
    return 'DailyFortune{date: $date, lunarDate: $lunarDate, isAIGenerated: $isAIGenerated}';
  }
} 