import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../widgets/common_widgets.dart';
import 'package:provider/provider.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/user_provider.dart';
import '../providers/daily_fortune_provider.dart';
import '../models/daily_fortune.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  String _getGreetingText() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return '命主,早上好！';
    } else if (hour < 18) {
      return '命主,下午好！';
    } else {
      return '命主,晚上好！';
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAutoLogin();
      _loadDailyFortune();
    });
  }

  Future<void> _checkAutoLogin() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    await userProvider.autoLogin();
  }

  Future<void> _loadDailyFortune() async {
    final fortuneProvider = Provider.of<DailyFortuneProvider>(context, listen: false);
    await fortuneProvider.fetchDailyFortune();
  }

  String _getCurrentDateString() {
    final now = DateTime.now();
    try {
      final formatter = DateFormat('yyyy年MM月dd日 EEEE', 'zh_CN');
      return formatter.format(now);
    } catch (e) {
      // 如果本地化失败，使用简单格式
      final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      final weekday = weekdays[now.weekday - 1];
      return '${now.year}年${now.month}月${now.day}日 $weekday';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFF8FAFC),
          Color(0xFFE2E8F0),
        ],
        child: Column(
          children: [
            // iOS状态栏
            const IOSStatusBar(),
            
            // 顶部导航栏
            CommonNavBar(
              title: '银发-满天神佛',
              trailing: const ConnectionStatusWidget(
                showDetails: true,
                showRefreshButton: true,
              ),
              onTrailingPressed: () {
                context.go('/profile');
              },
            ),
            
            // 连接状态横幅
            const ConnectionBanner(),
            
            // 主内容区
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 问候语区域
                    _buildGreetingSection(),
                    
                    // 每日运势卡片
                    _buildDailyFortuneCard(),
                    
                    // 预测服务区域
                    _buildPredictionServicesSection(),
                    
                    // 预测组合区域
                    _buildPredictionComboSection(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            
            // 底部导航
            const BottomNavigation(currentIndex: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildGreetingSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AnimatedOpacity(
            opacity: 1.0,
            duration: const Duration(milliseconds: 1000),
            child: Text(
              _getGreetingText(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '愿您今日诸事顺遂，心想事成',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF4B5563),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDailyFortuneCard() {
    return Consumer<DailyFortuneProvider>(
      builder: (context, fortuneProvider, child) {
        final fortune = fortuneProvider.dailyFortune;
        final isLoading = fortuneProvider.isLoading;
        final error = fortuneProvider.error;

        return Container(
          margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              // 运势标题栏
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '每日黄历',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getCurrentDateString(),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                        if (fortune?.lunarDate != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            fortune!.lunarDate,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white60,
                            ),
                          ),
                        ],
                      ],
                    ),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: isLoading ? null : () async {
                            await fortuneProvider.fetchDailyFortune(forceRefresh: true);
                          },
                          child: AnimatedRotation(
                            duration: const Duration(milliseconds: 500),
                            turns: isLoading ? 1 : 0,
                            child: Icon(
                              FontAwesomeIcons.sync,
                              color: isLoading ? Colors.white60 : Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Icon(
                          FontAwesomeIcons.star,
                          color: Color(0xFFFDE047),
                          size: 20,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 运势内容
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                child: _buildFortuneContent(fortune, isLoading, error),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFortuneContent(DailyFortune? fortune, bool isLoading, String? error) {
    if (isLoading) {
      return const Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 12),
          Text(
            '正在为您生成今日运势...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      );
    }

    if (error != null && fortune == null) {
      return Column(
        children: [
          const Icon(
            FontAwesomeIcons.exclamationTriangle,
            color: Colors.white70,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '获取运势失败',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error.replaceAll('Exception: ', ''),
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    if (fortune == null) {
      return const Text(
        '暂无运势数据',
        style: TextStyle(
          color: Colors.white70,
          fontSize: 14,
        ),
      );
    }

    // 解析运势详情
    final fortuneDetails = fortune.parseFortuneDetails();
    final overallFortune = fortune.getOverallFortune();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 整体运势
        if (overallFortune.isNotEmpty) ...[
          Text(
            overallFortune,
            style: const TextStyle(
              fontSize: 15,
              color: Colors.white,
              height: 1.4,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
        ],
        
        // 今日宜忌
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 今日宜
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '今日宜',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...fortuneDetails['suitable']!.take(3).map((item) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      '• $item',
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                        height: 1.3,
                      ),
                    ),
                  )),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 今日忌
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '今日忌',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...fortuneDetails['avoid']!.take(3).map((item) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      '• $item',
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                        height: 1.3,
                      ),
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // 幸运信息
        Row(
          children: [
            if (fortuneDetails['luckyColors']!.isNotEmpty) ...[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '幸运色',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      fortuneDetails['luckyColors']!.join('、'),
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            if (fortuneDetails['luckyNumbers']!.isNotEmpty) ...[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '幸运数字',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      fortuneDetails['luckyNumbers']!.join('、'),
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        
        // 查看详情按钮
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () {
            _showFortuneDetailsDialog(context, fortune);
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: const Text(
              '查看完整运势',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  void _showFortuneDetailsDialog(BuildContext context, DailyFortune fortune) {
    // 解析运势详情
    final fortuneDetails = fortune.parseFortuneDetails();
    final overallFortune = fortune.getOverallFortune();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(maxHeight: 600),
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '今日运势详情',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  fortune.date,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                  ),
                ),
                if (fortune.lunarDate.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    fortune.lunarDate,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 整体运势
                        if (overallFortune.isNotEmpty) ...[
                          const Text(
                            '整体运势',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            overallFortune,
                            style: const TextStyle(
                              fontSize: 15,
                              height: 1.5,
                              color: Color(0xFF374151),
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),
                        ],
                        
                        // 今日宜
                        const Text(
                          '今日宜',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...fortuneDetails['suitable']!.map((item) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '• ',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Color(0xFF374151),
                                  height: 1.5,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  item,
                                  style: const TextStyle(
                                    fontSize: 15,
                                    color: Color(0xFF374151),
                                    height: 1.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )),
                        
                        const SizedBox(height: 16),
                        
                        // 今日忌
                        const Text(
                          '今日忌',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...fortuneDetails['avoid']!.map((item) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '• ',
                                style: TextStyle(
                                  fontSize: 15,
                                  color: Color(0xFF374151),
                                  height: 1.5,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  item,
                                  style: const TextStyle(
                                    fontSize: 15,
                                    color: Color(0xFF374151),
                                    height: 1.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )),
                        
                        const SizedBox(height: 16),
                        
                        // 幸运信息
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (fortuneDetails['luckyColors']!.isNotEmpty) ...[
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      '幸运色',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF1F2937),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      fortuneDetails['luckyColors']!.join('、'),
                                      style: const TextStyle(
                                        fontSize: 15,
                                        color: Color(0xFF374151),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                            
                            const SizedBox(width: 16),
                            
                            if (fortuneDetails['luckyNumbers']!.isNotEmpty) ...[
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      '幸运数字',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF1F2937),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      fortuneDetails['luckyNumbers']!.join('、'),
                                      style: const TextStyle(
                                        fontSize: 15,
                                        color: Color(0xFF374151),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // 原始内容（可选，如果需要保留原始内容）
                        if (fortune.isAIGenerated) ...[
                          const Divider(),
                          const SizedBox(height: 16),
                          const Text(
                            'AI生成的完整解析',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            fortune.content,
                            style: const TextStyle(
                              fontSize: 14,
                              height: 1.6,
                              color: Color(0xFF6B7280),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPredictionServicesSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '预测服务',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),
          
          // 六爻预测（单独一行）
          FunctionCard(
            onTap: () => context.go('/liuyao'),
            child: Column(
              children: [
                const Icon(
                  FontAwesomeIcons.coins,
                  size: 48,
                  color: Color(0xFFD97706),
                ),
                const SizedBox(height: 12),
                const Text(
                  '六爻预测',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '摇卦问事，古法占卜',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF59E0B),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '开始预测',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 其他三个预测（一行三列）
          Row(
            children: [
              // 紫微斗数
              Expanded(
                child: FunctionCard(
                  onTap: () => context.go('/ziwei'),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(
                        FontAwesomeIcons.star,
                        size: 36,
                        color: Color(0xFF8B5CF6),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '紫微斗数',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '命盘分析',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF8B5CF6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '开始分析',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 八字预测
              Expanded(
                child: FunctionCard(
                  onTap: () => context.go('/bazi'),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(
                        FontAwesomeIcons.calendarAlt,
                        size: 36,
                        color: Color(0xFF16A34A),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '八字预测',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '命理分析',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF16A34A),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '开始算命',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 星座运势
              Expanded(
                child: FunctionCard(
                  onTap: () => context.go('/constellation'),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(
                        FontAwesomeIcons.moon,
                        size: 36,
                        color: Color(0xFF3B82F6),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '星座运势',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '每日更新',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF3B82F6),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          '查看运势',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionComboSection() {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '预测组合',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              // 星座组合
              Expanded(
                child: FunctionCard(
                  onTap: () => context.go('/prediction-combo'),
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            FontAwesomeIcons.moon,
                            size: 24,
                            color: Color(0xFF3B82F6),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            '星座+紫微',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '星座+紫微预测年月日运势，全面解析',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '最近组合: 3次',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF9CA3AF),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF3B82F6),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '开始组合',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // 八字组合
              Expanded(
                child: FunctionCard(
                  onTap: () => context.go('/prediction-combo'),
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(
                            FontAwesomeIcons.calendarAlt,
                            size: 24,
                            color: Color(0xFF16A34A),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            '八字+六爻',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '八字+六爻全面分析运势，深度解读',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '最近组合: 1次',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF9CA3AF),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF16A34A),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '开始组合',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 