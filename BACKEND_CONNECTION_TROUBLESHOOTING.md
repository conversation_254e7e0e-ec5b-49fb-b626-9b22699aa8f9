# 后端连接问题排查指南

## 问题现象
前端页面显示"后端离线"，但后端明明已经启动。

## 问题分析

### 可能的原因
1. **后端服务未正确启动**
2. **端口冲突或被占用**
3. **防火墙阻止连接**
4. **API端点配置错误**
5. **AI服务状态检测失败**

## 排查步骤

### 1. 检查后端服务状态

#### 方法1：使用检查脚本
```bash
# 运行检查脚本
./check_backend.bat
```

#### 方法2：手动检查
```bash
# 检查端口是否在监听
netstat -ano | findstr :3001

# 检查Node.js进程
tasklist | findstr node

# 测试API端点
curl http://localhost:3001/api/v1/chat/status
```

### 2. 启动后端服务

#### 方法1：使用NPM脚本
```bash
cd backend
npm start
```

#### 方法2：直接运行
```bash
cd backend
node server.js
```

#### 方法3：开发模式
```bash
cd backend
npm run dev
```

### 3. 检查配置

#### 前端配置
- 文件：`lib/services/api_service.dart`
- 默认地址：`http://localhost:3001`

#### 后端配置
- 文件：`backend/server.js`
- 默认端口：3001

### 4. 常见问题及解决方案

#### 问题1：端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :3001

# 结束占用进程（替换PID）
taskkill /PID <PID> /F
```

#### 问题2：权限问题
```bash
# 以管理员身份运行命令提示符
# 或者更改端口到8080等其他端口
```

#### 问题3：防火墙阻止
1. 打开Windows防火墙设置
2. 添加Node.js为允许的应用
3. 或者暂时关闭防火墙测试

#### 问题4：依赖缺失
```bash
cd backend
npm install
```

## 修复内容

### 1. 改进了AI服务状态检测
- 修改了`AIService.js`的`getServiceStatus`方法
- 即使AI服务不可用，也会返回后端在线状态
- 提供更详细的错误信息

### 2. 增强了前端连接检测
- 改进了`testConnection`方法
- 添加了详细的调试日志
- 更智能的状态判断

### 3. 添加了连接状态显示
- 实时显示连接状态
- 提供手动刷新功能
- 详细的诊断信息

## 验证修复

### 1. 启动后端
```bash
cd backend
npm start
```

### 2. 检查状态
```bash
curl http://localhost:3001/api/v1/chat/status
```

### 3. 启动前端
```bash
flutter run
```

### 4. 观察连接状态
- 查看右上角的连接状态指示器
- 应该显示绿色的"后端已连接"

## 预防措施

### 1. 自动启动脚本
创建一个启动脚本同时启动前后端：
```bash
# start_all.bat
start cmd /k "cd backend && npm start"
start cmd /k "flutter run"
```

### 2. 环境检查
定期检查：
- Node.js版本
- NPM依赖
- 端口占用情况

### 3. 监控工具
- 使用应用内的连接调试页面
- 定期查看后端日志
- 设置自动重启机制

## 技术细节

### 连接检测流程
1. 前端每30秒检测一次连接
2. 调用`/api/v1/chat/status`端点
3. 检查响应状态码和内容
4. 更新UI显示状态

### 状态判断逻辑
- HTTP 200 + 有效响应 = 连接成功
- 网络错误 = 连接失败
- 超时 = 连接超时

### 错误处理
- 详细的错误日志记录
- 用户友好的错误提示
- 自动重试机制

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Node.js版本
3. 错误日志
4. 网络环境
5. 防火墙设置

---

## 更新日志

### v1.0.0 (2024-01-10)
- 初始版本
- 基础连接检测
- 状态显示功能

### v1.1.0 (2024-01-10)
- 修复AI服务状态检测问题
- 改进连接检测逻辑
- 添加详细的调试信息 