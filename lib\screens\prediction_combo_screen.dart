import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import 'package:intl/intl.dart';
import 'dart:math' as math;
import 'package:provider/provider.dart'; // Added for Provider
import '../providers/constellation_provider.dart'; // Added for ConstellationProvider

enum ComboType {
  constellationZiwei,
  baziZiwei,
}

class PredictionComboScreen extends StatefulWidget {
  const PredictionComboScreen({super.key});

  @override
  State<PredictionComboScreen> createState() => _PredictionComboScreenState();
}

class _PredictionComboScreenState extends State<PredictionComboScreen> {
  ComboType _selectedCombo = ComboType.constellationZiwei;
  int _selectedTabIndex = 0;
  final List<String> _tabs = ['年运预测', '月运预测', '日运预测'];
  
  // 用户出生日期时间
  DateTime? _birthDateTime;
  String? _constellation;
  final TextEditingController _birthDateController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 添加监听Provider状态
    Future.microtask(() {
      final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
      constellationProvider.addListener(_checkAnalysisStatus);
    });
  }
  
  @override
  void dispose() {
    // 移除监听
    Provider.of<ConstellationProvider>(context, listen: false).removeListener(_checkAnalysisStatus);
    _birthDateController.dispose();
    super.dispose();
  }
  
  // 检查分析状态，自动导航到结果页面
  bool _isNavigating = false;
  
  void _checkAnalysisStatus() {
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    
    // 如果分析已完成且有结果，且尚未开始导航，则自动导航到结果页面
    if (!constellationProvider.isLoadingComboAnalysis && 
        constellationProvider.comboAnalysisResult != null && 
        !_isNavigating) {
      print('🔄 Provider状态变化：分析已完成且有结果，准备自动导航');
      _isNavigating = true;
      
      // 延迟一小段时间确保状态已完全更新
      Future.delayed(Duration(milliseconds: 500), () {
        // 使用状态清除任何可能的对话框
        if (context.mounted) {
          // 首先尝试关闭当前所有对话框
          try {
            print('🚪 Provider监听：尝试关闭所有对话框');
            Navigator.of(context, rootNavigator: true).popUntil((route) => route.isFirst);
            print('✅ Provider监听：对话框关闭成功');
          } catch (e) {
            print('⚠️ Provider监听：关闭对话框失败: $e');
          }
          
          // 然后导航到结果页面
          if (context.mounted && _constellation != null) {
            print('🧭 Provider监听：导航到星座页面');
            context.go('/constellation', extra: {
              'constellation': _constellation,
              'analysisType': 'yearly',
              'isFromCombo': true,
            });
            
            // 显示通知
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('✅ 分析完成，已跳转到结果页面'),
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFF8FAFC),
          Color(0xFFE2E8F0),
        ],
        child: Column(
          children: [
            const IOSStatusBar(),
            CommonNavBar(
              title: '预测组合',
              leading: const Icon(
                Icons.arrow_back_ios,
                color: Colors.white,
                size: 20,
              ),
              onLeadingPressed: () => context.pop(),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 组合选择区域
                    _buildComboSelector(),
                    
                    // 组合说明
                    _buildComboDescription(),
                    
                    // 用户出生时间输入（仅在星座+紫微模式显示）
                    if (_selectedCombo == ComboType.constellationZiwei)
                      _buildBirthTimeSelector(),
                    
                    // 选项卡
                    _buildTabSelector(),
                    
                    // 对比内容
                    _buildComparisonContent(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            const BottomNavigation(currentIndex: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildComboSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 150, // 固定高度确保两个按钮一致
      child: Row(
        children: [
          // 星座+紫微选择
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCombo = ComboType.constellationZiwei;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _selectedCombo == ComboType.constellationZiwei
                      ? const Color(0xFF3B82F6)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _selectedCombo == ComboType.constellationZiwei
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFFE5E7EB),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center, // 确保内容居中
                  children: [
                    Icon(
                      FontAwesomeIcons.moon,
                      size: 32,
                      color: _selectedCombo == ComboType.constellationZiwei
                          ? Colors.white
                          : const Color(0xFF3B82F6),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '星座+紫微',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _selectedCombo == ComboType.constellationZiwei
                            ? Colors.white
                            : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '西方占星与东方命理',
                      style: TextStyle(
                        fontSize: 12,
                        color: _selectedCombo == ComboType.constellationZiwei
                            ? Colors.white70
                            : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 八字+紫微选择
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCombo = ComboType.baziZiwei;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _selectedCombo == ComboType.baziZiwei
                      ? const Color(0xFF16A34A)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _selectedCombo == ComboType.baziZiwei
                        ? const Color(0xFF16A34A)
                        : const Color(0xFFE5E7EB),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center, // 确保内容居中
                  children: [
                    Icon(
                      FontAwesomeIcons.calendarAlt,
                      size: 32,
                      color: _selectedCombo == ComboType.baziZiwei
                          ? Colors.white
                          : const Color(0xFF16A34A),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '八字+紫微',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _selectedCombo == ComboType.baziZiwei
                            ? Colors.white
                            : const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '双重东方命理分析',
                      style: TextStyle(
                        fontSize: 12,
                        color: _selectedCombo == ComboType.baziZiwei
                            ? Colors.white70
                            : const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComboDescription() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _selectedCombo == ComboType.constellationZiwei
                    ? FontAwesomeIcons.moon
                    : FontAwesomeIcons.calendarAlt,
                size: 24,
                color: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFF16A34A),
              ),
              const SizedBox(width: 12),
              Text(
                _selectedCombo == ComboType.constellationZiwei
                    ? '星座与紫微组合'
                    : '八字与紫微组合',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            _selectedCombo == ComboType.constellationZiwei
                ? '星座占星与紫微斗数结合，可以全面预测年月日运势。星座提供西方视角的运势解读，紫微斗数提供东方命理的精准分析，两者结合能够更全面地把握运势变化。'
                : '八字与紫微斗数结合可以全面预测整体运势。八字提供五行生克的命理基础，紫微斗数提供宫位星曜的具体表现，两者结合能够更全面地把握人生走向。',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF4B5563),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(_tabs.length, (index) {
          final isSelected = _selectedTabIndex == index;
          return Container(
            width: MediaQuery.of(context).size.width / _tabs.length - 24, // 平均分配宽度，减去边距
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = index;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: isSelected
                      ? (_selectedCombo == ComboType.constellationZiwei
                          ? const Color(0xFFEBF8FF)
                          : const Color(0xFFECFDF5))
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? (_selectedCombo == ComboType.constellationZiwei
                            ? const Color(0xFF3B82F6)
                            : const Color(0xFF16A34A))
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Text(
                  _tabs[index],
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected
                        ? (_selectedCombo == ComboType.constellationZiwei
                            ? const Color(0xFF3B82F6)
                            : const Color(0xFF16A34A))
                        : const Color(0xFF6B7280),
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildComparisonContent() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // 主要对比表格
          _buildComparisonTable(),
          
          const SizedBox(height: 16),
          
          // 优势分析
          _buildAdvantageAnalysis(),
          
          const SizedBox(height: 16),
          
          // 实用建议
          _buildPracticalAdvice(),
        ],
      ),
    );
  }

  Widget _buildComparisonTable() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_tabs[_selectedTabIndex]}组合分析',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 16),
          
          // 表格头部
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFF9FAFB),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 3,
                  child: Text(
                    '分析项目',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF374151),
                    ),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    _selectedCombo == ComboType.constellationZiwei ? '星座运势' : '八字命理',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF374151),
                    ),
                  ),
                ),
                const Expanded(
                  flex: 4,
                  child: Text(
                    '紫微斗数',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF374151),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 表格内容
          ...List.generate(
            _getTableData().length,
            (index) => _buildTableRow(_getTableData()[index]),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(Map<String, String> data) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Text(
              data['item'] ?? '',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF374151),
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFFEBF8FF)
                    : const Color(0xFFECFDF5),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                data['left'] ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: _selectedCombo == ComboType.constellationZiwei
                      ? const Color(0xFF1E40AF)
                      : const Color(0xFF065F46),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 4,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFFF9FAFB),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                data['right'] ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF4B5563),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, String>> _getTableData() {
    if (_selectedCombo == ComboType.constellationZiwei) {
      switch (_selectedTabIndex) {
        case 0: // 年运预测
          return [
            {'item': '预测周期', 'left': '按阳历年计算，每年更新', 'right': '按流年太岁，农历年计算'},
            {'item': '预测方法', 'left': '行星运行与本命盘的相位关系', 'right': '流年星耀入宫与本命盘的组合'},
            {'item': '重点关注', 'left': '木星、土星等行星的迁移', 'right': '太岁、岁破等星耀的影响'},
            {'item': '组合优势', 'left': '西方视角，宏观把握', 'right': '东方命理，细节分析'},
            {'item': '预测精度', 'left': '年度大趋势，方向性预测', 'right': '具体事项，细节性预测'},
          ];
        case 1: // 月运预测
          return [
            {'item': '预测周期', 'left': '按阳历月份，太阳星座变化', 'right': '按农历月份，月系星耀变化'},
            {'item': '预测方法', 'left': '月亮、水星等快速行星运行', 'right': '月系星耀在各宫位的表现'},
            {'item': '重点关注', 'left': '月亮相位与本命盘的关系', 'right': '月系星耀与本命盘的互动'},
            {'item': '组合优势', 'left': '情绪波动，人际关系变化', 'right': '具体事项，短期运势预测'},
          ];
        case 2: // 日运预测
          return [
            {'item': '预测周期', 'left': '按阳历日期，日座变化', 'right': '按农历日期，日系星耀变化'},
            {'item': '预测方法', 'left': '日运行星与本命盘的互动', 'right': '日系星耀的短期影响'},
            {'item': '重点关注', 'left': '太阳、月亮的日常影响', 'right': '日系星耀的具体表现'},
            {'item': '组合优势', 'left': '日常生活指导', 'right': '具体行动建议'},
          ];
        default:
          return [];
      }
    } else {
      switch (_selectedTabIndex) {
        case 0: // 整体运势
          return [
            {'item': '命理基础', 'left': '天干地支，五行生克制化', 'right': '星曜组合，宫位飞化'},
            {'item': '分析重点', 'left': '五行平衡，用神喜忌', 'right': '主星副星，十二宫位'},
            {'item': '运势判断', 'left': '大运流年，十年一变', 'right': '流年星耀，逐年变化'},
            {'item': '预测范围', 'left': '整体命运，性格特点', 'right': '具体事项，时间节点'},
            {'item': '组合价值', 'left': '根本命理，本质分析', 'right': '细节表现，具体指导'},
          ];
        case 1: // 事业财运
          return [
            {'item': '事业分析', 'left': '官印相生，事业宫位', 'right': '官禄宫，事业星耀'},
            {'item': '财运判断', 'left': '财星得失，财库强弱', 'right': '财帛宫，财星组合'},
            {'item': '时机把握', 'left': '流年财官，大运配合', 'right': '流年星耀，宫位变化'},
            {'item': '发展方向', 'left': '五行喜忌，行业选择', 'right': '星耀特性，具体建议'},
          ];
        case 2: // 健康感情
          return [
            {'item': '健康状况', 'left': '五行偏枯，身体强弱', 'right': '疾厄宫，健康星耀'},
            {'item': '感情分析', 'left': '夫妻宫位，配偶情况', 'right': '夫妻宫，感情星耀'},
            {'item': '健康建议', 'left': '五行调理，养生方法', 'right': '星耀影响，具体措施'},
            {'item': '感情指导', 'left': '合婚分析，感情发展', 'right': '桃花运势，关系建议'},
          ];
        default:
          return [];
      }
    }
  }

  Widget _buildAdvantageAnalysis() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.lightbulb,
                size: 20,
                color: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFF16A34A),
              ),
              const SizedBox(width: 8),
              const Text(
                '组合优势分析',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          ...List.generate(
            _getAdvantageData().length,
            (index) => Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 8, right: 12),
                    decoration: BoxDecoration(
                      color: _selectedCombo == ComboType.constellationZiwei
                          ? const Color(0xFF3B82F6)
                          : const Color(0xFF16A34A),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      _getAdvantageData()[index],
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF4B5563),
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getAdvantageData() {
    if (_selectedCombo == ComboType.constellationZiwei) {
      return [
        '西方占星学提供宏观的性格分析和年度运势趋势，帮助理解整体运势方向',
        '紫微斗数提供精确的时间节点和具体事项预测，补充细节分析',
        '两种体系相互验证，提高预测的准确性和可靠性',
        '结合不同文化背景的智慧，形成更全面的人生指导',
        '适合对未来规划有明确需求的现代人群',
      ];
    } else {
      return [
        '八字命理提供深层的命运基础和性格本质分析，是预测的根本',
        '紫微斗数提供具体的事项预测和时间节点，是预测的细节',
        '两者都是中华传统文化的精髓，相互补充形成完整体系',
        '八字看本质，紫微看表现，层次分明互不冲突',
        '特别适合深入了解自己命运规律的用户',
      ];
    }
  }

  Widget _buildPracticalAdvice() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.compass,
                size: 20,
                color: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFF16A34A),
              ),
              const SizedBox(width: 8),
              const Text(
                '实用建议',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _selectedCombo == ComboType.constellationZiwei
                  ? const Color(0xFFEBF8FF)
                  : const Color(0xFFECFDF5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '最佳使用方法：',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _selectedCombo == ComboType.constellationZiwei
                        ? const Color(0xFF1E40AF)
                        : const Color(0xFF065F46),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedCombo == ComboType.constellationZiwei
                      ? '1. 先看星座运势了解大方向\n2. 再看紫微斗数确定具体时间\n3. 结合两者制定行动计划\n4. 定期回顾调整策略'
                      : '1. 先看八字了解基本命理\n2. 再看紫微确定具体表现\n3. 结合大运流年制定计划\n4. 根据星耀变化调整策略',
                  style: TextStyle(
                    fontSize: 14,
                    color: _selectedCombo == ComboType.constellationZiwei
                        ? const Color(0xFF1E40AF)
                        : const Color(0xFF065F46),
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 开始分析按钮
          Container(
            width: double.infinity,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: ElevatedButton(
              onPressed: () {
                _showAnalysisDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFF16A34A),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: Text(
                '开始${_selectedCombo == ComboType.constellationZiwei ? '星座+紫微' : '八字+紫微'}分析',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 新增用户出生时间选择组件
  Widget _buildBirthTimeSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.calendarAlt,
                size: 20,
                color: const Color(0xFF3B82F6),
              ),
              const SizedBox(width: 8),
              const Text(
                '填写出生信息',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '请输入您的出生日期和时间，系统将根据您的信息确定您的星座',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF4B5563),
            ),
          ),
          const SizedBox(height: 20),
          
          // 出生日期选择
          GestureDetector(
            onTap: () => _showDatePicker(context),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFFE5E7EB),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.calendar_month,
                    color: Color(0xFF3B82F6),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _birthDateTime == null
                          ? '请选择出生日期'
                          : DateFormat('yyyy-MM-dd HH:mm').format(_birthDateTime!),
                      style: TextStyle(
                        fontSize: 16,
                        color: _birthDateTime == null
                            ? const Color(0xFF9CA3AF)
                            : const Color(0xFF1F2937),
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Color(0xFF9CA3AF),
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 显示星座结果
          if (_constellation != null)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFEBF8FF),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    FontAwesomeIcons.star,
                    color: Color(0xFF3B82F6),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '您的星座是：',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF4B5563),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _constellation!,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1E40AF),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // 显示日期选择器
  Future<void> _showDatePicker(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _birthDateTime ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      helpText: '选择出生日期',
      cancelText: '取消',
      confirmText: '确定',
    );

    if (pickedDate != null) {
      // 选择时间
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
        helpText: '选择出生时间',
        cancelText: '取消',
        confirmText: '确定',
      );

      if (pickedTime != null) {
        setState(() {
          _birthDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
          _constellation = _getConstellation(
            _birthDateTime!.month,
            _birthDateTime!.day,
          );
        });
      }
    }
  }

  // 根据出生日期判断星座
  String _getConstellation(int month, int day) {
    const List<String> constellations = [
      '水瓶座', '双鱼座', '白羊座', '金牛座', '双子座', '巨蟹座',
      '狮子座', '处女座', '天秤座', '天蝎座', '射手座', '摩羯座'
    ];
    
    const List<int> cutoffDays = [20, 19, 21, 20, 21, 21, 23, 23, 23, 23, 22, 22];
    
    if (day < cutoffDays[month - 1]) {
      return constellations[(month - 1) % 12];
    } else {
      return constellations[month % 12];
    }
  }
  
  // 在分析对话框中添加出生时间验证
  void _showAnalysisDialog() {
    // 在星座+紫微模式下，验证用户是否已输入出生时间
    if (_selectedCombo == ComboType.constellationZiwei && _birthDateTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先填写您的出生日期和时间'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }
    
    // 获取Provider以准备调用AI分析
    final constellationProvider = Provider.of<ConstellationProvider>(context, listen: false);
    
    if (_selectedCombo == ComboType.constellationZiwei && _birthDateTime != null && _constellation != null) {
      // 显示对话框，询问是否进行AI深度分析
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('星座+紫微深度分析', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '系统已确认您的星座为：$_constellation\n出生日期时间：${DateFormat('yyyy-MM-dd HH:mm').format(_birthDateTime!)}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              const Text(
                '是否使用火山方舟AI进行深度分析？这将分析您的日运、月运和年运，并提供详细的运势解读。',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              const Text(
                'AI分析可能需要30-60秒的时间，请耐心等待。',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop(); // 关闭确认对话框
                
                // 创建一个标记，用于跟踪对话框是否仍然显示
                bool isDialogShowing = true;
                
                print('🔄 准备显示加载对话框');
                // 显示加载对话框
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext dialogContext) {
                    print('📣 加载对话框builder被调用, dialogContext=$dialogContext');
                    return AlertDialog(  // 使用dialogContext而不是context
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 16),
                          const CircularProgressIndicator(),
                          const SizedBox(height: 24),
                          const Text('火山方舟AI正在为您分析星座+紫微组合运势...',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          const Text('这可能需要3-5分钟时间，AI模型正在深度分析您的星座和紫微数据',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          const Text('您可以点击下方按钮隐藏等待窗口，分析将在后台继续进行',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          const SizedBox(height: 8),
                          const Text('请耐心等待，不要关闭应用',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TextButton(
                                onPressed: () {
                                  // 用户取消操作，但不取消请求
                                  print('👆 用户点击了"隐藏等待窗口"按钮');
                                  isDialogShowing = false;
                                  print('🚪 尝试关闭对话框，dialogContext=$dialogContext');
                                  Navigator.of(dialogContext).pop();  // 使用dialogContext
                                  print('✅ 对话框关闭成功');
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('已关闭等待窗口，分析将在后台继续进行'),
                                      behavior: SnackBarBehavior.floating,
                                      duration: Duration(seconds: 3),
                                    ),
                                  );
                                },
                                child: const Text('隐藏等待窗口'),
                              ),
                              
                              // 添加一个分析完成后可以点击的按钮
                              // 初始状态下不可用，当分析完成后变为可用
                              StatefulBuilder(
                                builder: (context, setState) {
                                  // 添加一个定时刷新机制，每2秒检查一次状态
                                  void checkStatus() {
                                    if (context.mounted && dialogContext != null) {
                                      setState(() {
                                        // 只是触发刷新
                                        print('🔄 定时刷新对话框状态...');
                                      });
                                      
                                      // 继续定时检查
                                      Future.delayed(Duration(seconds: 2), checkStatus);
                                    }
                                  }
                                  
                                  // 启动定时检查
                                  Future.microtask(() => checkStatus());
                                  
                                  // 检查分析是否已完成
                                  final bool analysisCompleted = !constellationProvider.isLoadingComboAnalysis && 
                                                                   constellationProvider.comboAnalysisResult != null;
                                  
                                  if (analysisCompleted) {
                                    print('✅ 检测到分析已完成，启用结果按钮');
                                    
                                    // 如果分析已完成，稍后自动关闭对话框
                                    Future.delayed(Duration(seconds: 1), () {
                                      if (context.mounted && dialogContext != null) {
                                        try {
                                          print('🚪 分析完成，自动关闭对话框');
                                          Navigator.of(dialogContext).pop();
                                          
                                          // 导航到结果页面
                                          if (context.mounted && !_isNavigating) {
                                            _isNavigating = true;
                                            print('🧭 自动导航到星座页面');
                                            context.go('/constellation', extra: {
                                              'constellation': _constellation,
                                              'analysisType': 'yearly',
                                              'isFromCombo': true,
                                            });
                                          }
                                        } catch (e) {
                                          print('⚠️ 自动关闭对话框失败: $e');
                                        }
                                      }
                                    });
                                  }
                                  
                                  return TextButton(
                                    onPressed: analysisCompleted ? () {
                                      print('👆 用户点击了"查看分析结果"按钮');
                                      Navigator.of(dialogContext).pop();
                                      print('✅ 通过结果按钮关闭对话框');
                                      
                                      // 直接导航到星座页面显示结果
                                      _isNavigating = true;
                                      print('🧭 直接导航到星座页面，传递参数: constellation=$_constellation, analysisType=yearly, isFromCombo=true');
                                      context.go('/constellation', extra: {
                                        'constellation': _constellation,
                                        'analysisType': 'yearly', // 默认显示年运
                                        'isFromCombo': true,
                                      });
                                    } : null,
                                    child: Text(
                                      '查看分析结果',
                                      style: TextStyle(
                                        color: analysisCompleted ? Colors.green : Colors.grey,
                                        fontWeight: analysisCompleted ? FontWeight.bold : FontWeight.normal,
                                      ),
                                    ),
                                  );
                                }
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ).then((_) {
                  print('🔍 对话框被关闭，isDialogShowing=$isDialogShowing');
                });
                
                print('⏩ 加载对话框显示后代码继续执行');
                
                try {
                  // 调用Provider中的方法获取AI深度分析
                  print('🚀 开始请求星座+紫微深度分析...');
                  
                  // 使用Future.microtask确保对话框已经完全显示
                  Future.microtask(() async {
                    try {
                      // 在微任务中执行API调用，确保UI已更新
                      final result = await constellationProvider.getConstellationZiweiAnalysis(
                        birthDateTime: _birthDateTime!,
                        constellation: _constellation!,
                      );
                      
                      print('✅ 星座+紫微深度分析请求完成，结果: ${result != null ? '成功' : '失败'}');
                      
                      // 无论isDialogShowing状态如何，都尝试关闭对话框
                      // 这是关键修复 - 确保对话框总是被关闭
                      print('🚪 强制尝试关闭所有对话框');
                      if (context.mounted) {
                        try {
                          // 使用Navigator.of(context).popUntil方法确保回到预期页面
                          Navigator.of(context, rootNavigator: true).popUntil((route) {
                            print('📑 检查路由: ${route.settings.name}');
                            return route.isFirst;
                          });
                          print('✅ 对话框强制关闭成功');
                        } catch (e) {
                          print('⚠️ 强制关闭对话框失败: $e');
                          // 如果失败，多尝试几次直接的pop操作
                          try {
                            print('🔄 尝试多次pop操作');
                            Navigator.of(context, rootNavigator: true).pop();
                            Future.delayed(Duration(milliseconds: 100), () {
                              if (context.mounted) {
                                try {
                                  Navigator.of(context, rootNavigator: true).pop();
                                } catch (e) {
                                  print('二次pop失败: $e');
                                }
                              }
                            });
                          } catch (e2) {
                            print('❌ 多次pop操作失败: $e2');
                          }
                        }
                      }
                      
                      // 检查API结果并显示
                      if (result != null && context.mounted) {
                        print('📊 分析结果包含以下内容:');
                        print('- 年运数据: ${result.containsKey('yearlyFortune')}');
                        print('- 月运数据: ${result.containsKey('monthlyFortune')}');
                        print('- 日运数据: ${result.containsKey('dailyFortune')}');
                        print('- 总结: ${result.containsKey('summary')}');
                        
                        // 使用更直接的方式导航到结果页面
                        print('📊 准备直接导航到结果页面，跳过显示对话框');
                        
                        // 添加短暂延迟确保前一个对话框已完全关闭
                        await Future.delayed(Duration(milliseconds: 300));
                        
                        if (context.mounted) {
                          // 直接导航到星座页面显示结果
                          print('🧭 直接导航到星座页面，传递参数: constellation=$_constellation, analysisType=yearly, isFromCombo=true');
                          context.go('/constellation', extra: {
                            'constellation': _constellation,
                            'analysisType': 'yearly', // 默认显示年运
                            'isFromCombo': true,
                          });
                          
                          print('✅ 已导航到分析结果页面');
                          
                          // 显示成功通知
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('✅ 分析完成，已跳转到结果页面'),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        } else {
                          print('⚠️ context已失效，无法导航到结果页面');
                        }
                      } else if (context.mounted) {
                        // 处理失败情况
                        print('❌ 分析失败，错误: ${constellationProvider.comboAnalysisError}');
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('分析失败：${constellationProvider.comboAnalysisError}'),
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.red,
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    } catch (e) {
                      print('❌❌ 微任务中发生异常: $e');
                      // 确保关闭对话框
                      if (context.mounted) {
                        try {
                          Navigator.of(context, rootNavigator: true).pop();
                          print('✅ 异常情况下对话框安全关闭');
                        } catch (navError) {
                          print('❌ 异常情况下关闭对话框失败: $navError');
                        }
                      }
                      
                      // 显示错误信息
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('分析过程中发生错误：${e.toString()}'),
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.red,
                            duration: const Duration(seconds: 3),
                          ),
                        );
                      }
                    }
                  });
                } catch (e) {
                  // 主try-catch块的异常处理
                  print('❌❌❌ 主try-catch块捕获异常: $e');
                  if (context.mounted) {
                    // 尝试关闭对话框
                    try {
                      Navigator.of(context, rootNavigator: true).pop();
                      print('✅ 主异常处理中对话框关闭成功');
                    } catch (navError) {
                      print('❌ 主异常处理中关闭对话框失败: $navError');
                    }
                    
                    // 显示错误消息
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('处理请求时发生错误：${e.toString()}'),
                        behavior: SnackBarBehavior.floating,
                        backgroundColor: Colors.red,
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3B82F6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('开始AI分析'),
            ),
          ],
        ),
      );
    } else {
      // 如果是八字+紫微模式，或没有选择出生日期，则使用原始逻辑
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text(
            _selectedCombo == ComboType.constellationZiwei ? '星座+紫微分析' : '八字+紫微分析',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _selectedCombo == ComboType.constellationZiwei
                    ? '请先完成星座和紫微的基础测试，然后系统会自动进行组合分析。'
                    : '请先完成八字和紫微的基础测试，然后系统会自动进行组合分析。',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              const Text(
                '建议测试顺序：',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _selectedCombo == ComboType.constellationZiwei
                    ? '1. 星座运势分析\n2. 紫微斗数排盘\n3. 组合运势解读'
                    : '1. 八字命理分析\n2. 紫微斗数排盘\n3. 组合运势解读',
                style: const TextStyle(fontSize: 14),
              ),
              if (_selectedCombo == ComboType.constellationZiwei && _constellation != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEBF8FF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.infoCircle,
                        color: Color(0xFF3B82F6),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '系统已确认您的星座为：$_constellation',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF1E40AF),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (_selectedCombo == ComboType.constellationZiwei) {
                  // 传递星座信息到星座页面
                  context.go('/constellation', extra: {'constellation': _constellation});
                } else {
                  context.go('/bazi');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: _selectedCombo == ComboType.constellationZiwei
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFF16A34A),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text('开始测试'),
            ),
          ],
        ),
      );
    }
  }
  
  // 显示分析结果选择对话框
  void _showAnalysisResultDialog(Map<String, dynamic> result) {
    print('准备显示分析结果对话框，数据: ${result.keys.toList()}');
    
    // 检查数据完整性
    final bool hasYearlyData = result.containsKey('yearlyFortune') && result['yearlyFortune'] != null;
    final bool hasMonthlyData = result.containsKey('monthlyFortune') && result['monthlyFortune'] != null;
    final bool hasDailyData = result.containsKey('dailyFortune') && result['dailyFortune'] != null;
    
    print('数据完整性检查: 年运=$hasYearlyData, 月运=$hasMonthlyData, 日运=$hasDailyData');
    
    if (!hasYearlyData && !hasMonthlyData && !hasDailyData) {
      // 数据异常，显示错误消息
      print('❌ 分析结果数据不完整，显示错误消息');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分析结果数据不完整，请重试'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    try {
      print('🔄 开始调用showDialog显示分析结果对话框');
      
      // 使用更直接的方式导航到结果页面，避免对话框显示问题
      print('🧭 直接导航到星座页面，传递参数: constellation=$_constellation, analysisType=yearly, isFromCombo=true');
      context.go('/constellation', extra: {
        'constellation': _constellation,
        'analysisType': 'yearly', // 默认显示年运
        'isFromCombo': true,
      });
      
      print('✅ 已导航到分析结果页面');
      
      // 显示成功通知
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ 分析完成，已跳转到结果页面'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      print('❌ 导航到分析结果页面失败: $e');
      // 显示错误消息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('无法显示分析结果: ${e.toString()}'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
  
  // 构建分析选项按钮
  Widget _buildAnalysisOptionButton(String title, String description, String type) {
    return GestureDetector(
      onTap: () {
        print('👆 用户选择了$title运势分析');
        Navigator.of(context).pop(); // 关闭对话框
        print('✅ 关闭结果对话框，准备导航到分析结果页面，type=$type');
        _navigateToAnalysisResult(type);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFB),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE5E7EB)),
        ),
        child: Row(
          children: [
            Icon(
              type == 'yearly' ? Icons.calendar_today : 
              type == 'monthly' ? Icons.date_range : 
              Icons.today,
              color: const Color(0xFF3B82F6),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(0xFF9CA3AF),
            ),
          ],
        ),
      ),
    );
  }
  
  // 导航到分析结果页面
  void _navigateToAnalysisResult(String type) {
    print('🧭 导航到星座页面，传递参数: constellation=$_constellation, analysisType=$type, isFromCombo=true');
    // 传递类型参数到星座页面，以便显示对应的分析结果
    context.go('/constellation', extra: {
      'constellation': _constellation,
      'analysisType': type,
      'isFromCombo': true,
    });
  }
} 