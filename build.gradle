// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

plugins {
    id 'java-gradle-plugin'
    id 'groovy'
}

group = 'dev.flutter.plugin'
version = '1.0.0'

// Optional: enable stricter validation, to ensure Gradle configuration is correct
tasks.validatePlugins {
    enableStricterValidation = true
}

gradlePlugin {
    plugins {
        // The "flutterPlugin" name isn't used anywhere.
        flutterPlugin {
            id = 'dev.flutter.flutter-gradle-plugin'
            implementationClass = 'com.flutter.gradle.FlutterPlugin'
        }
        // The "flutterAppPluginLoaderPlugin" name isn't used anywhere.
        flutterAppPluginLoaderPlugin {
            id = 'dev.flutter.flutter-plugin-loader'
            implementationClass = 'com.flutter.gradle.FlutterAppPluginLoaderPlugin'
        }
    }
}

tasks.withType(JavaCompile) {
    options.release = 8
}

tasks.test {
    useJUnitPlatform()
}

dependencies {
    // Versions available https://mvnrepository.com/artifact/androidx.annotation/annotation-jvm.
    // Version release notes https://developer.android.com/jetpack/androidx/releases/annotation
    compileOnly 'androidx.annotation:annotation-jvm:1.9.1'
    // When bumping, also update:
    //  * ndkVersion in FlutterExtension in packages/flutter_tools/gradle/src/main/groovy/flutter.groovy
    //  * AGP version in the buildscript block in packages/flutter_tools/gradle/src/main/kotlin_scripts/dependency_version_checker.gradle.kts
    //  * AGP version constants in packages/flutter_tools/lib/src/android/gradle_utils.dart
    compileOnly 'com.android.tools.build:gradle:7.4.2'

    testImplementation 'org.mockito:mockito-core:5.8.0'
}
