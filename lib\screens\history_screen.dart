import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import 'package:provider/provider.dart';
import '../providers/prediction_provider.dart';
import '../widgets/connection_status_widget.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  int _selectedTabIndex = 0;
  
  final List<String> _tabs = ['全部', '八字', '紫微', '六爻', '星座'];
  
  // 模拟历史记录数据
  final List<HistoryItem> _allHistoryItems = [
    // 今天的记录
    HistoryItem(
      type: HistoryType.bazi,
      title: '八字预测',
      description: '1995年8月15日 女 - 木火土金组合，事业线佳',
      time: '10:23',
      date: DateTime.now(),
      route: '/bazi',
    ),
    HistoryItem(
      type: HistoryType.ziwei,
      title: '紫微斗数',
      description: '1995年8月15日 女 - 紫微天府同宫，财运亨通',
      time: '09:47',
      date: DateTime.now(),
      route: '/ziwei',
    ),
    HistoryItem(
      type: HistoryType.ai,
      title: 'AI咨询',
      description: '关于最近工作变动的咨询 - 聊天记录 (12条)',
      time: '08:15',
      date: DateTime.now(),
      route: '/ai_chat',
    ),
    // 昨天的记录
    HistoryItem(
      type: HistoryType.liuyao,
      title: '六爻预测',
      description: '事业发展 - 泽天夬卦，上进有为，事业有成',
      time: '19:32',
      date: DateTime.now().subtract(const Duration(days: 1)),
      route: '/liuyao',
    ),
    HistoryItem(
      type: HistoryType.constellation,
      title: '星座预测',
      description: '狮子座 - 本周运势分析，感情有新发展',
      time: '16:20',
      date: DateTime.now().subtract(const Duration(days: 1)),
      route: '/constellation',
    ),
    HistoryItem(
      type: HistoryType.bazi,
      title: '八字组合',
      description: '八字与紫微整体运势组合 - 2024年运势分析',
      time: '14:05',
      date: DateTime.now().subtract(const Duration(days: 1)),
      route: '/prediction_combo',
    ),
    // 更早的记录
    HistoryItem(
      type: HistoryType.constellation,
      title: '星座组合',
      description: '狮子座与紫微 - 年月日运势分析',
      time: '周一',
      date: DateTime.now().subtract(const Duration(days: 3)),
      route: '/constellation',
    ),
    HistoryItem(
      type: HistoryType.liuyao,
      title: '六爻对比',
      description: '两个工作机会对比 - 乾卦与坤卦分析',
      time: '上周',
      date: DateTime.now().subtract(const Duration(days: 7)),
      route: '/liuyao',
    ),
  ];

  List<HistoryItem> get _filteredHistoryItems {
    if (_selectedTabIndex == 0) return _allHistoryItems;
    
    final filterType = HistoryType.values[_selectedTabIndex - 1];
    return _allHistoryItems.where((item) => item.type == filterType).toList();
  }

  Map<String, List<HistoryItem>> get _groupedHistoryItems {
    final filtered = _filteredHistoryItems;
    final grouped = <String, List<HistoryItem>>{};
    
    for (final item in filtered) {
      final daysDiff = DateTime.now().difference(item.date).inDays;
      String group;
      
      if (daysDiff == 0) {
        group = '今天';
      } else if (daysDiff == 1) {
        group = '昨天';
      } else {
        group = '更早';
      }
      
      grouped.putIfAbsent(group, () => []).add(item);
    }
    
    return grouped;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadHistory();
    });
  }

  Future<void> _loadHistory() async {
    final predictionProvider = Provider.of<PredictionProvider>(context, listen: false);
    await predictionProvider.loadRecords();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // iOS状态栏
              const IOSStatusBar(),
              // 顶部导航栏
              CustomNavigationBar(
                title: '历史记录',
                onBackPressed: () => context.pop(),
              ),
              // 选项卡
              _buildTabBar(),
              // 历史记录列表
              Expanded(
                child: _buildHistoryList(),
              ),
              // 底部导航
              const BottomNavigation(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: _tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = _selectedTabIndex == index;
          
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedTabIndex = index;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFF1F5F9) : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                tab,
                style: TextStyle(
                  color: isSelected ? const Color(0xFF3B82F6) : Colors.grey.shade600,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildHistoryList() {
    final groupedItems = _groupedHistoryItems;
    
    if (groupedItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无历史记录',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 按时间分组显示
        ...groupedItems.entries.map((entry) {
          final groupName = entry.key;
          final items = entry.value;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分组标题
              Padding(
                padding: const EdgeInsets.only(left: 8, top: 16, bottom: 8),
                child: Text(
                  groupName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
              // 分组内的历史记录
              ...items.map((item) => _buildHistoryItem(item)),
            ],
          );
        }).toList(),
      ],
    );
  }

  Widget _buildHistoryItem(HistoryItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border(
          left: BorderSide(
            color: _getTypeColor(item.type),
            width: 4,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // 跳转到对应页面
            if (item.route.isNotEmpty) {
              context.push(item.route);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('页面跳转功能开发中...')),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和时间
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getTypeIcon(item.type),
                          color: _getTypeColor(item.type),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          item.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      item.time,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // 描述
                Text(
                  item.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon(HistoryType type) {
    switch (type) {
      case HistoryType.bazi:
        return FontAwesomeIcons.calendarDays;
      case HistoryType.ziwei:
        return FontAwesomeIcons.star;
      case HistoryType.liuyao:
        return FontAwesomeIcons.yinYang;
      case HistoryType.constellation:
        return FontAwesomeIcons.moon;
      case HistoryType.ai:
        return FontAwesomeIcons.robot;
    }
  }

  Color _getTypeColor(HistoryType type) {
    switch (type) {
      case HistoryType.bazi:
        return const Color(0xFF16A34A); // 绿色
      case HistoryType.ziwei:
        return const Color(0xFF8B5CF6); // 紫色
      case HistoryType.liuyao:
        return const Color(0xFFF59E0B); // 黄色
      case HistoryType.constellation:
        return const Color(0xFF3B82F6); // 蓝色
      case HistoryType.ai:
        return const Color(0xFFEC4899); // 粉色
    }
  }
}

enum HistoryType {
  bazi,
  ziwei,
  liuyao,
  constellation,
  ai,
}

class HistoryItem {
  final HistoryType type;
  final String title;
  final String description;
  final String time;
  final DateTime date;
  final String route;

  HistoryItem({
    required this.type,
    required this.title,
    required this.description,
    required this.time,
    required this.date,
    required this.route,
  });
} 