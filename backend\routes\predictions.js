const express = require('express');
const AuthUtils = require('../utils/auth');
const ValidationUtils = require('../utils/validation');
const PredictionService = require('../services/PredictionService');
const PredictionRecord = require('../models/PredictionRecord');
const User = require('../models/User');
const router = express.Router();

// 八字预测 - 支持游客模式和AI解读
router.post('/bazi', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    console.log('\n🎯 ===== 八字预测路由开始 =====');
    const userId = req.user ? req.user.userId : 'guest';
    const isGuest = !req.user;
    console.log('👤 用户ID:', userId);
    console.log('🎭 游客模式:', isGuest);
    console.log('📥 原始请求数据:', JSON.stringify(req.body, null, 2));
    
    // 验证请求数据
    console.log('🔍 开始验证请求数据...');
    const { error, value } = ValidationUtils.validateBaziPrediction(req.body);
    if (error) {
      console.log('❌ 数据验证失败:', error.details[0].message);
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }
    console.log('✅ 数据验证通过');
    console.log('📋 验证后的数据:', JSON.stringify(value, null, 2));

    const { gender, birthDate, birthTime, inputData } = value;
    
    console.log('🏗️ 构建输入数据完成:');
    console.log('⚥ 性别:', gender);
    console.log('📅 生日:', birthDate);
    console.log('⏰ 时间:', birthTime);
    console.log('📦 额外数据:', inputData);
    
    console.log('\n🚀 调用PredictionService.predictBazi...');
    
    // 执行八字预测 - 现在带AI解读
    const prediction = await PredictionService.predictBazi(gender, birthDate, birthTime, inputData);
    
    console.log('✅ PredictionService调用成功!');
    console.log('🎯 预测结果:', JSON.stringify(prediction, null, 2));
    
    let response;
    
    if (isGuest) {
      // 游客模式：直接返回预测结果，不保存记录
      console.log('🎭 游客模式：跳过保存记录和用户统计');
      response = {
        result: prediction.result
      };
    } else {
      // 登录用户：保存预测记录和更新统计
      console.log('💾 开始保存预测记录...');
      const record = await PredictionRecord.createRecord(
        userId,
        prediction.type,
        prediction.title,
        prediction.description,
        prediction.result
      );
      console.log('✅ 预测记录保存成功, ID:', record.id);
      
      // 更新用户统计
      console.log('📊 更新用户统计...');
      await User.updateUserStats(userId, 'predictionCount');
      console.log('✅ 用户统计更新成功');
      
      response = {
        record: {
          id: record.id,
          type: record.get('type'),
          title: record.get('title'),
          description: record.get('description'),
          createdAt: record.get('createdAt'),
          isFavorite: record.get('isFavorite')
        },
        result: prediction.result
      };
    }
    
    console.log('📤 准备返回响应:', JSON.stringify(response, null, 2));
    console.log('🎉 ===== 八字预测路由完成 =====\n');
    
    res.status(201).json(response);
  } catch (error) {
    console.log('\n❌ ===== 八字预测路由出错 =====');
    console.log('🚨 错误类型:', error.name);
    console.log('📝 错误信息:', error.message);
    console.log('📋 错误堆栈:', error.stack);
    console.log('💥 ===== 八字预测路由结束 =====\n');
    
    console.error('❌ 八字预测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '八字预测失败，请稍后再试'
      }
    });
  }
});

// 六爻预测
router.post('/liuyao', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    console.log('\n🎯 ===== 六爻预测路由开始 =====');
    const userId = req.user ? req.user.userId : 'guest';
    const isGuest = !req.user;
    console.log('👤 用户ID:', userId);
    console.log('🎭 游客模式:', isGuest);
    console.log('📥 原始请求数据:', JSON.stringify(req.body, null, 2));
    
    // 验证请求数据
    console.log('🔍 开始验证请求数据...');
    const { error, value } = ValidationUtils.validateLiuyaoPrediction(req.body);
    if (error) {
      console.log('❌ 数据验证失败:', error.details[0].message);
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }
    console.log('✅ 数据验证通过');
    console.log('📋 验证后的数据:', JSON.stringify(value, null, 2));

    const { question, guaLines, timeInfo, divineTime } = value;
    
    // 构建输入数据
    const inputData = {
      guaLines,
      timeInfo,
      divineTime
    };
    
    console.log('🏗️ 构建输入数据完成:');
    console.log('❓ 问题:', question);
    console.log('📊 卦爻:', guaLines);
    console.log('⏰ 时间信息:', timeInfo);
    console.log('🕐 占卜时间:', divineTime);
    console.log('📦 完整输入数据:', JSON.stringify(inputData, null, 2));
    
    console.log('\n🚀 调用PredictionService.predictLiuyao...');
    
    // 执行六爻预测 - 传入完整的输入数据
    const prediction = await PredictionService.predictLiuyao(question, inputData);
    
    console.log('✅ PredictionService调用成功!');
    console.log('🎯 预测结果:', JSON.stringify(prediction, null, 2));
    
    let response;
    
    if (isGuest) {
      // 游客模式：直接返回预测结果，不保存记录
      console.log('🎭 游客模式：跳过保存记录和用户统计');
      response = {
        result: prediction.result
      };
    } else {
      // 登录用户：保存预测记录和更新统计
      console.log('💾 开始保存预测记录...');
      const record = await PredictionRecord.createRecord(
        userId,
        prediction.type,
        prediction.title,
        prediction.description,
        prediction.result
      );
      console.log('✅ 预测记录保存成功, ID:', record.id);
      
      // 更新用户统计
      console.log('📊 更新用户统计...');
      await User.updateUserStats(userId, 'predictionCount');
      console.log('✅ 用户统计更新成功');
      
      response = {
        record: {
          id: record.id,
          type: record.get('type'),
          title: record.get('title'),
          description: record.get('description'),
          createdAt: record.get('createdAt'),
          isFavorite: record.get('isFavorite')
        },
        result: prediction.result
      };
    }
    
    console.log('📤 准备返回响应:', JSON.stringify(response, null, 2));
    console.log('🎉 ===== 六爻预测路由完成 =====\n');
    
    res.status(201).json(response);
  } catch (error) {
    console.log('\n❌ ===== 六爻预测路由出错 =====');
    console.log('🚨 错误类型:', error.name);
    console.log('📝 错误信息:', error.message);
    console.log('📋 错误堆栈:', error.stack);
    console.log('💥 ===== 六爻预测路由结束 =====\n');
    
    console.error('❌ 六爻预测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '六爻预测失败，请稍后再试'
      }
    });
  }
});

// 紫微斗数预测
router.post('/ziwei', AuthUtils.optionalAuthenticate, async (req, res) => {
  try {
    console.log('\n🔮 ===== 紫微斗数预测路由开始 =====');
    const userId = req.user ? req.user.userId : 'guest';
    const isGuest = !req.user;
    console.log('👤 用户ID:', userId);
    console.log('🎭 游客模式:', isGuest);
    console.log('📥 原始请求数据:', JSON.stringify(req.body, null, 2));
    
    // 验证请求数据
    console.log('🔍 开始验证请求数据...');
    const { error, value } = ValidationUtils.validateZiweiPrediction(req.body);
    if (error) {
      console.log('❌ 数据验证失败:', error.details[0].message);
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }
    console.log('✅ 数据验证通过');
    console.log('📋 验证后的数据:', JSON.stringify(value, null, 2));

    const { gender, birthDate, birthTime } = value;
    
    console.log('🏗️ 构建输入数据完成:');
    console.log('👤 性别:', gender);
    console.log('📅 出生日期:', birthDate);
    console.log('⏰ 出生时间:', birthTime);
    
    console.log('\n🚀 调用PredictionService.predictZiwei...');
    
    // 执行紫微斗数预测
    const prediction = await PredictionService.predictZiwei(gender, birthDate, birthTime);
    
    console.log('✅ PredictionService调用成功!');
    console.log('🎯 预测结果:', JSON.stringify(prediction, null, 2));
    
    let response;
    
    if (isGuest) {
      // 游客模式：直接返回预测结果，不保存记录
      console.log('🎭 游客模式：跳过保存记录和用户统计');
      response = {
        result: prediction.result
      };
    } else {
      // 登录用户：保存预测记录和更新统计
      console.log('💾 开始保存预测记录...');
      const record = await PredictionRecord.createRecord(
        userId,
        prediction.type,
        prediction.title,
        prediction.description,
        prediction.result
      );
      console.log('✅ 预测记录保存成功, ID:', record.id);
      
      // 更新用户统计
      console.log('📊 更新用户统计...');
      await User.updateUserStats(userId, 'predictionCount');
      console.log('✅ 用户统计更新成功');
      
      response = {
        record: {
          id: record.id,
          type: record.get('type'),
          title: record.get('title'),
          description: record.get('description'),
          createdAt: record.get('createdAt'),
          isFavorite: record.get('isFavorite')
        },
        result: prediction.result
      };
    }
    
    console.log('📤 准备返回响应:', JSON.stringify(response, null, 2));
    console.log('🎉 ===== 紫微斗数预测路由完成 =====\n');
    
    res.status(201).json(response);
  } catch (error) {
    console.log('\n❌ ===== 紫微斗数预测路由出错 =====');
    console.log('🚨 错误类型:', error.name);
    console.log('📝 错误信息:', error.message);
    console.log('📋 错误堆栈:', error.stack);
    console.log('💥 ===== 紫微斗数预测路由结束 =====\n');
    
    console.error('❌ 紫微斗数预测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '紫微斗数预测失败，请稍后再试'
      }
    });
  }
});

// 组合预测
router.post('/combo', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 验证请求数据
    const { error, value } = ValidationUtils.validateComboPrediction(req.body);
    if (error) {
      return res.status(400).json(ValidationUtils.formatValidationError(error));
    }

    const { comboType, inputData } = value;
    
    // 执行组合预测
    const prediction = await PredictionService.predictCombo(comboType, inputData);
    
    // 保存预测记录
    const record = await PredictionRecord.createRecord(
      userId,
      prediction.type,
      prediction.title,
      prediction.description,
      prediction.result
    );
    
    // 更新用户统计
    await User.updateUserStats(userId, 'predictionCount');
    
    res.status(201).json({
      record: {
        id: record.id,
        type: record.get('type'),
        title: record.get('title'),
        description: record.get('description'),
        createdAt: record.get('createdAt'),
        isFavorite: record.get('isFavorite')
      },
      result: prediction.result
    });
  } catch (error) {
    console.error('组合预测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '组合预测失败，请稍后再试'
      }
    });
  }
});

// 获取预测记录详情
router.get('/:recordId', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const recordId = req.params.recordId;

    if (!recordId) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '记录ID不能为空'
        }
      });
    }

    // 查找记录
    const query = new AV.Query('PredictionRecord');
    query.equalTo('objectId', recordId);
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    
    const record = await query.first();
    if (!record) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '记录不存在'
        }
      });
    }

    res.json({
      id: record.id,
      type: record.get('type'),
      title: record.get('title'),
      description: record.get('description'),
      result: record.get('result'),
      isFavorite: record.get('isFavorite'),
      createdAt: record.get('createdAt')
    });
  } catch (error) {
    console.error('获取预测记录详情失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取预测记录详情失败'
      }
    });
  }
});

// 重新预测（基于历史记录）
router.post('/:recordId/repredict', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const recordId = req.params.recordId;

    if (!recordId) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '记录ID不能为空'
        }
      });
    }

    // 查找原始记录
    const query = new AV.Query('PredictionRecord');
    query.equalTo('objectId', recordId);
    query.equalTo('user', AV.Object.createWithoutData('_User', userId));
    
    const originalRecord = await query.first();
    if (!originalRecord) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '原始记录不存在'
        }
      });
    }

    const originalResult = originalRecord.get('result');
    const recordType = originalRecord.get('type');
    
    let newPrediction;
    
    // 根据记录类型重新预测
    switch (recordType) {
      case 'bazi':
        // 从原始结果中提取参数（简化处理）
        newPrediction = await PredictionService.predictBazi('male', '1990-01-01', '12:00');
        break;
      case 'liuyao':
        newPrediction = await PredictionService.predictLiuyao('重新询问的问题');
        break;
      case 'ziwei':
        newPrediction = await PredictionService.predictZiwei('male', '1990-01-01', '12:00');
        break;
      default:
        return res.status(400).json({
          error: {
            code: 400,
            message: '不支持的预测类型'
          }
        });
    }

    // 创建新记录
    const newRecord = await PredictionRecord.createRecord(
      userId,
      newPrediction.type,
      newPrediction.title + ' (重新预测)',
      newPrediction.description,
      newPrediction.result
    );
    
    // 更新用户统计
    await User.updateUserStats(userId, 'predictionCount');
    
    res.status(201).json({
      record: {
        id: newRecord.id,
        type: newRecord.get('type'),
        title: newRecord.get('title'),
        description: newRecord.get('description'),
        createdAt: newRecord.get('createdAt'),
        isFavorite: newRecord.get('isFavorite')
      },
      result: newPrediction.result
    });
  } catch (error) {
    console.error('重新预测失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '重新预测失败，请稍后再试'
      }
    });
  }
});

// 获取预测统计信息
router.get('/stats/summary', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 统计各类型预测数量
    const stats = {};
    const types = ['bazi', 'liuyao', 'ziwei', 'combo', 'constellation'];
    
    for (const type of types) {
      const query = new AV.Query('PredictionRecord');
      query.equalTo('user', AV.Object.createWithoutData('_User', userId));
      query.equalTo('type', type);
      
      const count = await query.count();
      stats[type] = count;
    }
    
    // 计算总数
    const totalCount = Object.values(stats).reduce((sum, count) => sum + count, 0);
    
    // 获取最近的预测记录
    const recentQuery = new AV.Query('PredictionRecord');
    recentQuery.equalTo('user', AV.Object.createWithoutData('_User', userId));
    recentQuery.descending('createdAt');
    recentQuery.limit(5);
    
    const recentRecords = await recentQuery.find();
    
    res.json({
      total: totalCount,
      byType: stats,
      recentRecords: recentRecords.map(record => ({
        id: record.id,
        type: record.get('type'),
        title: record.get('title'),
        createdAt: record.get('createdAt')
      }))
    });
  } catch (error) {
    console.error('获取预测统计失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '获取预测统计失败'
      }
    });
  }
});

// 保存六爻预测结果
router.post('/liuyao/:recordId/save', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { recordId } = req.params;
    const { notes, tags } = req.body;
    
    // 验证记录是否存在且属于当前用户
    const record = await PredictionRecord.findOne({
      where: {
        id: recordId,
        userId: userId
      }
    });
    
    if (!record) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '预测记录不存在'
        }
      });
    }
    
    // 更新记录的备注和标签
    await record.update({
      notes: notes || '',
      tags: JSON.stringify(tags || []),
      updatedAt: new Date()
    });
    
    res.json({
      success: true,
      message: '保存成功',
      record: {
        id: record.id,
        notes: record.get('notes'),
        tags: JSON.parse(record.get('tags') || '[]'),
        updatedAt: record.get('updatedAt')
      }
    });
  } catch (error) {
    console.error('保存六爻预测结果失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: '保存失败，请稍后再试'
      }
    });
  }
});

// 基于六爻卦象的AI咨询
router.post('/liuyao/:recordId/ai-consult', AuthUtils.authenticate, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { recordId } = req.params;
    const { question } = req.body;
    
    // 验证问题输入
    if (!question || question.trim().length < 2) {
      return res.status(400).json({
        error: {
          code: 400,
          message: '咨询问题不能为空，至少需要2个字符'
        }
      });
    }
    
    // 查找六爻预测记录
    const record = await PredictionRecord.findOne({
      where: {
        id: recordId,
        userId: userId,
        type: 'liuyao'
      }
    });
    
    if (!record) {
      return res.status(404).json({
        error: {
          code: 404,
          message: '六爻预测记录不存在'
        }
      });
    }
    
    // 获取卦象解析作为上下文
    const resultData = record.get('result');
    const hexagramContext = {
      hexagramName: resultData.hexagramName,
      originalQuestion: record.get('description').split(' - ')[0], // 提取原始问题
      analysis: resultData.analysis,
      lines: resultData.lines,
      timeInfo: resultData.timeInfo
    };
    
    // 调用AI咨询服务
    const consultResult = await PredictionService.consultLiuyaoWithAI(question, hexagramContext);
    
    res.json({
      success: true,
      consultation: {
        question: question,
        answer: consultResult.answer,
        context: {
          hexagramName: hexagramContext.hexagramName,
          originalQuestion: hexagramContext.originalQuestion
        },
        consultTime: new Date().toIso8601String()
      }
    });
  } catch (error) {
    console.error('六爻AI咨询失败:', error);
    res.status(500).json({
      error: {
        code: 500,
        message: 'AI咨询服务暂时不可用，请稍后再试'
      }
    });
  }
});

module.exports = router; 