import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import '../widgets/connection_status_widget.dart';
import '../providers/prediction_provider.dart';

class ZiweiScreen extends StatefulWidget {
  const ZiweiScreen({super.key});

  @override
  State<ZiweiScreen> createState() => _ZiweiScreenState();
}

class _ZiweiScreenState extends State<ZiweiScreen>
    with TickerProviderStateMixin {
  late AnimationController _starAnimationController;
  final _formKey = GlobalKey<FormState>();
  
  bool _isMale = true;
  DateTime _selectedDate = DateTime(1995, 3, 15);
  String _selectedShichen = '子时 (23:00-01:00)';
  bool _showChart = false;
  bool _isLoading = false;
  Map<String, dynamic>? _analysisResult;
  
  // 日期选择相关
  int _selectedYear = 1995;
  int _selectedMonth = 3;
  int _selectedDay = 15;
  bool _showDatePicker = false; // 控制日期选择器显示
  bool _showShichenPicker = false; // 控制时辰选择器显示
  
  // 时辰选择相关
  int _selectedShichenIndex = 0;
  
  // 时辰数据
  final List<Map<String, dynamic>> _shichenData = [
    {'name': '子时', 'time': '23:00-01:00', 'hour': 0},
    {'name': '丑时', 'time': '01:00-03:00', 'hour': 2},
    {'name': '寅时', 'time': '03:00-05:00', 'hour': 4},
    {'name': '卯时', 'time': '05:00-07:00', 'hour': 6},
    {'name': '辰时', 'time': '07:00-09:00', 'hour': 8},
    {'name': '巳时', 'time': '09:00-11:00', 'hour': 10},
    {'name': '午时', 'time': '11:00-13:00', 'hour': 12},
    {'name': '未时', 'time': '13:00-15:00', 'hour': 14},
    {'name': '申时', 'time': '15:00-17:00', 'hour': 16},
    {'name': '酉时', 'time': '17:00-19:00', 'hour': 18},
    {'name': '戌时', 'time': '19:00-21:00', 'hour': 20},
    {'name': '亥时', 'time': '21:00-23:00', 'hour': 22},
  ];

  final List<String> _shichenList = [
    '子时 (23:00-01:00)', '丑时 (01:00-03:00)', '寅时 (03:00-05:00)',
    '卯时 (05:00-07:00)', '辰时 (07:00-09:00)', '巳时 (09:00-11:00)',
    '午时 (11:00-13:00)', '未时 (13:00-15:00)', '申时 (15:00-17:00)',
    '酉时 (17:00-19:00)', '戌时 (19:00-21:00)', '亥时 (21:00-23:00)',
  ];

  @override
  void initState() {
    super.initState();
    _starAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    // 初始化日期选择器的年月日
    _selectedYear = _selectedDate.year;
    _selectedMonth = _selectedDate.month;
    _selectedDay = _selectedDate.day;
    
    // 初始化时辰索引
    _selectedShichenIndex = _getShichenIndexFromName(_selectedShichen);
  }
  
  // 获取指定年月的天数
  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
  
  // 根据时辰名称获取索引
  int _getShichenIndexFromName(String shichenName) {
    for (int i = 0; i < _shichenList.length; i++) {
      if (_shichenList[i] == shichenName) {
        return i;
      }
    }
    return 0; // 默认返回子时
  }
  
  // 更新选择的日期
  void _updateSelectedDate() {
    // 确保日期有效，特别是在切换月份时
    int daysInMonth = _getDaysInMonth(_selectedYear, _selectedMonth);
    int validDay = _selectedDay > daysInMonth ? daysInMonth : _selectedDay;
    
    setState(() {
      _selectedDate = DateTime(_selectedYear, _selectedMonth, validDay);
      _selectedDay = validDay; // 更新日期，确保有效
    });
  }

  @override
  void dispose() {
    _starAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        colors: const [
          Color(0xFFE0E7FF),
          Color(0xFFC7D2FE),
        ],
        child: Column(
          children: [
            const IOSStatusBar(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: const BoxDecoration(
                color: Color(0xFF1E293B),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () => context.go('/'),
                    icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                  ),
                  const Expanded(
                    child: Text(
                      '紫微斗数',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const ConnectionStatusWidget(),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 说明区域
                    _buildInstructionCard(),
                    
                    // 生辰信息输入
                    _buildInputForm(),
                    
                    // 命盘展示（条件显示）
                    if (_showChart) _buildChart(),
                    
                    // 分析结果（条件显示）
                    if (_showChart) _buildAnalysisResult(),
                    
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            const BottomNavigation(currentIndex: 0),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedBuilder(
                animation: _starAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: 1.0 + _starAnimationController.value * 0.2,
                    child: Icon(
                      FontAwesomeIcons.star,
                      color: Color(0xFF8B5CF6),
                      size: 20,
                    ),
                  );
                },
              ),
              const SizedBox(width: 8),
              const Text(
                '紫微斗数命盘',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructionItem('• 请准确填写您的出生信息'),
              _buildInstructionItem('• 出生时间精确到时辰更准确'),
              _buildInstructionItem('• 系统将为您排出专属命盘'),
              _buildInstructionItem('• 提供AI解读的命理分析和建议'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          color: Color(0xFF374151),
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildInputForm() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '生辰信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1F2937),
              ),
            ),
            const SizedBox(height: 20),
            
            // 性别选择
            _buildGenderSelection(),
            
            const SizedBox(height: 20),
            
            // 出生日期
            _buildDateSelection(),
            
            const SizedBox(height: 20),
            
            // 出生时辰
            _buildShichenSelection(),
            
            const SizedBox(height: 24),
            
            // 提交按钮
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '性别',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isMale = true),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: _isMale ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.mars,
                        color: _isMale ? Colors.white : const Color(0xFF374151),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '男',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _isMale ? Colors.white : const Color(0xFF374151),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isMale = false),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: !_isMale ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.venus,
                        color: !_isMale ? Colors.white : const Color(0xFF374151),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '女',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: !_isMale ? Colors.white : const Color(0xFF374151),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '出生日期',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 12),
        
        // 日期展示栏
        GestureDetector(
          onTap: () {
            setState(() {
              _showDatePicker = !_showDatePicker;
              if (_showDatePicker) {
                _showShichenPicker = false; // 关闭时辰选择器
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF8B5CF6), width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.calendarAlt,
                      color: Color(0xFF8B5CF6),
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      DateFormat('yyyy年MM月dd日').format(_selectedDate),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ],
                ),
                Icon(
                  _showDatePicker ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Color(0xFF8B5CF6),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
        
        // 可折叠的日期选择器
        if (_showDatePicker)
          Container(
            margin: const EdgeInsets.only(top: 12),
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF8B5CF6), width: 1),
            ),
            child: Row(
              children: [
                // 年份选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF3E8FF),
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(15),
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            '年',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF8B5CF6),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedYear - 1900
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedYear = 1900 + index;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            DateTime.now().year - 1900 + 1,
                            (index) => Center(
                              child: Text(
                                '${1900 + index}年',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 月份选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        color: const Color(0xFFF3E8FF),
                        child: const Center(
                          child: Text(
                            '月',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF8B5CF6),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedMonth - 1
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedMonth = index + 1;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            12,
                            (index) => Center(
                              child: Text(
                                '${index + 1}月',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 日期选择
                Expanded(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF3E8FF),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(15),
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            '日',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF8B5CF6),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: CupertinoPicker(
                          itemExtent: 40,
                          scrollController: FixedExtentScrollController(
                            initialItem: _selectedDay - 1
                          ),
                          onSelectedItemChanged: (int index) {
                            setState(() {
                              _selectedDay = index + 1;
                              _updateSelectedDate();
                            });
                          },
                          children: List.generate(
                            _getDaysInMonth(_selectedYear, _selectedMonth),
                            (index) => Center(
                              child: Text(
                                '${index + 1}日',
                                style: const TextStyle(
                                  fontSize: 17,
                                  color: Color(0xFF374151),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
        // 选择确认按钮
        if (_showDatePicker)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showDatePicker = false;
                    });
                  },
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFF8B5CF6),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildShichenSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '出生时辰',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 12),
        
        // 时辰展示栏
        GestureDetector(
          onTap: () {
            setState(() {
              _showShichenPicker = !_showShichenPicker;
              if (_showShichenPicker) {
                _showDatePicker = false; // 关闭日期选择器
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF8B5CF6), width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.clock,
                      color: Color(0xFF8B5CF6),
                      size: 18,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _selectedShichen,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ],
                ),
                Icon(
                  _showShichenPicker ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Color(0xFF8B5CF6),
                  size: 24,
                ),
              ],
            ),
          ),
        ),
        
        // 可折叠的时辰选择器
        if (_showShichenPicker)
          Container(
            margin: const EdgeInsets.only(top: 12),
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFF8B5CF6), width: 1),
            ),
            child: CupertinoPicker(
              itemExtent: 40,
              scrollController: FixedExtentScrollController(initialItem: _selectedShichenIndex),
              onSelectedItemChanged: (int index) {
                setState(() {
                  _selectedShichenIndex = index;
                  _selectedShichen = _shichenList[index];
                });
              },
              children: _shichenList.map((item) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Center(
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 18,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
        // 选择确认按钮
        if (_showShichenPicker)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showShichenPicker = false;
                    });
                  },
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFF8B5CF6),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : () async {
          if (_formKey.currentState!.validate()) {
            setState(() {
              _isLoading = true;
              _showChart = false;
              _analysisResult = null;
            });
            
            try {
              // 确保使用最新的选中日期和时辰
              String birthTime = _parseShichenToTime(_selectedShichen);
              print('🔮 前端: 开始紫微斗数分析请求');
              print('📅 生辰信息: ${DateFormat('yyyy-MM-dd').format(_selectedDate)} ${_isMale ? '男' : '女'} $_selectedShichen -> $birthTime');
              
              // 调用真正的AI分析
              final predictionProvider = Provider.of<PredictionProvider>(context, listen: false);
              final result = await predictionProvider.createZiweiRecord(
                birthDate: DateFormat('yyyy-MM-dd').format(_selectedDate),
                gender: _isMale ? 'male' : 'female',
                inputData: {
                  'selectedShichen': _selectedShichen,
                  'birthTime': birthTime,
                },
              );
              
              print('📦 前端收到结果: $result');
              print('📊 结果数据结构检查:');
              print('   - 包含result: ${result.containsKey('result')}');
              
              if (result.containsKey('result')) {
                final resultData = result['result'];
                print('   - result包含palaces: ${resultData.containsKey('palaces')}');
                print('   - result包含analysis: ${resultData.containsKey('analysis')}');
                
                if (resultData.containsKey('palaces')) {
                  final palaces = resultData['palaces'];
                  print('   - palaces是List类型: ${palaces is List}');
                  print('   - palaces长度: ${palaces is List ? palaces.length : 'N/A'}');
                  if (palaces is List && palaces.isNotEmpty) {
                    print('   - 第一个宫位示例: ${palaces[0]}');
                  }
                }
              }
              
              setState(() {
                _analysisResult = result['result'];
                _showChart = true;
                _isLoading = false;
              });
              
              print('✅ 前端: 紫微斗数分析完成，界面已更新');
              print('📊 _analysisResult数据: $_analysisResult');
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('✨ AI解析完成！'),
                  backgroundColor: Color(0xFF8B5CF6),
                ),
              );
              
            } catch (e) {
              print('❌ 前端: 紫微斗数分析失败 - $e');
              setState(() {
                _isLoading = false;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('❌ 分析失败: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF8B5CF6),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading 
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'AI解析中...',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  FontAwesomeIcons.magic,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Text(
                  '排出命盘',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
      ),
    );
  }

  // 解析时辰为具体时间
  String _parseShichenToTime(String shichen) {
    // 从时辰名称中提取时间范围
    final pattern = RegExp(r'\((\d+):00-\d+:00\)');
    final match = pattern.firstMatch(shichen);
    
    if (match != null && match.groupCount >= 1) {
      final startHour = match.group(1);
      return '$startHour:00';
    }
    
    // 如果无法解析，使用默认时间映射
    final timeMap = {
      '子时 (23:00-01:00)': '23:30',
      '丑时 (01:00-03:00)': '02:00',
      '寅时 (03:00-05:00)': '04:00',
      '卯时 (05:00-07:00)': '06:00',
      '辰时 (07:00-09:00)': '08:00',
      '巳时 (09:00-11:00)': '10:00',
      '午时 (11:00-13:00)': '12:00',
      '未时 (13:00-15:00)': '14:00',
      '申时 (15:00-17:00)': '16:00',
      '酉时 (17:00-19:00)': '18:00',
      '戌时 (19:00-21:00)': '20:00',
      '亥时 (21:00-23:00)': '22:00',
    };
    return timeMap[shichen] ?? '12:00';
  }

  Widget _buildChart() {
    // 确保我们使用API返回的数据，而不是预设数据
    final palaces = _analysisResult?['palaces'] as List<dynamic>?;
    
    if (palaces == null || palaces.isEmpty) {
      print('⚠️ 前端: API返回的命盘数据为空，无法显示命盘');
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFFF5F3FF),
              Color(0xFFEDE9FE),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(color: Color(0xFFD8B4FE), width: 1),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  FontAwesomeIcons.starOfDavid,
                  color: Color(0xFF8B5CF6),
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '紫微命盘',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4C1D95),
                    letterSpacing: 1.2,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  FontAwesomeIcons.moon,
                  color: Color(0xFF8B5CF6),
                  size: 18,
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.circleExclamation,
                    color: Color(0xFFEF4444),
                    size: 36,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '命盘数据加载失败',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '请尝试重新生成命盘',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF6B7280),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    
    print('✅ 前端: 成功获取到API返回的命盘数据，共 ${palaces.length} 宫位');
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFF5F3FF),
            Color(0xFFEDE9FE),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: Color(0xFFD8B4FE), width: 1),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.starOfDavid,
                color: Color(0xFF8B5CF6),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '紫微命盘',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF4C1D95),
                  letterSpacing: 1.2,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                FontAwesomeIcons.moon,
                color: Color(0xFF8B5CF6),
                size: 18,
              ),
            ],
          ),
          const SizedBox(height: 24),
          
          // 命盘网格
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFFF3E8FF), Color(0xFFEDE9FE)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF8B5CF6).withOpacity(0.2),
                  blurRadius: 10,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(color: const Color(0xFF8B5CF6), width: 2),
            ),
            padding: EdgeInsets.all(6),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                childAspectRatio: 0.9, // 调整比例，让单元格更高一些
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: 16,
              itemBuilder: (context, index) {
                // 中间位置留空（2、3行的2、3列）
                if (index == 5 || index == 6 || index == 9 || index == 10) {
                  return _buildEmptyCell();
                }
                
                // 调整索引映射到实际宫位
                int palaceIndex;
                if (index < 5) {
                  palaceIndex = index;
                } else if (index < 9) {
                  palaceIndex = index - 2;
                } else if (index < 13) {
                  palaceIndex = index - 4;
                } else {
                  palaceIndex = index - 4;
                }
                
                if (palaceIndex < palaces.length) {
                  final cellData = palaces[palaceIndex];
                  return _buildChartCell(cellData);
                } else {
                  // 如果宫位数据不足，显示空宫
                  return _buildChartCell({
                    'position': '空宫',
                    'stars': [],
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  // 空白单元格
  Widget _buildEmptyCell() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFAF5FF), Color(0xFFF5F3FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFD8B4FE), width: 1),
      ),
      child: Center(
        child: Icon(
          FontAwesomeIcons.yinYang,
          size: 18,
          color: Color(0xFFC4B5FD).withOpacity(0.3),
        ),
      ),
    );
  }

  Widget _buildChartCell(Map<String, dynamic> cellData) {
    // 确保position是String类型，这里应该是宫位名称
    final position = cellData['position']?.toString() ?? cellData['name']?.toString() ?? '空宫';
    
    // 确保stars是字符串列表，这些是星曜名称
    List<String> stars = [];
    if (cellData['stars'] != null) {
      // 将stars列表中的每个元素转换为字符串
      stars = (cellData['stars'] as List<dynamic>).map((star) => star.toString()).toList();
    }
    
    // 检查是否是十二宫之一
    final isTwelvePalace = ['命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫', '迁移宫', '奴仆宫', '官禄宫', '田宅宫', '福德宫', '父母宫'].contains(position);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isTwelvePalace 
            ? [Color(0xFFFEF3C7), Color(0xFFFDE68A)]
            : [Color(0xFFF9FAFB), Color(0xFFF3F4F6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            spreadRadius: 0,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: isTwelvePalace ? const Color(0xFFA78BFA) : const Color(0xFFC4B5FD),
          width: 1.2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 宫位名称
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 3),
              decoration: BoxDecoration(
                color: isTwelvePalace ? Color(0xFF8B5CF6).withOpacity(0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: isTwelvePalace 
                  ? Border.all(color: Color(0xFFA78BFA), width: 0.5)
                  : null,
              ),
              child: Text(
                position,
                style: TextStyle(
                  fontSize: 13, // 增大宫位名称字体
                  fontWeight: FontWeight.bold,
                  color: isTwelvePalace ? const Color(0xFF6D28D9) : const Color(0xFF6B7280),
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 6),
            
            // 星曜列表
            Expanded(
              child: stars.isEmpty
                  ? Center(
                      child: Icon(
                        FontAwesomeIcons.star,
                        size: 12,
                        color: Color(0xFFC4B5FD).withOpacity(0.3),
                      ),
                    )
                  : ListView(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      children: stars.map((star) {
                        // 为不同星曜设置不同颜色
                        Color starColor = Color(0xFF374151);
                        if (star.contains('紫微') || star.contains('天府') || star.contains('廉贞')) {
                          starColor = Color(0xFF8B5CF6); // 紫色
                        } else if (star.contains('太阳') || star.contains('武曲') || star.contains('天相')) {
                          starColor = Color(0xFFEF4444); // 红色
                        } else if (star.contains('太阴') || star.contains('天梁') || star.contains('天同')) {
                          starColor = Color(0xFF3B82F6); // 蓝色
                        }
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            star,
                            style: TextStyle(
                              fontSize: 12, // 增大星曜名称字体
                              fontWeight: star.contains('紫微') ? FontWeight.bold : FontWeight.normal,
                              color: starColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }).toList(),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalysisResult() {
    final analysis = _analysisResult?['analysis'] as Map<String, dynamic>?;
    
    if (analysis == null) {
      print('⚠️ 前端: 分析结果为空，不显示分析区域');
      return Container();
    }

    print('📖 前端: 显示AI分析结果，包含 ${analysis.keys.length} 个模块');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFF3E8FF),
            const Color(0xFFE9D5FF),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF8B5CF6), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.scroll,
                color: Color(0xFF8B5CF6),
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'AI命盘解析',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          _buildAnalysisSection('命宫解析', analysis['life'] ?? '命宫分析暂时无法显示'),
          _buildAnalysisSection('财运分析', analysis['wealth'] ?? '财运分析暂时无法显示'),
          _buildAnalysisSection('事业运势', analysis['career'] ?? '事业分析暂时无法显示'),
          _buildAnalysisSection('情感婚姻', analysis['relationships'] ?? '感情分析暂时无法显示'),
          _buildAnalysisSection('健康建议', analysis['health'] ?? '健康建议暂时无法显示'),
        ],
      ),
    );
  }

  Widget _buildAnalysisSection(String title, String content) {
    IconData sectionIcon;
    Color iconColor;
    
    // 根据章节设置不同图标
    switch (title) {
      case '命宫解析':
        sectionIcon = FontAwesomeIcons.user;
        iconColor = Color(0xFF8B5CF6);
        break;
      case '财运分析':
        sectionIcon = FontAwesomeIcons.coins;
        iconColor = Color(0xFFF59E0B);
        break;
      case '事业运势':
        sectionIcon = FontAwesomeIcons.briefcase;
        iconColor = Color(0xFF3B82F6);
        break;
      case '情感婚姻':
        sectionIcon = FontAwesomeIcons.heartPulse;
        iconColor = Color(0xFFEF4444);
        break;
      case '健康建议':
        sectionIcon = FontAwesomeIcons.stethoscope;
        iconColor = Color(0xFF10B981);
        break;
      default:
        sectionIcon = FontAwesomeIcons.star;
        iconColor = Color(0xFF8B5CF6);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: iconColor.withOpacity(0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                sectionIcon,
                color: iconColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          const Divider(
            color: Color(0xFFE5E7EB),
            thickness: 1,
            height: 16,
          ),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF374151),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
} 