const AV = require('leanengine');

// 聊天消息类
const ChatMessage = AV.Object.extend('ChatMessage');

// 消息类型枚举
ChatMessage.TYPES = {
  USER: 'user',
  AI: 'ai'
};

// 创建聊天消息
ChatMessage.createMessage = async (userId, type, content) => {
  const message = new ChatMessage();
  message.set('user', AV.Object.createWithoutData('_User', userId));
  message.set('type', type);
  message.set('content', content);
  
  return await message.save();
};

// 获取用户的聊天历史
ChatMessage.getChatHistory = async (userId, limit = 50) => {
  const query = new AV.Query(ChatMessage);
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  query.ascending('createdAt');
  query.limit(limit);
  
  const messages = await query.find();
  
  return messages.map(message => ({
    id: message.id,
    type: message.get('type'),
    content: message.get('content'),
    timestamp: message.get('createdAt')
  }));
};

// 清空用户聊天历史
ChatMessage.clearHistory = async (userId) => {
  const query = new AV.Query(ChatMessage);
  query.equalTo('user', AV.Object.createWithoutData('_User', userId));
  
  const messages = await query.find();
  return await AV.Object.destroyAll(messages);
};

module.exports = ChatMessage; 