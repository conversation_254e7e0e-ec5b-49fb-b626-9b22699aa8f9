# 银发-满天神佛 启动脚本使用说明

## 📋 脚本列表

本项目提供了以下启动脚本，帮助您快速启动开发环境：

### 🚀 主要脚本

| 脚本名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `start_all.bat` | 一键启动所有服务 | **推荐使用** - 开发时快速启动 |
| `start_backend.bat` | 仅启动后端服务 | 单独调试后端API |
| `start_frontend.bat` | 仅启动前端应用 | 单独调试前端界面 |
| `stop_all.bat` | 停止所有服务 | 结束开发时清理进程 |

## 🛠️ 使用方法

### 1. 一键启动（推荐）

```batch
# 双击运行或命令行执行
start_all.bat
```

**启动流程：**
1. 检查项目结构
2. 启动后端服务（端口：3001）
3. 启动前端应用（端口：8080）
4. 自动在Chrome浏览器中打开应用

### 2. 分别启动

```batch
# 仅启动后端
start_backend.bat

# 仅启动前端
start_frontend.bat
```

### 3. 停止服务

```batch
# 停止所有服务
stop_all.bat
```

## 📊 服务信息

### 后端服务
- **端口**: 3001
- **访问地址**: http://localhost:3001
- **健康检查**: http://localhost:3001/health
- **API文档**: http://localhost:3001/api/v1/

### 前端应用
- **端口**: 8080
- **访问地址**: http://localhost:8080
- **平台**: Web (Chrome)
- **模式**: Debug

## ⚠️ 注意事项

### 系统要求
- Windows 10/11
- Node.js 18+
- Flutter SDK 3.0+
- Chrome浏览器

### 环境配置
1. **Node.js**: 确保已安装并配置环境变量
2. **Flutter**: 确保已安装并配置环境变量
3. **Chrome**: 确保已安装Chrome浏览器

### 端口占用
- 如果端口被占用，请停止相关服务或修改脚本中的端口配置
- 后端端口：3001
- 前端端口：8080

## 🔧 故障排除

### 常见问题

#### 1. 后端启动失败
**症状**: 后端服务无法启动
**解决方案**:
- 检查Node.js是否正确安装
- 运行 `npm install` 安装依赖
- 检查3001端口是否被占用

#### 2. 前端启动失败
**症状**: Flutter应用无法启动
**解决方案**:
- 检查Flutter是否正确安装
- 运行 `flutter doctor` 检查环境
- 运行 `flutter pub get` 获取依赖

#### 3. 浏览器未自动打开
**症状**: 前端启动成功但浏览器未打开
**解决方案**:
- 手动打开Chrome浏览器
- 访问 http://localhost:8080
- 检查Chrome是否为默认浏览器

#### 4. 服务无法停止
**症状**: 运行stop_all.bat后服务仍在运行
**解决方案**:
- 手动关闭相关命令行窗口
- 在任务管理器中结束node.exe和flutter.exe进程
- 重启计算机

### 调试模式

如需调试，可以：
1. 分别启动后端和前端服务
2. 查看各自的日志输出
3. 检查控制台错误信息

## 📝 开发建议

### 开发流程
1. 使用 `start_all.bat` 启动开发环境
2. 进行代码修改
3. 热重载会自动生效
4. 完成开发后使用 `stop_all.bat` 停止服务

### 性能优化
- 首次启动可能较慢，需要下载依赖
- 后续启动会更快
- 建议使用SSD硬盘以提升启动速度

## 🆘 技术支持

如果遇到问题：
1. 检查本文档的故障排除部分
2. 查看命令行输出的错误信息
3. 确保所有环境依赖都已正确安装
4. 检查防火墙设置是否阻止了端口访问

---

**版本**: 1.0.0  
**更新日期**: 2024-01-01  
**维护者**: 银发-满天神佛开发团队 