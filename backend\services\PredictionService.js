const moment = require('moment');

class PredictionService {
  // 天干地支数据
  static TIANGAN = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
  static DIZHI = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
  
  // 五行属性
  static WUXING = {
    '甲': '木', '乙': '木', '丙': '火', '丁': '火', '戊': '土', '己': '土',
    '庚': '金', '辛': '金', '壬': '水', '癸': '水',
    '子': '水', '丑': '土', '寅': '木', '卯': '木', '辰': '土', '巳': '火',
    '午': '火', '未': '土', '申': '金', '酉': '金', '戌': '土', '亥': '水'
  };

  // 八字预测 - 集成AI解读
  static async predictBazi(gender, birthDate, birthTime, inputData = null) {
    console.log('\n🔮 ===== 八字预测AI解析开始 =====');
    console.log('⚥ 性别:', gender);
    console.log('📅 生日:', birthDate);
    console.log('⏰ 时间:', birthTime);
    console.log('📦 输入数据:', inputData);
    
    const birth = moment(birthDate + ' ' + (birthTime || '12:00'), 'YYYY-MM-DD HH:mm');
    
    // 简化的八字排盘算法
    const bazi = this.calculateBazi(birth);
    console.log('🧮 八字排盘结果:', bazi);
    
    // 五行统计
    const wuxingCount = this.calculateWuxing(bazi);
    console.log('🌊 五行统计:', wuxingCount);
    
    // 尝试使用AI进行解读
    let aiAnalysis = '';
    try {
      console.log('🤖 开始AI八字解读...');
      aiAnalysis = await this.getAIBaziAnalysis(bazi, wuxingCount, gender, birthDate, birthTime);
      console.log('✅ AI八字解读成功');
      console.log('📖 AI解读内容:', aiAnalysis.substring(0, 200) + '...');
    } catch (error) {
      console.log('❌ AI八字解读失败:', error.message);
      console.log('🔄 回退到传统解读...');
      aiAnalysis = this.generateBaziAnalysis(bazi, wuxingCount, gender);
      console.log('📚 传统解读结果:', aiAnalysis);
    }
    
    const description = `${birthDate} ${gender === 'male' ? '男' : '女'} - ${this.formatBazi(bazi)}`;
    
    console.log('🎉 ===== 八字预测AI解析完成 =====\n');
    
    return {
      type: 'bazi',
      title: '八字预测',
      description,
      result: {
        yearColumn: bazi.year,
        monthColumn: bazi.month,
        dayColumn: bazi.day,
        hourColumn: bazi.hour,
        wuxingCount,
        analysis: aiAnalysis,
        aiAnalysis: aiAnalysis,
        bazi: bazi,
        gender: gender,
        birthDate: birthDate,
        birthTime: birthTime || '12:00'
      }
    };
  }

  // 六爻预测 - 升级版，集成火山方舟AI
  static async predictLiuyao(question, inputData = null) {
    const AIService = require('./AIService');
    let lines, hexagram;
    
    // 如果提供了输入数据（前端摇卦结果），使用前端数据
    if (inputData && inputData.guaLines) {
      lines = inputData.guaLines.map((lineType, index) => ({
        type: lineType === '阳' ? 'yangLine' : 'yinLine',
        isChanging: Math.random() > 0.8, // 20%概率变爻
        position: index + 1
      }));
      
      // 获取时间信息用于卦象解析
      const timeInfo = inputData.timeInfo || {};
      const divineTime = inputData.divineTime || new Date().toIso8601String();
      
      // 调用AIService进行卦象解析
      try {
        console.log('\n🔮 ===== 六爻AI解析开始 =====');
        console.log('❓ 用户问题:', question);
        console.log('📊 卦爻:', lines);
        console.log('⏰ 时间信息:', timeInfo);
        console.log('🕐 占卜时间:', divineTime);
        
        hexagram = await this.getAILiuyaoAnalysisWithService(question, lines, timeInfo, divineTime);
        
        console.log('✅ AI六爻解析成功');
        console.log('🎯 解析结果:', hexagram);
        console.log('🎉 ===== 六爻AI解析完成 =====\n');
      } catch (error) {
        console.log('\n❌ ===== 六爻AI解析失败 =====');
        console.log('🚨 错误信息:', error.message);
        console.log('📋 错误详情:', error);
        console.log('🔄 回退到传统解析...');
        console.log('💥 ===== 六爻AI解析结束 =====\n');
        
        console.error('❌ AI六爻解析失败:', error);
        // 回退到传统解析
        hexagram = this.interpretLiuyaoHexagram(lines);
        
        console.log('📚 传统解析结果:', hexagram);
      }
    } else {
      // 兼容旧版本：生成六爻卦象
      lines = this.generateLiuyaoLines();
      hexagram = this.interpretLiuyaoHexagram(lines);
    }
    
    const description = `${question} - ${hexagram.name}`;
    
    return {
      type: 'liuyao',
      title: '六爻预测',
      description,
      result: {
        hexagramName: hexagram.name,
        changingHexagramName: hexagram.changingName || hexagram.name,
        lines,
        analysis: hexagram.analysis,
        aiAnalysis: hexagram.aiAnalysis || null,
        timeInfo: inputData?.timeInfo || null,
        divineTime: inputData?.divineTime || null
      }
    };
  }

  // 紫微斗数预测 - 现在接入真正的AI模型
  static async predictZiwei(gender, birthDate, birthTime) {
    console.log('\n🔮 ===== 紫微斗数预测开始 =====');
    console.log(`📋 输入参数: gender=${gender}, birthDate=${birthDate}, birthTime=${birthTime}`);
    
    const birth = moment(birthDate + ' ' + birthTime, 'YYYY-MM-DD HH:mm');
    console.log(`⏰ 解析后的时间: ${birth.format('YYYY-MM-DD HH:mm')}`);
    
    // 计算紫微斗数命盘
    const palaces = this.calculateZiweiPalaces(birth, gender);
    console.log(`🏰 命盘计算完成，共 ${palaces.length} 个宫位`);
    
    // 调用AI进行专业分析
    console.log('🤖 开始调用AI进行紫微分析...');
    const aiAnalysis = await this.getAIZiweiAnalysis(palaces, gender, birthDate, birthTime);
    console.log('✅ AI紫微分析完成');
    
    const description = `${birthDate} ${gender === 'male' ? '男' : '女'} - 紫微斗数AI解析`;
    
    const result = {
      type: 'ziwei',
      title: '紫微斗数',
      description,
      result: {
        palaces,
        analysis: aiAnalysis
      }
    };
    
    console.log('🎉 ===== 紫微斗数预测完成 =====\n');
    return result;
  }

  // 组合预测
  static async predictCombo(comboType, inputData) {
    let result = {
      type: 'combo',
      title: this.getComboTitle(comboType),
      description: this.getComboDescription(comboType, inputData),
      result: {
        summary: '',
        comparison: {
          yearly: [],
          monthly: [],
          daily: []
        },
        advice: ''
      }
    };

    if (comboType === 'bazi_ziwei') {
      const baziResult = await this.predictBazi(inputData.gender, inputData.birthDate, inputData.birthTime);
      const ziweiResult = await this.predictZiwei(inputData.gender, inputData.birthDate, inputData.birthTime);
      
      result.result = this.combineBaziZiwei(baziResult.result, ziweiResult.result);
    } else if (comboType === 'constellation_ziwei') {
      const constellation = this.getConstellationByBirthDate(inputData.birthDate);
      const ziweiResult = await this.predictZiwei(inputData.gender, inputData.birthDate, inputData.birthTime);
      
      result.result = this.combineConstellationZiwei(constellation, ziweiResult.result);
    }

    return result;
  }

  // 计算八字
  static calculateBazi(birth) {
    // 简化算法，实际应用中需要更复杂的干支计算
    const year = birth.year();
    const month = birth.month() + 1;
    const day = birth.date();
    const hour = birth.hour();
    
    return {
      year: this.TIANGAN[year % 10] + this.DIZHI[year % 12],
      month: this.TIANGAN[month % 10] + this.DIZHI[month % 12],
      day: this.TIANGAN[day % 10] + this.DIZHI[day % 12],
      hour: this.TIANGAN[Math.floor(hour / 2) % 10] + this.DIZHI[Math.floor(hour / 2) % 12]
    };
  }

  // 计算五行
  static calculateWuxing(bazi) {
    const wuxing = { jin: 0, mu: 0, shui: 0, huo: 0, tu: 0 };
    
    Object.values(bazi).forEach(column => {
      for (let char of column) {
        const element = this.WUXING[char];
        if (element) {
          switch (element) {
            case '金': wuxing.jin++; break;
            case '木': wuxing.mu++; break;
            case '水': wuxing.shui++; break;
            case '火': wuxing.huo++; break;
            case '土': wuxing.tu++; break;
          }
        }
      }
    });
    
    return wuxing;
  }

  // 生成八字分析
  static generateBaziAnalysis(bazi, wuxingCount, gender) {
    const dominant = Object.keys(wuxingCount).reduce((a, b) => 
      wuxingCount[a] > wuxingCount[b] ? a : b
    );
    
    const analyses = {
      jin: '金性格坚毅，做事果断，具有领导能力',
      mu: '木性格温和，有创造力，善于成长发展',
      shui: '水性格聪明，适应能力强，善于变通',
      huo: '火性格热情，积极向上，具有感染力',
      tu: '土性格稳重，踏实可靠，善于积累'
    };
    
    return `此命五行以${dominant}为主，${analyses[dominant]}。${gender === 'male' ? '男' : '女'}命格局整体平衡，运势稳定，适合从事相关行业发展。`;
  }

  // 使用AIService进行八字分析
  static async getAIBaziAnalysis(bazi, wuxingCount, gender, birthDate, birthTime) {
    const AIService = require('./AIService');
    const aiService = new AIService();
    
    try {
      console.log('\n🤖 ===== AIService八字分析开始 =====');
      
      // 构建八字描述
      const baziDesc = `年柱: ${bazi.year}, 月柱: ${bazi.month}, 日柱: ${bazi.day}, 时柱: ${bazi.hour}`;
      
      // 构建五行描述
      const wuxingDesc = `金: ${wuxingCount.jin}个, 木: ${wuxingCount.mu}个, 水: ${wuxingCount.shui}个, 火: ${wuxingCount.huo}个, 土: ${wuxingCount.tu}个`;
      
      // 构建详细的分析请求
      const analysisPrompt = `请作为专业的八字命理师，为以下八字进行详细解读：

【基本信息】
性别：${gender === 'male' ? '男' : '女'}
出生日期：${birthDate}
出生时间：${birthTime || '12:00'}

【八字排盘】
${baziDesc}

【五行分布】
${wuxingDesc}

请从以下几个方面进行专业分析：
1. 五行特征分析 - 分析五行的强弱和缺失，解读性格特点
2. 性格特征分析 - 根据八字组合分析性格倾向和行为特点
3. 事业运势分析 - 适合的职业方向和事业发展建议
4. 财运分析 - 财富积累方式和理财建议
5. 感情运势 - 婚姻感情的特点和建议
6. 健康建议 - 需要注意的健康问题和养生建议
7. 人生建议 - 综合的人生发展建议和改运方法

请用专业的命理术语，语言要详细而有条理，给出实用的建议。字数在800-1200字之间。`;
      
      console.log('📝 发送AI分析请求...');
      console.log('📋 请求内容:', analysisPrompt.substring(0, 200) + '...');
      
      const aiResponse = await aiService.sendMessage(analysisPrompt);
      
      console.log('✅ AI八字分析完成');
      console.log('📖 AI响应长度:', aiResponse.length);
      console.log('🎉 ===== AIService八字分析结束 =====\n');
      
      return aiResponse;
      
    } catch (error) {
      console.log('\n❌ ===== AIService八字分析失败 =====');
      console.log('🚨 错误信息:', error.message);
      console.log('💥 ===== AIService八字分析结束 =====\n');
      
      console.error('❌ AI八字分析失败:', error);
      throw error;
    }
  }

  // 生成六爻卦象
  static generateLiuyaoLines() {
    const lines = [];
    for (let i = 0; i < 6; i++) {
      const isYang = Math.random() > 0.5;
      const isChanging = Math.random() > 0.8; // 20%概率变爻
      lines.push({
        type: isYang ? 'yangLine' : 'yinLine',
        isChanging
      });
    }
    return lines;
  }

  // 调用火山方舟AI进行六爻卦象分析
  // 使用AIService进行六爻分析
  static async getAILiuyaoAnalysisWithService(question, lines, timeInfo, divineTime) {
    const AIService = require('./AIService');
    const aiService = new AIService();
    
    try {
      console.log('\n🤖 ===== AIService六爻分析开始 =====');
      
      // 构建卦象描述
      const guaLinesDesc = lines.map((line, index) => 
        `第${index + 1}爻: ${line.type === 'yangLine' ? '阳爻(—)' : '阴爻(- -)'}${line.isChanging ? ' [变爻]' : ''}`
      ).join('\n');
      
      // 格式化时间信息
      const timeDesc = `占卜时间: ${divineTime}\n年: ${timeInfo.year}年, 月: ${timeInfo.month}月, 日: ${timeInfo.day}日, 时: ${timeInfo.hour}时${timeInfo.minute}分\n星期: ${this.getWeekdayName(timeInfo.weekday)}`;
      
      // 构建AI请求提示词
      const prompt = `你是一位专业的六爻占卜大师，请根据以下信息进行详细的卦象分析：

问题: ${question}

卦象信息:
${guaLinesDesc}

时间信息:
${timeDesc}

请从以下几个方面进行分析：
1. 卦象名称和基本含义
2. 针对提问的具体解答
3. 事情发展的时间预测
4. 具体的行动建议
5. 需要注意的事项

请用专业而易懂的语言回答，字数控制在300-500字之间。`;

      console.log('📝 构建提示词完成:');
      console.log('📊 卦象描述:', guaLinesDesc);
      console.log('⏰ 时间描述:', timeDesc);
      console.log('📄 完整提示词:', prompt);
      console.log('\n🚀 开始调用AIService...');

      // 使用AIService发送消息
      const aiAnalysis = await aiService.sendMessage(prompt, []);
      
      console.log('\n✅ AIService调用成功!');
      console.log('📦 AI分析结果:', aiAnalysis);
      
      // 解析AI返回的内容，提取卦象名称
      const hexagramNameMatch = aiAnalysis.match(/卦象名称[：:]\s*([^\n\r，。]+)/i);
      const hexagramName = hexagramNameMatch ? hexagramNameMatch[1].trim() : this.extractHexagramFromLines(lines);
      
      console.log('🎯 提取卦象名称:', hexagramName);
      
      const result = {
        name: hexagramName,
        changingName: hexagramName, // AI分析版本暂时使用同一个名称
        analysis: aiAnalysis,
        aiAnalysis: true
      };
      
      console.log('🎉 最终结果:', result);
      console.log('🎊 ===== AIService六爻分析完成 =====\n');
      
      return result;
      
    } catch (error) {
      console.log('\n❌ ===== AIService六爻分析出错 =====');
      console.log('🚨 错误类型:', error.name);
      console.log('📝 错误信息:', error.message);
      console.log('📋 错误堆栈:', error.stack);
      console.log('💥 ===== AIService六爻分析结束 =====\n');
      
      console.error('❌ AIService六爻分析失败:', error.message);
      throw error; // 重新抛出错误，让调用方处理
    }
  }

  // 根据爻线提取传统卦象名称（备用方法）
  static extractHexagramFromLines(lines) {
    // 简化的卦象判断逻辑
    const yangCount = lines.filter(line => line.type === 'yangLine').length;
    const hexagrams = ['坤卦', '剥卦', '比卦', '观卦', '临卦', '损卦', '否卦', '乾卦'];
    return hexagrams[yangCount] || '未知卦象';
  }

  // 获取星期名称
  static getWeekdayName(weekday) {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return `星期${weekdays[weekday % 7]}`;
  }

  // 解释六爻卦象（传统方法，作为备用）
  static interpretLiuyaoHexagram(lines) {
    const hexagrams = [
      { name: '泽天夬', changingName: '乾为天', analysis: '此卦象预示着突破困境，事业有成，宜积极行动。' },
      { name: '天泽履', changingName: '兑为泽', analysis: '此卦象预示着履险如夷，谨慎行事，可化险为夷。' },
      { name: '雷天大壮', changingName: '震为雷', analysis: '此卦象预示着实力雄厚，时机成熟，可大展宏图。' },
      { name: '火天大有', changingName: '离为火', analysis: '此卦象预示着财运亨通，事业成功，前程似锦。' }
    ];
    
    return hexagrams[Math.floor(Math.random() * hexagrams.length)];
  }

  // 计算紫微斗数宫位
  static calculateZiweiPalaces(birth, gender) {
    const palaces = [
      { name: '命宫', stars: ['紫微', '天府'], position: 0 },
      { name: '兄弟宫', stars: ['天机'], position: 1 },
      { name: '夫妻宫', stars: ['太阳'], position: 2 },
      { name: '子女宫', stars: ['武曲'], position: 3 },
      { name: '财帛宫', stars: ['天同'], position: 4 },
      { name: '疾厄宫', stars: ['廉贞'], position: 5 },
      { name: '迁移宫', stars: ['天相'], position: 6 },
      { name: '奴仆宫', stars: ['七杀'], position: 7 },
      { name: '官禄宫', stars: ['破军'], position: 8 },
      { name: '田宅宫', stars: ['贪狼'], position: 9 },
      { name: '福德宫', stars: ['巨门'], position: 10 },
      { name: '父母宫', stars: ['天梁'], position: 11 }
    ];
    
    return palaces;
  }

  // 使用AIService进行紫微斗数分析
  static async getAIZiweiAnalysis(palaces, gender, birthDate, birthTime) {
    const AIService = require('./AIService');
    const aiService = new AIService();
    
    try {
      console.log('\n🤖 ===== AIService紫微斗数分析开始 =====');
      
      // 构建命盘描述
      const palaceDesc = palaces.map(palace => 
        `${palace.name}: ${palace.stars.join('、')}`
      ).join('\n');
      
      console.log(`📋 命盘描述构建完成，包含 ${palaces.length} 个宫位`);
      
      // 构建详细的分析请求
      const analysisPrompt = `请作为专业的紫微斗数命理师，为以下命盘进行详细解读：

【基本信息】
性别：${gender === 'male' ? '男' : '女'}
出生日期：${birthDate}
出生时间：${birthTime || '12:00'}

【紫微命盘】
${palaceDesc}

请严格按照以下五个方面进行专业分析，每个部分要详细而实用：

1. 【命宫解析】- 分析命宫主星的特质、性格特点、天赋才能和人生格局

2. 【财运分析】- 分析财帛宫的配置，财富获得方式、理财建议、投资方向

3. 【事业运势】- 分析官禄宫的星情，适合的职业类型、事业发展方向、升迁机会

4. 【情感婚姻】- 分析夫妻宫的配置，感情特点、婚姻状况、配偶特征

5. 【健康建议】- 分析疾厄宫的星情，需要注意的健康问题、养生方法

请注意：
- 每个部分用200-300字详细分析
- 语言要专业但通俗易懂
- 给出具体实用的建议
- 不要有AI提醒话术，直接给出分析内容
- 用温和专业的语气

请严格按照以上五个标题分段回答，不要额外的开头结尾。`;
      
      console.log('📝 发送AI分析请求...');
      console.log('📋 请求内容长度:', analysisPrompt.length);
      
      const aiResponse = await aiService.sendMessage(analysisPrompt);
      
      console.log('✅ AI紫微分析完成');
      console.log('📖 AI响应长度:', aiResponse.length);
      console.log('🎉 ===== AIService紫微分析结束 =====\n');
      
      // 解析AI返回的分析结果
      return this.parseZiweiAIResponse(aiResponse);
      
    } catch (error) {
      console.log('\n❌ ===== AIService紫微分析失败 =====');
      console.log('🚨 错误信息:', error.message);
      console.log('💥 ===== AIService紫微分析结束 =====\n');
      
      console.error('❌ AI紫微分析失败:', error);
      
      // 返回备用分析结果
      return this.getFallbackZiweiAnalysis(palaces, gender);
    }
  }

  // 解析AI返回的紫微分析结果
  static parseZiweiAIResponse(aiResponse) {
    try {
      console.log('\n📄 ===== 开始解析AI响应 =====');
      console.log('📋 原始响应长度:', aiResponse.length);
      
      // 尝试按标题分割AI响应
      const sections = {};
      
      // 定义匹配模式
      const patterns = {
        life: /【命宫解析】[\s\S]*?(?=【|$)/,
        wealth: /【财运分析】[\s\S]*?(?=【|$)/,
        career: /【事业运势】[\s\S]*?(?=【|$)/,
        relationships: /【情感婚姻】[\s\S]*?(?=【|$)/,
        health: /【健康建议】[\s\S]*?(?=【|$)/
      };
      
      console.log('🔍 尝试标准模式解析...');
      for (const [key, pattern] of Object.entries(patterns)) {
        const match = aiResponse.match(pattern);
        if (match) {
          // 移除标题，只保留内容
          sections[key] = match[0].replace(/【.*?】\s*-?\s*/, '').trim();
          console.log(`✅ ${key} 解析成功，长度: ${sections[key].length}`);
        } else {
          console.log(`❌ ${key} 标准模式解析失败`);
        }
      }
      
      // 如果解析失败，尝试其他分割方式
      if (Object.keys(sections).length < 3) {
        console.log('📄 标准解析失败，尝试数字分割...');
        
        const numberPatterns = {
          life: /1\.?\s*【?命宫解析】?[\s\S]*?(?=2\.|【财运|$)/,
          wealth: /2\.?\s*【?财运分析】?[\s\S]*?(?=3\.|【事业|$)/,
          career: /3\.?\s*【?事业运势】?[\s\S]*?(?=4\.|【情感|【感情|$)/,
          relationships: /4\.?\s*【?(?:情感婚姻|感情婚姻|婚姻感情)】?[\s\S]*?(?=5\.|【健康|$)/,
          health: /5\.?\s*【?健康建议】?[\s\S]*?$/
        };
        
        for (const [key, pattern] of Object.entries(numberPatterns)) {
          const match = aiResponse.match(pattern);
          if (match) {
            sections[key] = match[0].replace(/\d+\.?\s*【?.*?】?\s*-?\s*/, '').trim();
            console.log(`✅ ${key} 数字模式解析成功，长度: ${sections[key].length}`);
          }
        }
      }
      
      // 如果还是解析失败，平均分割
      if (Object.keys(sections).length < 3) {
        console.log('📄 数字解析失败，使用平均分割...');
        const lines = aiResponse.split('\n').filter(line => line.trim());
        const sectionSize = Math.ceil(lines.length / 5);
        
        const keys = ['life', 'wealth', 'career', 'relationships', 'health'];
        keys.forEach((key, index) => {
          const start = index * sectionSize;
          const end = start + sectionSize;
          sections[key] = lines.slice(start, end).join('\n').trim();
          console.log(`✅ ${key} 平均分割完成，长度: ${sections[key].length}`);
        });
      }
      
      console.log('📋 AI响应解析结果:', Object.keys(sections));
      console.log('🎉 ===== AI响应解析完成 =====\n');
      return sections;
      
    } catch (error) {
      console.error('❌ AI响应解析失败:', error);
      return this.getFallbackZiweiAnalysis(null, 'unknown');
    }
  }

  // 备用紫微分析结果
  static getFallbackZiweiAnalysis(palaces, gender) {
    console.log('🔄 使用备用紫微分析结果');
    return {
      life: '命宫星象显示您具有领导才能和高贵气质，一生多贵人相助。性格坚毅果断，有独特的见解和远大的理想。建议充分发挥自身优势，在适当时机展现领导能力。',
      wealth: '财帛宫配置良好，财运稳定向上。适合通过正当途径积累财富，理财方面宜稳健保守。建议多关注长期投资，避免投机冒险，可考虑房地产或稳健型基金。',
      career: '官禄宫星情显示事业发展潜力巨大，适合在管理、教育或专业技术领域发展。建议持续学习提升自己，把握升迁机会，发挥个人专长服务社会。',
      relationships: '夫妻宫显示感情运势良好，配偶多才多艺且相互扶持。感情发展需要耐心和包容，建议在沟通中多表达关爱，共同创造美好的婚姻生活。',
      health: '疾厄宫平稳，整体健康状况良好。需注意工作压力对身体的影响，建议保持规律作息，适当运动，定期体检，保持身心平衡。'
    };
  }

  // 生成紫微斗数分析（保留原方法作为备用）
  static generateZiweiAnalysis(palaces, gender) {
    return {
      life: '命宫有紫微天府，为帝王之星，主贵，一生多贵人相助。',
      wealth: '财帛宫有天同星，财运平稳，适合稳健投资。',
      career: '官禄宫有破军星，事业多变化，宜选择创新行业。',
      relationships: '夫妻宫有太阳星，感情热烈，配偶多才多艺。',
      health: '疾厄宫有廉贞星，注意心血管方面的健康。'
    };
  }

  // 组合预测相关方法
  static getComboTitle(comboType) {
    const titles = {
      'bazi_ziwei': '八字+紫微组合预测',
      'constellation_ziwei': '星座+紫微组合预测'
    };
    return titles[comboType] || '组合预测';
  }

  static getComboDescription(comboType, inputData) {
    const { gender, birthDate, birthTime } = inputData;
    const genderText = gender === 'male' ? '男' : '女';
    return `${birthDate} ${genderText} - ${this.getComboTitle(comboType)}`;
  }

  static combineBaziZiwei(baziResult, ziweiResult) {
    return {
      summary: '八字与紫微斗数相互印证，命格层次较高，一生多贵人相助。',
      comparison: {
        yearly: [
          { item: '整体运势', bazi: '五行平衡，运势稳定', ziwei: '紫微入命，贵气十足' },
          { item: '事业发展', bazi: '适合稳步发展', ziwei: '有领导潜质' }
        ],
        monthly: [
          { item: '财运状况', bazi: '财星得位，财运亨通', ziwei: '财帛宫吉星云集' }
        ],
        daily: [
          { item: '人际关系', bazi: '人缘不错，贵人多助', ziwei: '社交能力强' }
        ]
      },
      advice: '建议充分发挥领导才能，在事业上可以大胆尝试，财运方面宜稳健投资。'
    };
  }

  static combineConstellationZiwei(constellation, ziweiResult) {
    return {
      summary: '星座特质与紫微命盘相得益彰，性格特点鲜明。',
      comparison: {
        yearly: [
          { item: '性格特点', constellation: '热情开朗，积极向上', ziwei: '命宫有贵星，气质不凡' }
        ],
        monthly: [
          { item: '感情运势', constellation: '感情丰富，魅力十足', ziwei: '夫妻宫星情良好' }
        ],
        daily: [
          { item: '健康状况', constellation: '活力充沛，体质较好', ziwei: '疾厄宫平稳' }
        ]
      },
      advice: '建议发挥星座的天赋优势，结合紫微命盘的指导，在合适的时机做出重要决策。'
    };
  }

  static getConstellationByBirthDate(birthDate) {
    // 简化的星座判断
    const month = parseInt(birthDate.split('-')[1]);
    const day = parseInt(birthDate.split('-')[2]);
    
    const constellations = [
      { name: '摩羯座', start: [12, 22], end: [1, 19] },
      { name: '水瓶座', start: [1, 20], end: [2, 18] },
      { name: '双鱼座', start: [2, 19], end: [3, 20] },
      { name: '白羊座', start: [3, 21], end: [4, 19] },
      { name: '金牛座', start: [4, 20], end: [5, 20] },
      { name: '双子座', start: [5, 21], end: [6, 21] },
      { name: '巨蟹座', start: [6, 22], end: [7, 22] },
      { name: '狮子座', start: [7, 23], end: [8, 22] },
      { name: '处女座', start: [8, 23], end: [9, 22] },
      { name: '天秤座', start: [9, 23], end: [10, 23] },
      { name: '天蝎座', start: [10, 24], end: [11, 22] },
      { name: '射手座', start: [11, 23], end: [12, 21] }
    ];
    
    return constellations.find(c => {
      const [startMonth, startDay] = c.start;
      const [endMonth, endDay] = c.end;
      
      if (startMonth === endMonth) {
        return month === startMonth && day >= startDay && day <= endDay;
      } else {
        return (month === startMonth && day >= startDay) || (month === endMonth && day <= endDay);
      }
    }) || { name: '白羊座' };
  }

  static formatBazi(bazi) {
    return `${bazi.year} ${bazi.month} ${bazi.day} ${bazi.hour}`;
  }

  // 基于六爻卦象的AI咨询
  static async consultLiuyaoWithAI(question, hexagramContext) {
    const axios = require('axios');
    
    try {
      // 构建卦象上下文描述
      const linesDesc = hexagramContext.lines ? 
        hexagramContext.lines.map((line, index) => 
          `第${index + 1}爻: ${line.type === 'yangLine' ? '阳爻(—)' : '阴爻(- -)'}${line.isChanging ? ' [变爻]' : ''}`
        ).join('\n') : '卦象信息不可用';
      
      // 构建时间信息描述
      const timeDesc = hexagramContext.timeInfo ? 
        `占卜时间: ${hexagramContext.timeInfo.year}年${hexagramContext.timeInfo.month}月${hexagramContext.timeInfo.day}日 ${hexagramContext.timeInfo.hour}时${hexagramContext.timeInfo.minute}分` : 
        '时间信息不可用';
      
      // 构建AI咨询提示词
      const prompt = `你是一位专业的六爻占卜大师，现在有一位客人基于之前的卦象解析，想要进一步咨询相关问题。

【原始卦象信息】
卦象名称: ${hexagramContext.hexagramName}
原始问题: ${hexagramContext.originalQuestion}
${timeDesc}

卦象详情:
${linesDesc}

原始卦象分析:
${hexagramContext.analysis}

【新的咨询问题】
${question}

请基于上述卦象的背景信息，针对新的咨询问题给出专业的解答。请注意：
1. 要结合原有卦象的含义来回答新问题
2. 保持与原始分析的一致性和连贯性
3. 回答要具体、实用，避免过于抽象
4. 如果新问题与原始问题相关，要指出其关联性
5. 控制回答长度在200-300字之间

请用专业而温和的语气回答。`;

      // 调用火山方舟API
      const response = await axios.post('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
        model: 'deepseek-v3-250324', // 使用正确的模型ID
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.8,
        max_tokens: 600
      }, {
        headers: {
          'Authorization': `Bearer 7ffa8945-a821-48ba-8170-0d59183afcec`, // 使用正确的API Key
          'Content-Type': 'application/json'
        },
        timeout: 15000 // 15秒超时
      });

      const aiAnswer = response.data.choices[0].message.content;
      
      return {
        answer: aiAnswer,
        consultTime: new Date().toIso8601String()
      };
      
    } catch (error) {
      console.error('六爻AI咨询失败:', error.message);
      
      // 提供备用回答
      const fallbackAnswer = `基于您之前的${hexagramContext.hexagramName}卦象，我建议您：

针对您的新问题"${question}"，结合原有的卦象分析，建议您继续保持当前的方向和态度。${hexagramContext.hexagramName}所体现的能量依然在影响着相关事情的发展。

建议您在处理这个新问题时，参考之前卦象给出的指导原则，并保持耐心和智慧。如果需要更详细的解答，建议您可以重新摇卦询问这个具体问题。

（注：网络连接不稳定，这是基于传统卦象的简要分析）`;

      return {
        answer: fallbackAnswer,
        consultTime: new Date().toIso8601String()
      };
    }
  }
}

module.exports = PredictionService; 