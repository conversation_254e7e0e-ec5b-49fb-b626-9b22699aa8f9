const AV = require('leanengine');
const axios = require('axios');

// 星座运势类
const ConstellationFortune = AV.Object.extend('ConstellationFortune');

// 星座类型枚举
ConstellationFortune.TYPES = {
  ARIES: 'aries',
  TAURUS: 'taurus',
  GEMINI: 'gemini',
  CANCER: 'cancer',
  LEO: 'leo',
  VIRGO: 'virgo',
  LIBRA: 'libra',
  SCORPIO: 'scorpio',
  SAGITTARIUS: 'sagittarius',
  CAPRICORN: 'capricorn',
  AQUARIUS: 'aquarius',
  PISCES: 'pisces'
};

// 星座名称映射
ConstellationFortune.NAMES = {
  aries: '白羊座',
  taurus: '金牛座',
  gemini: '双子座',
  cancer: '巨蟹座',
  leo: '狮子座',
  virgo: '处女座',
  libra: '天秤座',
  scorpio: '天蝎座',
  sagittarius: '射手座',
  capricorn: '摩羯座',
  aquarius: '水瓶座',
  pisces: '双鱼座'
};

// 星座日期范围
ConstellationFortune.DATE_RANGES = {
  aries: '3月21日 - 4月19日',
  taurus: '4月20日 - 5月20日',
  gemini: '5月21日 - 6月21日',
  cancer: '6月22日 - 7月22日',
  leo: '7月23日 - 8月22日',
  virgo: '8月23日 - 9月22日',
  libra: '9月23日 - 10月23日',
  scorpio: '10月24日 - 11月22日',
  sagittarius: '11月23日 - 12月21日',
  capricorn: '12月22日 - 1月19日',
  aquarius: '1月20日 - 2月18日',
  pisces: '2月19日 - 3月20日'
};

// 星座符号映射
ConstellationFortune.SYMBOLS = {
  aries: '♈',
  taurus: '♉',
  gemini: '♊',
  cancer: '♋',
  leo: '♌',
  virgo: '♍',
  libra: '♎',
  scorpio: '♏',
  sagittarius: '♐',
  capricorn: '♑',
  aquarius: '♒',
  pisces: '♓'
};

// 星座幸运方位
ConstellationFortune.LUCKY_DIRECTIONS = {
  aries: '东方',
  taurus: '西方',
  gemini: '南方',
  cancer: '北方',
  leo: '东南方',
  virgo: '西南方',
  libra: '东北方',
  scorpio: '西北方',
  sagittarius: '正南方',
  capricorn: '正北方',
  aquarius: '正东方',
  pisces: '正西方'
};

// 星座幸运食物
ConstellationFortune.LUCKY_FOODS = {
  aries: '辣椒',
  taurus: '牛肉',
  gemini: '坚果',
  cancer: '海鲜',
  leo: '橙子',
  virgo: '蔬菜',
  libra: '水果',
  scorpio: '巧克力',
  sagittarius: '肉类',
  capricorn: '鱼类',
  aquarius: '茶叶',
  pisces: '海带'
};

// 获取今日所有星座运势
ConstellationFortune.getTodayFortunes = async () => {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

  const query = new AV.Query(ConstellationFortune);
  query.greaterThanOrEqualTo('date', startOfDay);
  query.lessThan('date', endOfDay);
  query.equalTo('period', 'today');

  let fortunes = await query.find();

  // 如果今天没有数据，生成默认数据
  if (fortunes.length === 0) {
    console.log('📅 今日星座运势数据不存在，正在生成...');
    fortunes = await ConstellationFortune.generateTodayFortunes();
  } else {
    console.log(`📅 找到${fortunes.length}条今日星座运势数据`);
  }

  return fortunes.map(fortune => ({
    type: fortune.get('type'),
    name: ConstellationFortune.NAMES[fortune.get('type')],
    symbol: ConstellationFortune.SYMBOLS[fortune.get('type')],
    dateRange: ConstellationFortune.DATE_RANGES[fortune.get('type')],
    overallRating: fortune.get('overallRating'),
    loveRating: fortune.get('loveRating'),
    moneyRating: fortune.get('moneyRating'),
    careerRating: fortune.get('careerRating'),
    healthRating: fortune.get('healthRating'),
    luckyColor: fortune.get('luckyColor'),
    luckyNumber: fortune.get('luckyNumber'),
    luckyDirection: fortune.get('luckyDirection') || ConstellationFortune.LUCKY_DIRECTIONS[fortune.get('type')],
    luckyFood: fortune.get('luckyFood') || ConstellationFortune.LUCKY_FOODS[fortune.get('type')],
    summary: fortune.get('summary'),
    loveAdvice: fortune.get('loveAdvice'),
    moneyAdvice: fortune.get('moneyAdvice'),
    careerAdvice: fortune.get('careerAdvice'),
    healthAdvice: fortune.get('healthAdvice'),
    isAIGenerated: fortune.get('isAIGenerated') || false,
    lastUpdated: fortune.get('updatedAt') || fortune.get('createdAt')
  }));
};

// 获取指定星座的详细运势
ConstellationFortune.getConstellationFortune = async (type, period = 'today') => {
  const query = new AV.Query(ConstellationFortune);
  query.equalTo('type', type);
  query.equalTo('period', period);

  // 获取最新的运势数据
  query.descending('createdAt');
  query.limit(1);

  let fortune = await query.first();

  // 如果没有数据，生成默认数据
  if (!fortune) {
    fortune = await ConstellationFortune.generateFortune(type, period);
  }

  return {
    type: fortune.get('type'),
    name: ConstellationFortune.NAMES[fortune.get('type')],
    symbol: ConstellationFortune.SYMBOLS[fortune.get('type')],
    dateRange: ConstellationFortune.DATE_RANGES[fortune.get('type')],
    overallRating: fortune.get('overallRating'),
    loveRating: fortune.get('loveRating'),
    moneyRating: fortune.get('moneyRating'),
    careerRating: fortune.get('careerRating'),
    healthRating: fortune.get('healthRating'),
    luckyColor: fortune.get('luckyColor'),
    luckyNumber: fortune.get('luckyNumber'),
    luckyDirection: fortune.get('luckyDirection') || ConstellationFortune.LUCKY_DIRECTIONS[fortune.get('type')],
    luckyFood: fortune.get('luckyFood') || ConstellationFortune.LUCKY_FOODS[fortune.get('type')],
    summary: fortune.get('summary'),
    loveAdvice: fortune.get('loveAdvice'),
    moneyAdvice: fortune.get('moneyAdvice'),
    careerAdvice: fortune.get('careerAdvice'),
    healthAdvice: fortune.get('healthAdvice'),
    isAIGenerated: fortune.get('isAIGenerated') || false,
    lastUpdated: fortune.get('updatedAt') || fortune.get('createdAt')
  };
};

// 使用火山方舟AI模型获取星座运势
ConstellationFortune.getAIConstellationFortune = async (type) => {
  try {
    console.log(`🔮 开始请求AI星座运势解析: ${ConstellationFortune.NAMES[type]}`);
    
    const constellation = ConstellationFortune.NAMES[type];
    const dateRange = ConstellationFortune.DATE_RANGES[type];
    const today = new Date();
    const dateString = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
    
    // 构建提示词，要求AI生成结构化的星座运势解析
    const prompt = `你是一位专业的星座运势分析师。请为${constellation}（${dateRange}）生成${dateString}的详细运势分析。

分析需要包含以下内容，并使用JSON格式输出：
1. 总体运势概述(summary) - 必须严格控制在30-45字之间，不能超过45字
2. 爱情运势(loveAdvice) - 必须严格控制在30-45字之间，不能超过45字
3. 财运分析(moneyAdvice) - 必须严格控制在30-45字之间，不能超过45字
4. 事业运势(careerAdvice) - 必须严格控制在30-45字之间，不能超过45字
5. 健康运势(healthAdvice) - 必须严格控制在30-45字之间，不能超过45字
6. 幸运颜色(luckyColor)
7. 幸运数字(luckyNumber)

请确保分析内容专业、积极、有指导性，避免模糊或过于消极的预测。每个分析部分必须提供具体的指导和建议，内容要详细且有针对性，字数必须严格控制在30-45字之间，不能超过45字。在生成前务必精确计算字数。

输出格式为纯JSON，不要有其他文字，格式如下:
{
  "summary": "今日运势顺畅，人际交往有收获。适合处理要事，可能遇小障碍但能解决。保持积极心态获得更多机会。",
  "loveAdvice": "感情需要耐心和理解，尤其对方情绪波动时。单身者可通过朋友介绍认识新人，增加社交机会。",
  "moneyAdvice": "财务稳定，适合投资。避免冲动消费，工作收入有小增长，可考虑增加被动收入渠道。",
  "careerAdvice": "工作有新挑战，是展示能力好机会。主动承担责任，与同事保持良好沟通，提前做好准备。",
  "healthAdvice": "身体状况良好，注意保护颈椎和腰部。工作后应活动伸展，饮食清淡，多补充维生素。",
  "luckyColor": "深蓝色",
  "luckyNumber": "7",
  "overallRating": 4,
  "loveRating": 3,
  "moneyRating": 4,
  "careerRating": 5,
  "healthRating": 4
}

其中评分为1-5分，1分最差，5分最好。严格确保每个文本字段的内容在30-45字之间，必须精确计算字数，不能少于30字，不能超过45字。生成前必须检查每个字段的字数，确保符合要求。`;

    // 调用火山方舟API
    const response = await axios.post('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
      model: 'deepseek-v3-250324', // 使用正确的模型ID
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.VOLCANO_API_KEY || '7ffa8945-a821-48ba-8170-0d59183afcec'}`, // 使用环境变量中的API Key
        'Content-Type': 'application/json'
      },
      timeout: 15000 // 15秒超时
    });

    // 解析AI响应
    const aiAnswer = response.data.choices[0].message.content;
    console.log('✅ AI星座运势解析成功');
    
    let fortuneData;
    try {
      // 尝试解析JSON，处理可能包含Markdown代码块的情况
      let jsonContent = aiAnswer;
      
      // 移除可能的Markdown代码块标记
      const jsonRegex = /```(?:json)?\s*([\s\S]*?)```/;
      const match = jsonRegex.exec(jsonContent);
      if (match && match[1]) {
        jsonContent = match[1].trim();
      }
      
      // 尝试解析JSON
      fortuneData = JSON.parse(jsonContent);
      console.log('✅ JSON解析成功');
    } catch (e) {
      console.error('❌ JSON解析失败', e);
      // 如果解析失败，生成默认数据
      fortuneData = await ConstellationFortune._generateDefaultFortune(type);
    }

    // 保存到数据库
    const fortune = new ConstellationFortune();
    fortune.set('type', type);
    fortune.set('period', 'today');
    fortune.set('date', new Date());
    fortune.set('overallRating', fortuneData.overallRating || Math.floor(Math.random() * 5) + 1);
    fortune.set('loveRating', fortuneData.loveRating || Math.floor(Math.random() * 5) + 1);
    fortune.set('moneyRating', fortuneData.moneyRating || Math.floor(Math.random() * 5) + 1);
    fortune.set('careerRating', fortuneData.careerRating || Math.floor(Math.random() * 5) + 1);
    fortune.set('healthRating', fortuneData.healthRating || Math.floor(Math.random() * 5) + 1);
    fortune.set('luckyColor', fortuneData.luckyColor || '紫色');
    fortune.set('luckyNumber', fortuneData.luckyNumber || String(Math.floor(Math.random() * 9) + 1));
    fortune.set('luckyDirection', fortuneData.luckyDirection || ConstellationFortune.LUCKY_DIRECTIONS[type]);
    fortune.set('luckyFood', fortuneData.luckyFood || ConstellationFortune.LUCKY_FOODS[type]);
    fortune.set('summary', fortuneData.summary || `今天是${ConstellationFortune.NAMES[type]}运势不错的一天...`);
    fortune.set('loveAdvice', fortuneData.loveAdvice || '在感情方面要主动一些...');
    fortune.set('moneyAdvice', fortuneData.moneyAdvice || '财运方面需要谨慎理财...');
    fortune.set('careerAdvice', fortuneData.careerAdvice || '工作上要保持积极态度...');
    fortune.set('healthAdvice', fortuneData.healthAdvice || '注意身体健康，适当运动...');
    fortune.set('isAIGenerated', true);

    const savedFortune = await fortune.save();
    console.log('✅ 星座运势保存成功');

    return {
      type: savedFortune.get('type'),
      name: ConstellationFortune.NAMES[savedFortune.get('type')],
      symbol: ConstellationFortune.SYMBOLS[savedFortune.get('type')],
      dateRange: ConstellationFortune.DATE_RANGES[savedFortune.get('type')],
      overallRating: savedFortune.get('overallRating'),
      loveRating: savedFortune.get('loveRating'),
      moneyRating: savedFortune.get('moneyRating'),
      careerRating: savedFortune.get('careerRating'),
      healthRating: savedFortune.get('healthRating'),
      luckyColor: savedFortune.get('luckyColor'),
      luckyNumber: savedFortune.get('luckyNumber'),
      luckyDirection: savedFortune.get('luckyDirection'),
      luckyFood: savedFortune.get('luckyFood'),
      summary: savedFortune.get('summary'),
      loveAdvice: savedFortune.get('loveAdvice'),
      moneyAdvice: savedFortune.get('moneyAdvice'),
      careerAdvice: savedFortune.get('careerAdvice'),
      healthAdvice: savedFortune.get('healthAdvice'),
      isAIGenerated: true
    };
  } catch (error) {
    console.error('❌ AI星座运势解析失败:', error.message);
    
    // 如果API调用失败，使用默认数据生成方式
    const fortune = await ConstellationFortune.generateFortune(type, 'today');
    
    return {
      type: fortune.get('type'),
      name: ConstellationFortune.NAMES[fortune.get('type')],
      symbol: ConstellationFortune.SYMBOLS[fortune.get('type')],
      dateRange: ConstellationFortune.DATE_RANGES[fortune.get('type')],
      overallRating: fortune.get('overallRating'),
      loveRating: fortune.get('loveRating'),
      moneyRating: fortune.get('moneyRating'),
      careerRating: fortune.get('careerRating'),
      healthRating: fortune.get('healthRating'),
      luckyColor: fortune.get('luckyColor'),
      luckyNumber: fortune.get('luckyNumber'),
      luckyDirection: ConstellationFortune.LUCKY_DIRECTIONS[fortune.get('type')],
      luckyFood: ConstellationFortune.LUCKY_FOODS[fortune.get('type')],
      summary: fortune.get('summary'),
      loveAdvice: fortune.get('loveAdvice'),
      moneyAdvice: fortune.get('moneyAdvice'),
      careerAdvice: fortune.get('careerAdvice'),
      healthAdvice: fortune.get('healthAdvice'),
      isAIGenerated: false
    };
  }
};

// 生成今日所有星座运势
ConstellationFortune.generateTodayFortunes = async () => {
  const fortunes = [];
  const today = new Date();

  for (const type of Object.values(ConstellationFortune.TYPES)) {
    const fortune = new ConstellationFortune();
    fortune.set('type', type);
    fortune.set('period', 'today');
    fortune.set('date', today);
    fortune.set('overallRating', Math.floor(Math.random() * 5) + 1);
    fortune.set('loveRating', Math.floor(Math.random() * 5) + 1);
    fortune.set('moneyRating', Math.floor(Math.random() * 5) + 1);
    fortune.set('careerRating', Math.floor(Math.random() * 5) + 1);
    fortune.set('healthRating', Math.floor(Math.random() * 5) + 1);
    fortune.set('luckyColor', ['红色', '蓝色', '绿色', '黄色', '紫色'][Math.floor(Math.random() * 5)]);
    fortune.set('luckyNumber', Math.floor(Math.random() * 9) + 1);
    fortune.set('luckyDirection', ConstellationFortune.LUCKY_DIRECTIONS[type]);
    fortune.set('luckyFood', ConstellationFortune.LUCKY_FOODS[type]);
    fortune.set('summary', `今天是${ConstellationFortune.NAMES[type]}运势不错的一天...`);
    fortune.set('loveAdvice', '在感情方面要主动一些...');
    fortune.set('moneyAdvice', '财运方面需要谨慎理财...');
    fortune.set('careerAdvice', '工作上要保持积极态度...');
    fortune.set('healthAdvice', '注意身体健康，适当运动...');
    fortune.set('isAIGenerated', false);

    fortunes.push(fortune);
  }

  return await AV.Object.saveAll(fortunes);
};

// 生成单个星座运势
ConstellationFortune.generateFortune = async (type, period) => {
  const fortune = new ConstellationFortune();
  fortune.set('type', type);
  fortune.set('period', period);
  fortune.set('date', new Date());
  fortune.set('overallRating', Math.floor(Math.random() * 5) + 1);
  fortune.set('loveRating', Math.floor(Math.random() * 5) + 1);
  fortune.set('moneyRating', Math.floor(Math.random() * 5) + 1);
  fortune.set('careerRating', Math.floor(Math.random() * 5) + 1);
  fortune.set('healthRating', Math.floor(Math.random() * 5) + 1);
  fortune.set('luckyColor', ['红色', '蓝色', '绿色', '黄色', '紫色'][Math.floor(Math.random() * 5)]);
  fortune.set('luckyNumber', Math.floor(Math.random() * 9) + 1);
  fortune.set('luckyDirection', ConstellationFortune.LUCKY_DIRECTIONS[type]);
  fortune.set('luckyFood', ConstellationFortune.LUCKY_FOODS[type]);
  fortune.set('summary', `这是${ConstellationFortune.NAMES[type]}的${period}运势...`);
  fortune.set('loveAdvice', '在感情方面要主动一些...');
  fortune.set('moneyAdvice', '财运方面需要谨慎理财...');
  fortune.set('careerAdvice', '工作上要保持积极态度...');
  fortune.set('healthAdvice', '注意身体健康，适当运动...');
  fortune.set('isAIGenerated', false);

  return await fortune.save();
};

// 生成默认的星座运势数据（内部使用）
ConstellationFortune._generateDefaultFortune = async (type) => {
  return {
    overallRating: Math.floor(Math.random() * 5) + 1,
    loveRating: Math.floor(Math.random() * 5) + 1,
    moneyRating: Math.floor(Math.random() * 5) + 1,
    careerRating: Math.floor(Math.random() * 5) + 1,
    healthRating: Math.floor(Math.random() * 5) + 1,
    luckyColor: ['红色', '蓝色', '绿色', '黄色', '紫色'][Math.floor(Math.random() * 5)],
    luckyNumber: String(Math.floor(Math.random() * 9) + 1),
    luckyDirection: ConstellationFortune.LUCKY_DIRECTIONS[type],
    luckyFood: ConstellationFortune.LUCKY_FOODS[type],
    summary: `今天是${ConstellationFortune.NAMES[type]}运势不错的一天...`,
    loveAdvice: '在感情方面要主动一些...',
    moneyAdvice: '财运方面需要谨慎理财...',
    careerAdvice: '工作上要保持积极态度...',
    healthAdvice: '注意身体健康，适当运动...'
  };
};

// 批量更新所有星座的今日运势（使用AI）
ConstellationFortune.updateAllDailyFortunes = async () => {
  console.log('🔄 开始更新所有星座今日运势...');
  const results = [];
  const today = new Date();
  const dateString = `${today.getFullYear()}年${today.getMonth() + 1}月${today.getDate()}日`;
  
  // 先删除今天已有的运势数据
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
  
  const query = new AV.Query(ConstellationFortune);
  query.greaterThanOrEqualTo('date', startOfDay);
  query.lessThan('date', endOfDay);
  query.equalTo('period', 'today');
  
  try {
    const existingFortunes = await query.find();
    if (existingFortunes.length > 0) {
      console.log(`🗑️ 删除今日已有的${existingFortunes.length}条星座运势数据`);
      await AV.Object.destroyAll(existingFortunes);
    }
    
    // 为所有星座生成今日运势
    for (const type of Object.values(ConstellationFortune.TYPES)) {
      try {
        console.log(`🔮 正在为${ConstellationFortune.NAMES[type]}生成今日运势...`);
        const fortune = await ConstellationFortune.getAIConstellationFortune(type);
        results.push(fortune);
        console.log(`✅ ${ConstellationFortune.NAMES[type]}今日运势生成成功`);
        
        // 添加延迟，避免API请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error(`❌ 生成${ConstellationFortune.NAMES[type]}运势失败:`, error);
        // 如果AI生成失败，使用默认数据
        const defaultFortune = await ConstellationFortune.generateFortune(type, 'today');
        results.push({
          type: defaultFortune.get('type'),
          name: ConstellationFortune.NAMES[defaultFortune.get('type')],
          symbol: ConstellationFortune.SYMBOLS[defaultFortune.get('type')],
          dateRange: ConstellationFortune.DATE_RANGES[defaultFortune.get('type')],
          overallRating: defaultFortune.get('overallRating'),
          loveRating: defaultFortune.get('loveRating'),
          moneyRating: defaultFortune.get('moneyRating'),
          careerRating: defaultFortune.get('careerRating'),
          healthRating: defaultFortune.get('healthRating'),
          luckyColor: defaultFortune.get('luckyColor'),
          luckyNumber: defaultFortune.get('luckyNumber'),
          luckyDirection: ConstellationFortune.LUCKY_DIRECTIONS[defaultFortune.get('type')],
          luckyFood: ConstellationFortune.LUCKY_FOODS[defaultFortune.get('type')],
          summary: defaultFortune.get('summary'),
          loveAdvice: defaultFortune.get('loveAdvice'),
          moneyAdvice: defaultFortune.get('moneyAdvice'),
          careerAdvice: defaultFortune.get('careerAdvice'),
          healthAdvice: defaultFortune.get('healthAdvice'),
          isAIGenerated: false
        });
      }
    }
    
    console.log(`✅ 所有星座今日运势更新完成，共${results.length}条数据`);
    return results;
  } catch (error) {
    console.error('❌ 批量更新星座运势失败:', error);
    throw error;
  }
};

// 检查今日运势是否已更新
ConstellationFortune.checkTodayFortunesUpdated = async () => {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);
  
  const query = new AV.Query(ConstellationFortune);
  query.greaterThanOrEqualTo('date', startOfDay);
  query.lessThan('date', endOfDay);
  query.equalTo('period', 'today');
  
  const count = await query.count();
  return count >= 12; // 如果有12个星座的数据，说明今日运势已更新
};

module.exports = ConstellationFortune; 