import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../widgets/common_widgets.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../widgets/connection_status_widget.dart';
import 'connection_debug_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // 获取菜单列表（根据用户状态动态生成）
  List<List<MenuItem>> _getMenuGroups(bool isGuest) {
    final commonGroup = [
      MenuItem(
        icon: FontAwesomeIcons.clockRotateLeft,
        title: '历史记录',
        color: Colors.blue,
        route: '/history',
        isDisabled: isGuest,
      ),
      MenuItem(
        icon: FontAwesomeIcons.star,
        title: '我的收藏',
        color: Colors.amber,
        route: '/favorite',
        isDisabled: isGuest,
      ),
      MenuItem(
        icon: FontAwesomeIcons.bell,
        title: '消息通知',
        color: Colors.green,
        route: '/notification',
        isDisabled: isGuest,
      ),
    ];

    final settingsGroup = [
      MenuItem(
        icon: FontAwesomeIcons.gear,
        title: '设置',
        color: Colors.grey,
        route: '/settings',
      ),
      MenuItem(
        icon: FontAwesomeIcons.wifi,
        title: '连接状态',
        color: Colors.orange,
        route: '/connection-debug',
      ),
      MenuItem(
        icon: FontAwesomeIcons.circleQuestion,
        title: '帮助与反馈',
        color: Colors.purple,
        route: '/help',
      ),
      MenuItem(
        icon: FontAwesomeIcons.circleInfo,
        title: '关于我们',
        color: Colors.blue,
        route: '/about',
      ),
    ];

    final logoutGroup = [
      MenuItem(
        icon: isGuest ? FontAwesomeIcons.signIn : FontAwesomeIcons.signOut,
        title: isGuest ? '退出游客模式' : '退出登录',
        color: Colors.red,
        route: '/logout',
      ),
    ];

    return [commonGroup, settingsGroup, logoutGroup];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // iOS状态栏
              const IOSStatusBar(),
              // 顶部导航栏
              CustomNavigationBar(
                title: '我的',
                onBackPressed: () => context.pop(),
              ),
              // 主内容区
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // 个人信息区域
                      _buildProfileHeader(),
                      const SizedBox(height: 24),
                      // 菜单列表
                      _buildMenuGroups(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
              // 底部导航
              const BottomNavigation(currentIndex: 1),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final isGuest = userProvider.isGuest;
        final user = userProvider.user;
        
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isGuest 
                  ? [const Color(0xFFFF8A65), const Color(0xFFFF7043)]
                  : [const Color(0xFF3B82F6), const Color(0xFF1E40AF)],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // 游客模式提示横幅（仅游客显示）
                if (isGuest) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.visibility_outlined,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            '您正在使用游客模式，注册登录享受完整功能',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => context.go('/login'),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '立即登录',
                              style: TextStyle(
                                color: Color(0xFFFF7043),
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                // 用户头像和基本信息
                Row(
                  children: [
                    // 头像
                    GestureDetector(
                      onTap: () {
                        if (isGuest) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('游客模式下无法编辑头像，请先登录')),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('头像编辑功能开发中...')),
                          );
                        }
                      },
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 8,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Icon(
                          isGuest ? FontAwesomeIcons.userSecret : FontAwesomeIcons.user,
                          color: isGuest ? const Color(0xFFFF7043) : const Color(0xFF3B82F6),
                          size: 32,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // 用户信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user.nickname,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              userProvider.memberStatusText,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          if (isGuest)
                            const Text(
                              '加入日期: 游客模式',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white70,
                              ),
                            )
                          else
                            Text(
                              '加入日期: ${user.createdAt.year}-${user.createdAt.month.toString().padLeft(2, '0')}-${user.createdAt.day.toString().padLeft(2, '0')}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white70,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                // 统计数据
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      '预测记录',
                      user.predictionCount.toString(),
                      isDisabled: isGuest,
                    ),
                    _buildStatItem(
                      '收藏',
                      user.favoriteCount.toString(),
                      isDisabled: isGuest,
                    ),
                    _buildStatItem(
                      '咨询',
                      user.consultationCount.toString(),
                      isDisabled: isGuest,
                    ),
                  ],
                ),
                
                // 游客模式升级提示
                if (isGuest) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.star_outline,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          '注册后可保存数据和历史记录',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 8),
                        GestureDetector(
                          onTap: () => context.go('/register'),
                          child: const Text(
                            '立即注册',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, {bool isDisabled = false}) {
    return Column(
      children: [
        Text(
          isDisabled ? '-' : value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isDisabled ? Colors.white.withOpacity(0.5) : Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: isDisabled 
                ? Colors.white.withOpacity(0.4) 
                : Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuGroups() {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        final menuGroups = _getMenuGroups(userProvider.isGuest);
        
        return Column(
          children: menuGroups.map((group) {
            return Container(
              margin: const EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: 16,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: group.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return _buildMenuItem(
                    item,
                    isLast: index == group.length - 1,
                  );
                }).toList(),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildMenuItem(MenuItem item, {bool isLast = false}) {
    return GestureDetector(
      onTap: item.isDisabled ? null : () {
        if (item.route == '/history') {
          // 跳转到历史记录页面
          context.push('/history');
        } else if (item.route == '/logout') {
          // 退出登录或游客模式
          _showLogoutDialog();
        } else if (item.route == '/connection-debug') {
          // 跳转到连接调试页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ConnectionDebugScreen(),
            ),
          );
        } else {
          // 其他功能暂未实现
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${item.title}功能开发中...')),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: item.isDisabled 
                    ? Colors.grey.withOpacity(0.1) 
                    : item.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                item.icon,
                color: item.isDisabled ? Colors.grey : item.color,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            // 标题
            Expanded(
              child: Row(
                children: [
                  Text(
                    item.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: item.isDisabled ? Colors.grey : Colors.black87,
                    ),
                  ),
                  if (item.isDisabled) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '需登录',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // 右箭头
            Icon(
              Icons.chevron_right,
              color: item.isDisabled ? Colors.grey.shade300 : Colors.grey.shade400,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  // 显示退出确认对话框
  void _showLogoutDialog() {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final isGuest = userProvider.isGuest;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            isGuest ? '退出游客模式' : '确认退出',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          content: Text(
            isGuest 
                ? '您确定要退出游客模式吗？退出后将返回登录页面。' 
                : '您确定要退出登录吗？',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '取消',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _logout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '退出',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  // 修改退出登录方法为异步
  Future<void> _logout() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    try {
      await userProvider.logout();
      
      if (mounted) {
        // 显示退出成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle_outline, color: Colors.white),
                SizedBox(width: 12),
                Text('已退出登录', style: TextStyle(fontSize: 16)),
              ],
            ),
            backgroundColor: Colors.green.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
        
        // 跳转到登录页面
        context.go('/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 12),
                Text('退出失败: ${e.toString()}', style: const TextStyle(fontSize: 16)),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }
}

class MenuItem {
  final IconData icon;
  final String title;
  final Color color;
  final String route;
  final bool isDisabled;

  MenuItem({
    required this.icon,
    required this.title,
    required this.color,
    required this.route,
    this.isDisabled = false,
  });
} 