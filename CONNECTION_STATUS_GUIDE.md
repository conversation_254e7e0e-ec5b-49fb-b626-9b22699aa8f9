# 连接状态显示功能说明

## 功能概述

为了解决前后端连接问题，我们在应用中添加了实时连接状态显示功能，让用户清楚地了解当前的后端连接状态。

## 主要功能

### 1. 实时连接监控
- 每30秒自动检测后端连接状态
- 应用启动时立即检测连接
- 支持手动刷新连接状态

### 2. 状态显示
- **连接中**：橙色，显示正在检测连接
- **已连接**：绿色，后端服务正常
- **离线**：红色，无法连接到后端
- **错误**：红色，连接过程中出现错误

### 3. 用户界面

#### 小型状态指示器
- 位置：各页面右上角
- 显示：状态图标 + 文字 + 重试次数
- 功能：点击可查看详情，支持手动刷新

#### 连接状态横幅
- 位置：页面顶部（仅在连接异常时显示）
- 显示：详细的连接状态信息和错误描述
- 功能：提供重试按钮

#### 连接调试页面
- 位置：个人中心 → 连接状态
- 功能：详细的连接诊断和测试工具

## 使用方法

### 查看连接状态
1. 在任何页面右上角查看连接状态指示器
2. 绿色表示正常，红色表示异常
3. 点击状态指示器可查看详细信息

### 手动刷新连接
1. 点击状态指示器旁的刷新按钮
2. 或在连接横幅中点击"重试"按钮
3. 系统会立即检测连接状态

### 连接诊断
1. 进入个人中心
2. 点击"连接状态"菜单项
3. 查看详细的连接诊断信息
4. 使用测试工具检测不同API端点

## 技术实现

### 架构设计
- `ConnectionProvider`: 全局连接状态管理
- `ConnectionStatusWidget`: 状态显示组件
- `ConnectionBanner`: 连接异常横幅
- `ConnectionDebugScreen`: 调试页面

### 监控机制
- 使用Timer定期检测连接状态
- 通过Provider模式实现状态共享
- 支持多种连接测试方法

### 错误处理
- 网络超时检测（5秒）
- 详细的错误信息记录
- 优雅的降级处理

## 故障排除

### 常见问题

1. **显示"后端离线"**
   - 检查后端服务是否启动
   - 确认后端端口是否为3001
   - 检查防火墙设置

2. **显示"连接错误"**
   - 查看错误详情
   - 检查网络连接
   - 尝试重启应用

3. **连接不稳定**
   - 检查网络质量
   - 确认后端服务稳定性
   - 查看后端日志

### 调试步骤

1. 进入连接调试页面
2. 查看诊断信息
3. 执行连接测试
4. 执行API测试
5. 分析测试结果

## 配置说明

### 后端地址配置
- 默认地址：`http://localhost:3001`
- 修改位置：`lib/services/api_service.dart`

### 监控间隔配置
- 默认间隔：30秒
- 修改位置：`lib/providers/connection_provider.dart`

### 超时设置
- 连接超时：5秒
- 接收超时：5秒
- 修改位置：`lib/services/api_service.dart`

## 开发者注意事项

1. 连接状态会影响用户体验，确保及时响应
2. 在网络异常时提供合理的降级方案
3. 记录详细的连接日志便于问题排查
4. 定期检查和优化连接检测逻辑

## 更新日志

### v1.0.0
- 实现基础连接状态监控
- 添加状态显示组件
- 集成连接调试功能
- 支持手动刷新和自动检测 