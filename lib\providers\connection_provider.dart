import 'package:flutter/material.dart';
import 'dart:async';
import '../services/api_service.dart';

enum ConnectionStatus {
  connecting,
  connected,
  disconnected,
  error,
}

class ConnectionProvider extends ChangeNotifier {
  ConnectionStatus _status = ConnectionStatus.connecting;
  String _errorMessage = '';
  DateTime? _lastConnectedTime;
  int _retryCount = 0;
  Timer? _connectionTimer;
  
  ConnectionStatus get status => _status;
  String get errorMessage => _errorMessage;
  DateTime? get lastConnectedTime => _lastConnectedTime;
  int get retryCount => _retryCount;
  
  bool get isConnected => _status == ConnectionStatus.connected;
  bool get isDisconnected => _status == ConnectionStatus.disconnected;
  bool get isConnecting => _status == ConnectionStatus.connecting;
  bool get hasError => _status == ConnectionStatus.error;
  
  String get statusText {
    switch (_status) {
      case ConnectionStatus.connecting:
        return '连接中...';
      case ConnectionStatus.connected:
        return '后端已连接';
      case ConnectionStatus.disconnected:
        return '后端离线';
      case ConnectionStatus.error:
        return '连接错误';
    }
  }
  
  Color get statusColor {
    switch (_status) {
      case ConnectionStatus.connecting:
        return Colors.orange;
      case ConnectionStatus.connected:
        return Colors.green;
      case ConnectionStatus.disconnected:
        return Colors.red;
      case ConnectionStatus.error:
        return Colors.red;
    }
  }
  
  IconData get statusIcon {
    switch (_status) {
      case ConnectionStatus.connecting:
        return Icons.wifi_protected_setup;
      case ConnectionStatus.connected:
        return Icons.wifi;
      case ConnectionStatus.disconnected:
        return Icons.wifi_off;
      case ConnectionStatus.error:
        return Icons.error;
    }
  }

  ConnectionProvider() {
    _startConnectionMonitoring();
  }

  void _startConnectionMonitoring() {
    // 立即检查一次连接
    _checkConnection();
    
    // 每30秒检查一次连接状态
    _connectionTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _checkConnection();
    });
  }

  Future<void> _checkConnection() async {
    try {
      _updateStatus(ConnectionStatus.connecting);
      
      final apiService = ApiService();
      final isConnected = await apiService.testConnection();
      
      if (isConnected) {
        _updateStatus(ConnectionStatus.connected);
        _lastConnectedTime = DateTime.now();
        _retryCount = 0;
        _errorMessage = '';
      } else {
        _updateStatus(ConnectionStatus.disconnected);
        _retryCount++;
        _errorMessage = '无法连接到后端服务器 (localhost:3000)';
      }
    } catch (e) {
      _updateStatus(ConnectionStatus.error);
      _retryCount++;
      _errorMessage = '连接测试失败: ${e.toString()}';
      print('Connection check error: $e');
    }
  }

  void _updateStatus(ConnectionStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      notifyListeners();
    }
  }

  // 手动刷新连接状态
  Future<void> refreshConnection() async {
    await _checkConnection();
  }

  // 获取连接诊断信息
  Map<String, dynamic> getConnectionDiagnostics() {
    return {
      'status': _status.toString(),
      'isConnected': isConnected,
      'lastConnectedTime': _lastConnectedTime?.toIso8601String(),
      'retryCount': _retryCount,
      'errorMessage': _errorMessage,
      'baseUrl': ApiService.baseUrl,
    };
  }

  @override
  void dispose() {
    _connectionTimer?.cancel();
    super.dispose();
  }
} 