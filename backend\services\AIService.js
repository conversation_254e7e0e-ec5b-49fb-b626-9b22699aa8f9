const axios = require('axios');
const winston = require('winston');
require('dotenv').config();

class AIService {
  constructor() {
    this.baseURL = process.env.VOLCANO_API_URL;
    this.apiKey = process.env.VOLCANO_API_KEY;
    this.modelName = process.env.VOLCANO_MODEL_NAME;
    
    // 配置日志
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.json(),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'ai-service.log' })
      ]
    });
  }

  // 发送消息到AI模型
  async sendMessage(userMessage, conversationHistory = []) {
    try {
      // 构建对话历史
      const messages = this.buildConversationHistory(conversationHistory, userMessage);
      
      // 🔧 详细调试日志：请求前准备
      const requestURL = `${this.baseURL}/chat/completions`;
      const requestData = {
        model: this.modelName,
        messages: messages,
        max_tokens: 2000,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
        stream: false
      };
      
      const requestHeaders = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      };

      // 🔍 详细调试日志：请求信息
      console.log('\n🚀 ===== 火山方舟API调用开始 =====');
      console.log('📍 请求URL:', requestURL);
      console.log('🔑 API Key:', this.apiKey ? `${this.apiKey.substring(0, 8)}...` : 'undefined');
      console.log('🤖 模型名称:', this.modelName);
      console.log('📝 消息数量:', messages.length);
      console.log('📤 请求数据:', JSON.stringify(requestData, null, 2));
      console.log('📋 请求头:', JSON.stringify(requestHeaders, null, 2));
      
      this.logger.info(`🚀 火山方舟API调用: ${requestURL}, 模型: ${this.modelName}`);

      const response = await axios.post(requestURL, requestData, {
        headers: requestHeaders,
        timeout: 120000 // 增加到120秒超时
      });

      // 🔍 详细调试日志：响应信息
      console.log('\n✅ ===== 火山方舟API响应成功 =====');
      console.log('📊 响应状态:', response.status);
      console.log('📥 响应头:', JSON.stringify(response.headers, null, 2));
      console.log('📦 响应数据:', JSON.stringify(response.data, null, 2));

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const aiMessage = response.data.choices[0].message.content;
        console.log('💬 AI回复内容:', aiMessage);
        console.log('🎉 ===== 火山方舟API调用完成 =====\n');
        
        this.logger.info(`✅ AI响应成功: ${aiMessage.substring(0, 100)}...`);
        return aiMessage;
      } else {
        console.log('❌ AI响应格式异常:', response.data);
        throw new Error('AI响应格式异常');
      }
    } catch (error) {
      // 🔍 详细调试日志：错误信息
      console.log('\n❌ ===== 火山方舟API调用失败 =====');
      console.log('🚨 错误类型:', error.name);
      console.log('📝 错误信息:', error.message);
      console.log('🔢 错误代码:', error.code);
      
      if (error.response) {
        console.log('📊 响应状态:', error.response.status);
        console.log('📄 响应状态文本:', error.response.statusText);
        console.log('📥 响应头:', JSON.stringify(error.response.headers, null, 2));
        console.log('📦 响应数据:', JSON.stringify(error.response.data, null, 2));
      } else if (error.request) {
        console.log('🌐 请求信息:', error.request);
      }
      
      console.log('📋 完整错误对象:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
      console.log('💥 ===== 火山方舟API调用结束 =====\n');
      
      this.logger.error('❌ AI服务调用失败:', error);
      
      // 根据错误类型返回不同的回复
      if (error.code === 'ECONNABORTED') {
        return '抱歉，AI服务响应超时，请稍后再试。';
      } else if (error.response && error.response.status === 429) {
        return '当前咨询人数较多，请稍后再试。';
      } else if (error.response && error.response.status === 401) {
        return '服务认证失败，请联系管理员。';
      } else {
        return this.getFallbackResponse(userMessage);
      }
    }
  }

  // 流式发送消息到AI模型
  async sendMessageStream(userMessage, conversationHistory = [], onChunk) {
    try {
      // 构建对话历史
      const messages = this.buildConversationHistory(conversationHistory, userMessage);
      
      const requestURL = `${this.baseURL}/chat/completions`;
      const requestData = {
        model: this.modelName,
        messages: messages,
        max_tokens: 2000,
        temperature: 0.7,
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
        stream: true // 启用流式输出
      };
      
      const requestHeaders = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      };

      console.log('\n🚀 ===== 火山方舟流式API调用开始 =====');
      console.log('📍 请求URL:', requestURL);
      console.log('🤖 模型名称:', this.modelName);
      console.log('📝 消息数量:', messages.length);
      console.log('🔄 流式模式: 启用');
      
      const response = await axios.post(requestURL, requestData, {
        headers: requestHeaders,
        timeout: 120000, // 2分钟超时
        responseType: 'stream'
      });

      let fullResponse = '';
      
      // 处理流式响应
      response.data.on('data', (chunk) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              console.log('🎉 流式响应完成');
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                const content = parsed.choices[0].delta.content;
                fullResponse += content;
                onChunk(content);
              }
            } catch (e) {
              // 忽略JSON解析错误
            }
          }
        }
      });
      
      // 等待流式响应完成
      await new Promise((resolve, reject) => {
        response.data.on('end', resolve);
        response.data.on('error', reject);
      });
      
      console.log('✅ 流式AI响应成功');
      console.log('🎉 ===== 火山方舟流式API调用完成 =====\n');
      
      return fullResponse;
      
    } catch (error) {
      console.log('\n❌ ===== 火山方舟流式API调用失败 =====');
      console.log('🚨 错误信息:', error.message);
      console.log('💥 ===== 火山方舟流式API调用结束 =====\n');
      
      // 回退到普通方式
      console.log('🔄 回退到普通AI调用方式...');
      return await this.sendMessage(userMessage, conversationHistory);
    }
  }

  // 构建对话历史
  buildConversationHistory(history, currentMessage) {
    const messages = [
      {
        role: 'system',
        content: `你是一位专业的命理咨询师，精通八字、紫微斗数、六爻、星座等各种预测方法。你的回答应该：
1. 专业而详细，包含具体的命理分析
2. 语言温和，富有智慧
3. 给出实用的建议和指导
4. 适当引用传统文化知识
5. 保持神秘感和专业性

请用中文回答，语气要亲切专业。`
      }
    ];

    // 添加历史对话
    history.forEach(msg => {
      messages.push({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      });
    });

    // 添加当前消息
    messages.push({
      role: 'user',
      content: currentMessage
    });

    return messages;
  }

  // 获取回退响应（当AI服务不可用时）
  getFallbackResponse(userMessage) {
    const fallbackResponses = {
      '财运': '从命理角度来看，财运往往与个人的努力和时机密切相关。建议您在理财方面要谨慎稳健，避免过度投机。同时，保持积极的心态和勤奋的工作态度，财运自然会有所提升。',
      
      '事业': '事业发展需要天时地利人和。建议您要把握好当前的机遇，同时要注意人际关系的维护。在工作中要展现出您的专业能力和责任心，这样才能获得更好的发展。',
      
      '感情': '感情运势与个人的性格和处世方式息息相关。建议您在感情中要真诚待人，学会包容和理解。对于单身的朋友，要主动创造机会；对于有伴侣的朋友，要用心经营感情。',
      
      '健康': '健康是人生的基础。建议您要保持良好的生活习惯，注意饮食均衡，适当运动。同时要保持心情愉悦，避免过度焦虑和压力。定期体检也是必要的。',
      
      '运势': '运势如流水，有起有落。当前您的运势整体平稳，但需要注意把握机遇。建议您要保持积极乐观的心态，同时要脚踏实地，一步一个脚印地前进。'
    };

    // 根据关键词匹配回复
    for (const [keyword, response] of Object.entries(fallbackResponses)) {
      if (userMessage.includes(keyword)) {
        return response;
      }
    }

    // 默认回复
    return '感谢您的咨询。根据您的问题，我建议您要保持内心的平静和智慧，用心感悟生活中的每一个变化。无论遇到什么困难，都要相信自己的能力，同时要善于借助外界的力量。记住，命运掌握在自己手中，通过努力和智慧，一定能够创造美好的未来。如果您需要更详细的分析，建议您提供更具体的问题。';
  }

  // 预处理用户消息
  preprocessUserMessage(message) {
    // 过滤敏感词汇
    const sensitiveWords = ['政治', '暴力', '色情', '赌博', '毒品'];
    const lowerMessage = message.toLowerCase();
    
    for (const word of sensitiveWords) {
      if (lowerMessage.includes(word)) {
        return null; // 返回null表示消息不合适
      }
    }
    
    return message;
  }

  // 获取快捷问题的回复
  getQuickResponse(question) {
    const quickResponses = {
      '我的财运如何？': '财运方面，您目前正处于稳定期。建议您要把握好投资时机，避免盲目跟风。同时要注意开源节流，理性消费。在接下来的时间里，可以考虑一些稳健的投资方式，如基金定投等。',
      
      '感情运势怎样？': '感情运势方面，您最近会有不错的桃花运。如果您是单身，要主动参与社交活动，增加认识新朋友的机会。如果您已有伴侣，要多关心对方，用心经营感情。',
      
      '事业发展建议': '事业发展方面，您现在正处于上升期。建议您要保持学习的态度，不断提升自己的专业能力。同时要注意团队合作，处理好与同事的关系。在合适的时机，可以考虑寻求更好的发展机会。',
      
      '健康需要注意什么？': '健康方面，您需要注意劳逸结合，避免过度疲劳。建议您要保持规律的作息时间，注意饮食营养均衡。同时要适当运动，保持良好的心态。',
      
      '近期有什么需要注意的？': '近期您需要注意的是人际关系的处理。建议您要以诚待人，避免口舌是非。在工作中要细心谨慎，避免因小失大。同时要保持乐观的心态，相信好运即将到来。'
    };

    return quickResponses[question] || null;
  }

  // 检查消息是否为快捷问题
  isQuickQuestion(message) {
    const quickQuestions = [
      '我的财运如何？',
      '感情运势怎样？',
      '事业发展建议',
      '健康需要注意什么？',
      '近期有什么需要注意的？'
    ];
    
    return quickQuestions.includes(message);
  }

  // 添加流式消息处理
  async processMessageStream(userMessage, conversationHistory = [], onChunk) {
    try {
      // 预处理消息
      const processedMessage = this.preprocessUserMessage(userMessage);
      if (!processedMessage) {
        const errorMsg = '抱歉，我只能为您提供命理相关的咨询服务。';
        onChunk(errorMsg);
        return errorMsg;
      }

      // 检查是否为快捷问题
      if (this.isQuickQuestion(processedMessage)) {
        const quickResponse = this.getQuickResponse(processedMessage);
        onChunk(quickResponse);
        return quickResponse;
      }

      // 调用AI服务流式处理
      return await this.sendMessageStream(processedMessage, conversationHistory, onChunk);
    } catch (error) {
      this.logger.error('流式消息处理失败:', error);
      const errorMsg = '抱歉，服务暂时不可用，请稍后再试。';
      onChunk(errorMsg);
      return errorMsg;
    }
  }

  // 主要的消息处理入口
  async processMessage(userMessage, conversationHistory = []) {
    try {
      // 预处理消息
      const processedMessage = this.preprocessUserMessage(userMessage);
      if (!processedMessage) {
        return '抱歉，我只能为您提供命理相关的咨询服务。';
      }

      // 检查是否为快捷问题
      if (this.isQuickQuestion(processedMessage)) {
        return this.getQuickResponse(processedMessage);
      }

      // 调用AI服务
      return await this.sendMessage(processedMessage, conversationHistory);
    } catch (error) {
      this.logger.error('消息处理失败:', error);
      return '抱歉，服务暂时不可用，请稍后再试。';
    }
  }

  // 获取AI服务状态
  async getServiceStatus() {
    try {
      // 测试火山方舟API连接
      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        {
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: '测试连接'
            }
          ],
          max_tokens: 10,
          temperature: 0.1
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );
      
      return {
        status: 'online',
        model: this.modelName,
        timestamp: new Date().toISOString(),
        message: '后端服务运行正常，AI服务连接成功'
      };
    } catch (error) {
      this.logger.error('AI服务状态检查失败:', error);
      
      // 即使AI服务不可用，后端服务仍然在线
      return {
        status: 'online',
        model: this.modelName,
        timestamp: new Date().toISOString(),
        message: '后端服务运行正常，AI服务暂时不可用',
        aiError: error.message
      };
    }
  }

  // 生成今日运势
  async generateDailyFortune() {
    try {
      const today = new Date();
      const dateString = today.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
      
      const lunarDate = this.getLunarDate(today);
      
      const prompt = `作为专业的命理大师，请为${dateString}（${lunarDate}）生成今日运势。请包含以下内容：
1. 今日宜做的事情（3-4项）
2. 今日忌讳的事情（3-4项）
3. 整体运势简评（30-50字）
4. 幸运颜色和幸运数字
5. 财运、事业、感情、健康方面的简要提醒

请用温和、专业、富有智慧的语言，符合传统命理文化特色。回答要简洁明了，适合在手机应用中显示。`;

      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        {
          model: this.modelName,
          messages: [
            {
              role: 'system',
              content: '你是一位德高望重的命理大师，精通传统文化和命理学。你的运势预测准确而富有智慧，语言优雅温和，深受人们信赖。'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1000,
          temperature: 0.8,
          top_p: 0.9,
          frequency_penalty: 0.1,
          presence_penalty: 0.1,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const fortuneContent = response.data.choices[0].message.content;
        this.logger.info(`今日运势生成成功: ${dateString}`);
        
        return {
          date: dateString,
          lunarDate: lunarDate,
          content: fortuneContent,
          timestamp: today.toISOString(),
          isAIGenerated: true
        };
      } else {
        throw new Error('AI响应格式异常');
      }
    } catch (error) {
      this.logger.error('生成今日运势失败:', error);
      
      // 返回默认运势内容
      return this.getDefaultDailyFortune();
    }
  }

  // 获取默认今日运势（当AI服务不可用时）
  getDefaultDailyFortune() {
    const today = new Date();
    const dateString = today.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    });
    
    const lunarDate = this.getLunarDate(today);
    
    const defaultFortunes = [
      {
        content: `**今日运势概览**

**今日宜：**
• 祈福求财，拜访贵人
• 学习充电，提升技能
• 整理环境，净化心灵
• 与朋友聚会，增进感情

**今日忌：**
• 冲动投资，盲目决策
• 争执口角，意气用事
• 熬夜劳累，损伤精神
• 签订重要合同

**整体运势：** 今日运势平稳向上，适合稳中求进，把握机遇的同时要保持理性。

**幸运色彩：** 蓝色、白色
**幸运数字：** 3、7、9

**温馨提醒：**
💰 **财运：** 财运平稳，适合理财规划
🚀 **事业：** 工作顺利，团队合作佳
💕 **感情：** 感情和谐，沟通顺畅
🌱 **健康：** 注意休息，保持心情愉悦`,
        isAIGenerated: false
      },
      {
        content: `**今日运势概览**

**今日宜：**
• 读书学习，增长智慧
• 拜访长辈，孝敬父母
• 锻炼身体，强健体魄
• 慈善布施，积累福德

**今日忌：**
• 贪心投机，风险投资
• 情绪波动，心浮气躁
• 暴饮暴食，伤害脾胃
• 搬迁动土，改变格局

**整体运势：** 今日适合内省修心，通过学习和修养来提升自己，运势将逐步向好。

**幸运色彩：** 绿色、金色
**幸运数字：** 2、6、8

**温馨提醒：**
💰 **财运：** 收入稳定，避免投机
🚀 **事业：** 踏实工作，获得认可
💕 **感情：** 真诚待人，收获温暖
🌱 **健康：** 规律作息，适当运动`,
        isAIGenerated: false
      }
    ];
    
    const randomIndex = Math.floor(Math.random() * defaultFortunes.length);
    const selectedFortune = defaultFortunes[randomIndex];
    
    return {
      date: dateString,
      lunarDate: lunarDate,
      content: selectedFortune.content,
      timestamp: today.toISOString(),
      isAIGenerated: selectedFortune.isAIGenerated
    };
  }

  // 精确的农历转换（可以添加专业农历库，这里先优化算法）
  getLunarDate(date) {
    try {
      // 使用更精确的农历算法
      return this.calculateLunarDate(date);
    } catch (error) {
      console.error('农历转换失败:', error);
      // 使用备用方法
      return this.getFallbackLunarDate(date);
    }
  }

  // 更精确的农历计算方法
  calculateLunarDate(date) {
    const lunarMonths = [
      '正月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '冬月', '腊月'
    ];
    
    const lunarDays = [
      '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];
    
    // 获取基准日期的偏移量（相对于2000年春节）
    const baseDate = new Date(2000, 1, 5); // 2000年春节
    const diffDays = Math.floor((date - baseDate) / (1000 * 60 * 60 * 24));
    
    // 简化的农历计算（实际项目中建议使用专业的农历库）
    const lunarYear = 2000 + Math.floor(diffDays / 365);
    let monthIndex = Math.floor((diffDays % 365) / 30);
    let dayIndex = (diffDays % 30) + 1;
    
    // 调整边界值
    monthIndex = Math.max(0, Math.min(11, monthIndex));
    dayIndex = Math.max(1, Math.min(30, dayIndex));
    
    const lunarMonth = lunarMonths[monthIndex];
    const lunarDay = lunarDays[dayIndex];
    
    return `农历${lunarMonth}${lunarDay}`;
  }

  // 备用农历转换方法
  getFallbackLunarDate(date) {
    const lunarMonths = [
      '正月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '冬月', '腊月'
    ];
    
    const lunarDays = [
      '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];
    
    const month = date.getMonth();
    const day = date.getDate();
    
    const lunarMonth = lunarMonths[month % 12];
    const lunarDay = lunarDays[Math.min(day, 30)];
    
    return `农历${lunarMonth}${lunarDay}`;
  }

  // 获取星座+紫微组合分析
  async getConstellationZiweiAnalysis(birthDateTime, constellation) {
    try {
      console.log('\n🔮 开始调用火山方舟AI进行星座+紫微组合分析...');
      console.log(`📅 出生日期: ${birthDateTime}`);
      console.log(`⭐ 星座: ${constellation}`);
      
      // 构造更结构化的prompt，以获得更易解析的输出
      const prompt = `
请作为一位精通西方占星学和东方紫微斗数的命理专家，基于以下信息进行详细的星座+紫微组合分析：
- 用户星座: ${constellation}
- 出生日期时间: ${birthDateTime}

请提供一份非常详尽的分析报告，总体字数在1000字以上。每个分析领域（如事业、财运等）的内容至少需要100字，确保分析深入且有具体建议。

请使用以下严格的结构格式输出分析结果（务必包含所有章节标题和子标题）：

## 年运分析
总体概述：(至少100字，概述全年大势)
事业发展：(至少100字，详述职业机会、挑战及应对策略)
财运分析：(至少100字，分析收入来源、投资机会、理财建议)
感情状况：(至少100字，分析感情运势、相处之道、可能的变化)
健康状况：(至少100字，分析体能状况、易感疾病、养生之道)
人际关系：(至少100字，分析社交圈变化、人脉拓展、关系处理)
实用建议：(至少100字，提供具体可行的年度规划建议)

## 月运分析
本月总述：(至少100字，概述当月总体运势)
事业机遇：(至少100字，分析工作状况、机会与挑战)
财务状况：(至少100字，分析当月收支、投资机会)
感情运势：(至少100字，分析情感变化、处理方式)
健康提醒：(至少100字，分析健康隐患、调养方法)
人际互动：(至少100字，分析社交状况、人际变化)
实用建议：(至少100字，提供具体可行的月度规划建议)

## 日运分析
今日运势：(至少100字，概述当日整体状况)
工作表现：(至少100字，分析工作效率、重点事项)
财务情况：(至少100字，分析当日财运、消费建议)
感情互动：(至少100字，分析情感交流、相处建议)
健康状态：(至少100字，分析身体状况、注意事项)
人际交往：(至少100字，分析社交活动、人际关系)
实用建议：(至少100字，提供具体可执行的当日建议)

## 总结
(至少150字，对星座和紫微斗数组合分析的全面总结，包括两种体系相互验证的优势和综合建议)

分析要求：
1. 分析必须深入融合西方星座学和东方紫微斗数的精髓
2. 既要考虑${constellation}的性格特质，也要结合紫微斗数对命盘宫位和星曜的细节分析
3. 给出具体、实用且有针对性的建议，避免泛泛而谈
4. 结合具体时间节点提供精确指导
5. 保持专业性和权威性
6. 内容要丰富详实，避免重复，每个分析领域必须提供足够的信息量
7. 严格遵守上述格式和字数要求，确保分析全面而深入
8. 适当引用传统命理术语，增强专业感
`;

      // 调用AI模型
      const aiResponse = await this.sendMessage(prompt);
      console.log('✅ AI星座+紫微组合分析完成');
      
      // 解析AI返回的文本，转换为结构化数据
      const parseAIResponse = (text) => {
        try {
          // 使用正则表达式提取各部分内容
          const yearlyMatch = text.match(/##\s*年运分析\s*([\s\S]*?)(?=##\s*月运分析|$)/i);
          const monthlyMatch = text.match(/##\s*月运分析\s*([\s\S]*?)(?=##\s*日运分析|$)/i);
          const dailyMatch = text.match(/##\s*日运分析\s*([\s\S]*?)(?=##\s*总结|$)/i);
          const summaryMatch = text.match(/##\s*总结\s*([\s\S]*?)$/i);
          
          // 进一步解析年运内容
          const parseYearly = (content) => {
            if (!content) return {};
            
            const overview = content.match(/总体概述[：:]([\s\S]*?)(?=事业发展[：:]|$)/i);
            const career = content.match(/事业发展[：:]([\s\S]*?)(?=财运分析[：:]|$)/i);
            const money = content.match(/财运分析[：:]([\s\S]*?)(?=感情状况[：:]|$)/i);
            const love = content.match(/感情状况[：:]([\s\S]*?)(?=健康状况[：:]|$)/i);
            const health = content.match(/健康状况[：:]([\s\S]*?)(?=人际关系[：:]|$)/i);
            const social = content.match(/人际关系[：:]([\s\S]*?)(?=实用建议[：:]|$)/i);
            const advice = content.match(/实用建议[：:]([\s\S]*?)$/i);
            
            return {
              summary: overview ? overview[1].trim() : '暂无总体概述',
              career: career ? career[1].trim() : '暂无事业发展分析',
              money: money ? money[1].trim() : '暂无财运分析',
              love: love ? love[1].trim() : '暂无感情状况分析',
              health: health ? health[1].trim() : '暂无健康状况分析', 
              social: social ? social[1].trim() : '暂无人际关系分析',
              advice: advice ? advice[1].trim() : '暂无实用建议'
            };
          };
          
          // 解析月运内容
          const parseMonthly = (content) => {
            if (!content) return {};
            
            const overview = content.match(/本月总述[：:]([\s\S]*?)(?=事业机遇[：:]|$)/i);
            const career = content.match(/事业机遇[：:]([\s\S]*?)(?=财务状况[：:]|$)/i);
            const money = content.match(/财务状况[：:]([\s\S]*?)(?=感情运势[：:]|$)/i);
            const love = content.match(/感情运势[：:]([\s\S]*?)(?=健康提醒[：:]|$)/i);
            const health = content.match(/健康提醒[：:]([\s\S]*?)(?=人际互动[：:]|$)/i);
            const social = content.match(/人际互动[：:]([\s\S]*?)(?=实用建议[：:]|$)/i);
            const advice = content.match(/实用建议[：:]([\s\S]*?)$/i);
            
            return {
              summary: overview ? overview[1].trim() : '暂无月度总述',
              career: career ? career[1].trim() : '暂无事业机遇分析',
              money: money ? money[1].trim() : '暂无财务状况分析',
              love: love ? love[1].trim() : '暂无感情运势分析',
              health: health ? health[1].trim() : '暂无健康提醒',
              social: social ? social[1].trim() : '暂无人际互动分析',
              advice: advice ? advice[1].trim() : '暂无实用建议'
            };
          };
          
          // 解析日运内容
          const parseDaily = (content) => {
            if (!content) return {};
            
            const overview = content.match(/今日运势[：:]([\s\S]*?)(?=工作表现[：:]|$)/i);
            const career = content.match(/工作表现[：:]([\s\S]*?)(?=财务情况[：:]|$)/i);
            const money = content.match(/财务情况[：:]([\s\S]*?)(?=感情互动[：:]|$)/i);
            const love = content.match(/感情互动[：:]([\s\S]*?)(?=健康状态[：:]|$)/i);
            const health = content.match(/健康状态[：:]([\s\S]*?)(?=人际交往[：:]|$)/i);
            const social = content.match(/人际交往[：:]([\s\S]*?)(?=实用建议[：:]|$)/i);
            const advice = content.match(/实用建议[：:]([\s\S]*?)$/i);
            
            return {
              summary: overview ? overview[1].trim() : '暂无今日运势',
              career: career ? career[1].trim() : '暂无工作表现分析',
              money: money ? money[1].trim() : '暂无财务情况分析',
              love: love ? love[1].trim() : '暂无感情互动分析',
              health: health ? health[1].trim() : '暂无健康状态分析',
              social: social ? social[1].trim() : '暂无人际交往分析',
              advice: advice ? advice[1].trim() : '暂无实用建议'
            };
          };
          
          return {
            yearlyFortune: yearlyMatch ? parseYearly(yearlyMatch[1]) : { summary: '年运分析暂无数据' },
            monthlyFortune: monthlyMatch ? parseMonthly(monthlyMatch[1]) : { summary: '月运分析暂无数据' },
            dailyFortune: dailyMatch ? parseDaily(dailyMatch[1]) : { summary: '日运分析暂无数据' },
            summary: summaryMatch ? summaryMatch[1].trim() : '总结暂无数据',
            rawResponse: text, // 保存原始响应以备需要
            lastUpdated: new Date().toISOString()
          };
        } catch (parseError) {
          console.error('解析AI响应失败:', parseError);
          // 返回原始文本
          return {
            rawResponse: text,
            yearlyFortune: { summary: '解析失败，请查看原始响应' },
            monthlyFortune: { summary: '解析失败，请查看原始响应' },
            dailyFortune: { summary: '解析失败，请查看原始响应' },
            summary: '解析AI响应时出错',
            lastUpdated: new Date().toISOString()
          };
        }
      };
      
      const result = parseAIResponse(aiResponse);
      return result;
    } catch (error) {
      console.error('❌ 星座+紫微组合分析失败:', error);
      this.logger.error('星座+紫微组合分析失败:', error);
      throw error;
    }
  }
}

module.exports = AIService; 