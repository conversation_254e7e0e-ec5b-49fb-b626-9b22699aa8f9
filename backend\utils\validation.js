const Joi = require('joi');

class ValidationUtils {
  // 用户注册验证
  static validateRegister(data) {
    const schema = Joi.object({
      nickname: Joi.string().min(2).max(20).required().messages({
        'string.min': '昵称至少需要2个字符',
        'string.max': '昵称不能超过20个字符',
        'any.required': '昵称不能为空'
      }),
      password: Joi.string().min(6).max(50).required().messages({
        'string.min': '密码至少需要6个字符',
        'string.max': '密码不能超过50个字符',
        'any.required': '密码不能为空'
      })
    });

    return schema.validate(data);
  }

  // 用户登录验证
  static validateLogin(data) {
    const schema = Joi.object({
      nickname: Joi.string().required().messages({
        'any.required': '昵称不能为空'
      }),
      password: Joi.string().required().messages({
        'any.required': '密码不能为空'
      })
    });

    return schema.validate(data);
  }

  // 八字预测验证 - 支持AI解读
  static validateBaziPrediction(data) {
    const schema = Joi.object({
      gender: Joi.string().valid('male', 'female').required().messages({
        'any.only': '性别只能是male或female',
        'any.required': '性别不能为空'
      }),
      birthDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required().messages({
        'string.pattern.base': '出生日期格式必须为YYYY-MM-DD',
        'any.required': '出生日期不能为空'
      }),
      birthTime: Joi.string().pattern(/^\d{2}:\d{2}$/).required().messages({
        'string.pattern.base': '出生时间格式必须为HH:mm',
        'any.required': '出生时间不能为空'
      }),
      inputData: Joi.object({
        selectedTime: Joi.string().optional(),
        selectedHour: Joi.number().integer().min(1).max(12).optional()
      }).optional().messages({
        'object.base': '输入数据格式错误'
      })
    });

    return schema.validate(data);
  }

  // 六爻预测验证
  static validateLiuyaoPrediction(data) {
    const schema = Joi.object({
      question: Joi.string().min(5).max(200).required().messages({
        'string.min': '问题至少需要5个字符',
        'string.max': '问题不能超过200个字符',
        'any.required': '问题不能为空'
      }),
      guaLines: Joi.array().items(
        Joi.string().valid('阳', '阴')
      ).length(6).optional().messages({
        'array.length': '卦象必须包含6爻',
        'any.only': '爻线类型只能是阳或阴'
      }),
      timeInfo: Joi.object({
        year: Joi.number().integer().min(1900).max(2100),
        month: Joi.number().integer().min(1).max(12),
        day: Joi.number().integer().min(1).max(31),
        hour: Joi.number().integer().min(0).max(23),
        minute: Joi.number().integer().min(0).max(59),
        weekday: Joi.number().integer().min(0).max(6)
      }).optional(),
      divineTime: Joi.string().isoDate().optional().messages({
        'string.isoDate': '占卜时间必须是有效的ISO日期格式'
      })
    });

    return schema.validate(data);
  }

  // 紫微斗数预测验证
  static validateZiweiPrediction(data) {
    const schema = Joi.object({
      gender: Joi.string().valid('male', 'female').required(),
      birthDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
      birthTime: Joi.string().pattern(/^\d{2}:\d{2}$/).required()
    });

    return schema.validate(data);
  }

  // 组合预测验证
  static validateComboPrediction(data) {
    const schema = Joi.object({
      comboType: Joi.string().valid('bazi_ziwei', 'constellation_ziwei').required().messages({
        'any.only': '组合类型只能是bazi_ziwei或constellation_ziwei',
        'any.required': '组合类型不能为空'
      }),
      inputData: Joi.object({
        gender: Joi.string().valid('male', 'female').required(),
        birthDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
        birthTime: Joi.string().pattern(/^\d{2}:\d{2}$/).required()
      }).required()
    });

    return schema.validate(data);
  }

  // 聊天消息验证
  static validateChatMessage(data) {
    const schema = Joi.object({
      content: Joi.string().min(1).max(1000).required().messages({
        'string.min': '消息内容不能为空',
        'string.max': '消息内容不能超过1000个字符',
        'any.required': '消息内容不能为空'
      })
    });

    return schema.validate(data);
  }

  // 分页参数验证
  static validatePagination(data) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      type: Joi.string().valid('bazi', 'ziwei', 'liuyao', 'constellation', 'combo', 'ai').optional()
    });

    return schema.validate(data);
  }

  // 星座类型验证
  static validateConstellationType(type) {
    const validTypes = [
      'aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo',
      'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'
    ];
    
    return validTypes.includes(type);
  }

  // 星座周期验证
  static validateConstellationPeriod(period) {
    const validPeriods = ['today', 'week', 'month'];
    return validPeriods.includes(period);
  }

  // 通用错误响应格式化
  static formatValidationError(error) {
    return {
      error: {
        code: 400,
        message: error.details[0].message
      }
    };
  }
}

module.exports = ValidationUtils; 