const AV = require('leanengine');
const express = require('express');
const timeout = require('connect-timeout');
const path = require('path');
const cookieParser = require('cookie-parser');
const bodyParser = require('body-parser');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const schedule = require('node-schedule'); // 添加这一行

// 导入模型
const ConstellationFortune = require('./models/ConstellationFortune');

// 导入路由
const users = require('./routes/users');
const auth = require('./routes/auth');
const chat = require('./routes/chat');
const predictions = require('./routes/predictions');
const constellations = require('./routes/constellations');

// 设置定时任务
// 每天凌晨0点更新所有星座运势
schedule.scheduleJob('0 0 * * *', async function() {
  console.log('🕒 定时任务：开始更新今日星座运势...');
  try {
    const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
    if (!isUpdated) {
      await ConstellationFortune.updateAllDailyFortunes();
      console.log('✅ 定时任务：今日星座运势更新成功');
    } else {
      console.log('ℹ️ 定时任务：今日星座运势已更新，无需重复更新');
    }
  } catch (error) {
    console.error('❌ 定时任务：更新今日星座运势失败', error);
  }
});

// 应用启动时也检查一次
app.once('listening', async () => {
  console.log('🔍 应用启动：检查今日星座运势是否需要更新...');
  try {
    const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
    if (!isUpdated) {
      console.log('📅 应用启动：今日星座运势未更新，开始更新...');
      await ConstellationFortune.updateAllDailyFortunes();
      console.log('✅ 应用启动：今日星座运势更新成功');
    } else {
      console.log('ℹ️ 应用启动：今日星座运势已更新，无需重复更新');
    }
  } catch (error) {
    console.error('❌ 应用启动：更新今日星座运势失败', error);
  }
}); 