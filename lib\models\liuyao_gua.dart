enum GuaLineType {
  yangLine,  // 阳爻 (实线)
  yinLine,   // 阴爻 (虚线)
}

class GuaLine {
  final GuaLineType type;
  final int position; // 位置 (1-6, 从下往上)
  final bool isChanging; // 是否为变爻

  GuaLine({
    required this.type,
    required this.position,
    this.isChanging = false,
  });

  factory GuaLine.fromJson(Map<String, dynamic> json) {
    return GuaLine(
      type: GuaLineType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => GuaLineType.yangLine,
      ),
      position: json['position'],
      isChanging: json['isChanging'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'position': position,
      'isChanging': isChanging,
    };
  }
}

class LiuyaoGua {
  final String id;
  final String question;
  final List<GuaLine> lines;
  final String hexagramName;
  final String hexagramSymbol;
  final String interpretation;
  final String advice;
  final DateTime createdAt;

  LiuyaoGua({
    required this.id,
    required this.question,
    required this.lines,
    required this.hexagramName,
    required this.hexagramSymbol,
    required this.interpretation,
    required this.advice,
    required this.createdAt,
  });

  factory LiuyaoGua.fromJson(Map<String, dynamic> json) {
    return LiuyaoGua(
      id: json['id'],
      question: json['question'],
      lines: (json['lines'] as List)
          .map((line) => GuaLine.fromJson(line))
          .toList(),
      hexagramName: json['hexagramName'],
      hexagramSymbol: json['hexagramSymbol'],
      interpretation: json['interpretation'],
      advice: json['advice'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'lines': lines.map((line) => line.toJson()).toList(),
      'hexagramName': hexagramName,
      'hexagramSymbol': hexagramSymbol,
      'interpretation': interpretation,
      'advice': advice,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // 获取上卦 (第4、5、6爻)
  List<GuaLine> get upperTrigram {
    return lines.where((line) => line.position >= 4 && line.position <= 6).toList();
  }

  // 获取下卦 (第1、2、3爻)
  List<GuaLine> get lowerTrigram {
    return lines.where((line) => line.position >= 1 && line.position <= 3).toList();
  }

  // 获取变爻位置
  List<int> get changingPositions {
    return lines
        .where((line) => line.isChanging)
        .map((line) => line.position)
        .toList();
  }

  // 判断是否有变爻
  bool get hasChangingLines {
    return lines.any((line) => line.isChanging);
  }
}

// 预设的常见卦象
class CommonHexagrams {
  static const Map<String, Map<String, String>> hexagrams = {
    '乾': {
      'symbol': '☰',
      'name': '乾卦',
      'interpretation': '龙在天上，自强不息。事业上进，但需要谨慎行事。',
      'advice': '保持积极进取的态度，但要注意不要过于冒进。',
    },
    '坤': {
      'symbol': '☷',
      'name': '坤卦',
      'interpretation': '地德载物，厚德载物。宜守不宜攻，以柔制刚。',
      'advice': '保持谦逊的态度，以包容之心对待困难。',
    },
    '震': {
      'symbol': '☳',
      'name': '震卦',
      'interpretation': '雷声震动，万物复苏。有变化之象，宜顺应变化。',
      'advice': '准备迎接变化，保持灵活应变的能力。',
    },
    '巽': {
      'symbol': '☴',
      'name': '巽卦',
      'interpretation': '风行草偃，顺势而为。宜进不宜退，把握时机。',
      'advice': '顺应趋势，灵活调整策略。',
    },
    '坎': {
      'symbol': '☵',
      'name': '坎卦',
      'interpretation': '水流不息，险中求胜。虽有困难，但能克服。',
      'advice': '面对困难要坚持，如水一般柔韧不断。',
    },
    '离': {
      'symbol': '☲',
      'name': '离卦',
      'interpretation': '火光照耀，明智决策。事业有成，但需要保持清醒。',
      'advice': '保持理智，用智慧指导行动。',
    },
    '艮': {
      'symbol': '☶',
      'name': '艮卦',
      'interpretation': '山岳稳固，宜静不宜动。暂时停止，等待时机。',
      'advice': '耐心等待，保持稳定，不要轻举妄动。',
    },
    '兑': {
      'symbol': '☱',
      'name': '兑卦',
      'interpretation': '泽水相济，和谐共处。人际关系良好，合作顺利。',
      'advice': '保持和谐的人际关系，多与他人协作。',
    },
  };

  static Map<String, String>? getHexagramInfo(String name) {
    return hexagrams[name];
  }

  static List<String> get allNames => hexagrams.keys.toList();
} 