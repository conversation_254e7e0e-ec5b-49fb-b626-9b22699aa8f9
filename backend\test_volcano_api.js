const axios = require('axios');

async function testVolcanoAPI() {
  console.log('🔧 测试火山引擎API连接...');
  
  // 尝试不同的模型ID格式
  const modelIds = [
    'deepseek-v3-250324',
    'deepseek-chat',
    'deepseek-v3',
    'ep-20241230141133-2rjbz' // 常见的火山引擎endpoint格式
  ];
  
  for (const modelId of modelIds) {
    console.log(`\n🔍 测试模型ID: ${modelId}`);
    
    try {
      const response = await axios.post('https://ark.cn-beijing.volces.com/api/v3/chat/completions', {
        model: modelId,
        messages: [
          {
            role: 'user',
            content: '你好，请介绍一下六爻占卜的基本原理。'
          }
        ],
        temperature: 0.7,
        max_tokens: 300
      }, {
        headers: {
          'Authorization': `Bearer 2b823d35-99f4-4ca3-af93-8f3a3563606`,
          'Content-Type': 'application/json'
        },
        timeout: 15000
      });
      
      console.log(`✅ 模型 ${modelId} 调用成功！`);
      console.log('📝 模型响应:', response.data.choices[0].message.content);
      return; // 成功后退出
      
    } catch (error) {
      console.error(`❌ 模型 ${modelId} 调用失败:`, error.message);
      
      if (error.response) {
        console.error('📊 响应状态:', error.response.status);
        console.error('📋 响应数据:', JSON.stringify(error.response.data, null, 2));
      }
    }
  }
  
  console.log('\n🤔 建议检查：');
  console.log('1. API Key是否正确');
  console.log('2. 模型ID是否正确');
  console.log('3. 火山引擎账户是否有权限');
  console.log('4. 是否需要特殊的认证格式');
}

// 运行测试
testVolcanoAPI(); 