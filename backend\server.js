require('dotenv').config();
const AV = require('leanengine');
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
const schedule = require('node-schedule'); // 添加这一行
const path = require('path'); // 添加这一行

// 初始化LeanCloud
const { appId, appKey, masterKey } = require('./.leancloud/config.json');
AV.init({
  appId,
  appKey,
  masterKey,
  serverURL: 'https://4ahhqofx.lc-cn-n1-shared.com',
});

// 配置日志
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
});

// 创建Express应用
const app = express();

// 导入模型
const ConstellationFortune = require('./models/ConstellationFortune');

// 安全中间件
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: [
    'http://localhost:8080',   // 前端Flutter Web (固定)
    'http://127.0.0.1:8080',   // 备用地址
    'http://localhost:3000',   // 后端自身
    'http://localhost:3001',   // 备用端口
    'http://localhost:3002',   // 备用端口
    'https://your-frontend-domain.com'
  ],
  credentials: true
}));

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP每15分钟最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use('/api/', limiter);

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 提供静态文件
app.use(express.static(path.join(__dirname, 'public')));

// 启用LeanEngine中间件
app.use(AV.express());

// 请求日志中间件 - 便于调试前后端连接
app.use((req, res, next) => {
  const timestamp = new Date().toLocaleString();
  logger.info(`📨 [${timestamp}] ${req.method} ${req.url} - Origin: ${req.headers.origin || 'N/A'}`);
  console.log(`📨 [${timestamp}] ${req.method} ${req.url} - Origin: ${req.headers.origin || 'N/A'}`);
  next();
});

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const predictionRoutes = require('./routes/predictions');
const constellationRoutes = require('./routes/constellations');
const chatRoutes = require('./routes/chat');

// 使用路由
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/predictions', predictionRoutes);
app.use('/api/v1/constellations', constellationRoutes);
app.use('/api/v1/chat', chatRoutes);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  logger.error(err.stack);
  res.status(err.status || 500).json({
    error: {
      code: err.status || 500,
      message: err.message || '服务器内部错误'
    }
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: {
      code: 404,
      message: '请求的资源不存在'
    }
  });
});

// 设置星座运势定时更新任务
// 每天凌晨0点更新所有星座运势
schedule.scheduleJob('0 0 * * *', async function() {
  console.log('🕒 定时任务：开始更新今日星座运势...');
  logger.info('定时任务：开始更新今日星座运势');
  
  try {
    const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
    if (!isUpdated) {
      await ConstellationFortune.updateAllDailyFortunes();
      console.log('✅ 定时任务：今日星座运势更新成功');
      logger.info('定时任务：今日星座运势更新成功');
    } else {
      console.log('ℹ️ 定时任务：今日星座运势已更新，无需重复更新');
      logger.info('定时任务：今日星座运势已更新，无需重复更新');
    }
  } catch (error) {
    console.error('❌ 定时任务：更新今日星座运势失败', error);
    logger.error('定时任务：更新今日星座运势失败', error);
  }
});

// 启动服务器
const PORT = process.env.LEANCLOUD_APP_PORT || 3000;
app.listen(PORT, () => {
  console.log('\n🚀 ===== 银发-满天神佛后端服务启动 =====');
  console.log(`🔧 端口: ${PORT}`);
  console.log(`🌐 API接口地址: http://localhost:${PORT}/api/v1/`);
  console.log(`❤️ 健康检查: http://localhost:${PORT}/health`);
  console.log(`📡 CORS允许来源:`, process.env.CORS_ORIGIN || 'http://localhost:8080,http://localhost:3000');
  console.log('🎉 ===== 服务启动完成，等待请求... =====\n');
  
  logger.info(`银发-满天神佛后端服务已启动，端口: ${PORT}`);
  logger.info(`API接口地址: http://localhost:${PORT}/api/v1/`);
  logger.info(`健康检查: http://localhost:${PORT}/health`);
  
  // 应用启动时检查今日星座运势是否需要更新
  console.log('🔍 应用启动：检查今日星座运势是否需要更新...');
  logger.info('应用启动：检查今日星座运势是否需要更新');
  
  setTimeout(async () => {
    try {
      const isUpdated = await ConstellationFortune.checkTodayFortunesUpdated();
      if (!isUpdated) {
        console.log('📅 应用启动：今日星座运势未更新，开始更新...');
        logger.info('应用启动：今日星座运势未更新，开始更新');
        
        await ConstellationFortune.updateAllDailyFortunes();
        console.log('✅ 应用启动：今日星座运势更新成功');
        logger.info('应用启动：今日星座运势更新成功');
      } else {
        console.log('ℹ️ 应用启动：今日星座运势已更新，无需重复更新');
        logger.info('应用启动：今日星座运势已更新，无需重复更新');
      }
    } catch (error) {
      console.error('❌ 应用启动：更新今日星座运势失败', error);
      logger.error('应用启动：更新今日星座运势失败', error);
    }
  }, 5000); // 延迟5秒执行，确保应用完全启动
});

module.exports = app; 